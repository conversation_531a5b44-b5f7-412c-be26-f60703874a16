{"permissions": {"allow": ["Bash(git push:*)", "Bash(grep:*)", "<PERSON><PERSON>(chmod:*)", "Bash(git add:*)", "Bash(rm:*)", "WebFetch(domain:www.writer777.com)", "Bash(git commit:*)", "Bash(rg:*)", "<PERSON><PERSON>(sudo:*)", "Bash(psql:*)", "Bash(npm install)", "Bash(npm run test-postgres:*)", "Bash(npm run:*)", "Bash(npm start)", "Bash(node:*)", "Bash(PGPASSWORD=teQKTkwGHUCpIZUCNExWLjXbPOZcBvco psql -h shuttle.proxy.rlwy.net -p 21666 -U postgres -d railway -c \"\\d tasks\")", "Bash(PGPASSWORD=teQKTkwGHUCpIZUCNExWLjXbPOZcBvco psql -h shuttle.proxy.rlwy.net -p 21666 -U postgres -d railway -c \"\\d user_presets\")"], "deny": []}}