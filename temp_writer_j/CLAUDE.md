# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Writer J 是一个 AI 驱动的文章生成平台，通过 7 步工作流程帮助用户创建高质量、SEO 友好的文章。

## 项目结构

```
writer-j/
├── j-writer/
│   ├── ai-article-generator/    # 前端 React 应用
│   └── backend/                 # 后端 Express API
```

## 开发命令

### 前端开发 (在 j-writer/ai-article-generator/ 目录)
```bash
npm install          # 安装依赖
npm run dev         # 启动开发服务器 (http://localhost:5173)
npm run build       # 构建生产版本
npm run preview     # 预览生产版本
```

### 后端开发 (在 j-writer/backend/ 目录)
```bash
npm install         # 安装依赖
npm run dev        # 启动开发服务器 (http://localhost:3001)
npm start          # 启动生产服务器
npm run init-db    # 初始化数据库
npm run migrate    # 运行数据库迁移
```

## 技术架构

### 前端技术栈
- React 19.1.0 + Vite
- Tailwind CSS
- React Router
- Headless UI + Heroicons

### 后端技术栈
- Node.js + Express.js
- 数据库: PostgreSQL (生产) / SQLite (开发)
- AI 服务: Google Gemini API, DeepSeek, OpenAI
- 认证: JWT + bcrypt
- 搜索 API: Serper API

### 核心功能模块

1. **7 步文章生成工作流程**:
   - Step 1: 关键词研究
   - Step 2: 主题选择
   - Step 3: 资源整合
   - Step 4: 产品整合(可选)
   - Step 5: E-E-A-T 权威档案
   - Step 6: 风格与格式参数
   - Step 7: 文章生成与优化

2. **用户系统**:
   - 认证路由: `/api/auth/*`
   - 用户管理: 支持用户和管理员角色
   - 会话管理: 基于 JWT

3. **任务管理**:
   - 任务 API: `/api/tasks/*`
   - 支持保存草稿和断点续写
   - 预设管理: 保存常用配置

4. **管理员功能**:
   - 用户管理: `/api/admin/users/*`
   - AI 模型配置: `/api/admin/ai-models/*`
   - 提示词模板: `/api/admin/prompt-templates/*`

## 数据库结构

主要数据表:
- `users` - 用户账户信息
- `tasks` - 文章生成任务
- `user_presets` - 用户预设配置
- `blog_posts` - 生成的博客文章
- `ai_models` - AI 模型配置
- `prompt_templates` - 提示词模板

## API 配置

前端 API 配置文件: `j-writer/ai-article-generator/src/config/api.js`
- 开发环境: http://localhost:3001
- 生产环境: 通过环境变量 VITE_API_URL 配置

## 环境变量

后端需要的环境变量:
- `DATABASE_URL` - PostgreSQL 连接字符串
- `JWT_SECRET` - JWT 密钥
- `GOOGLE_API_KEY` - Google Gemini API 密钥
- `SERPER_API_KEY` - Serper 搜索 API 密钥
- `DEEPSEEK_API_KEY` - DeepSeek API 密钥 (可选)
- `OPENAI_API_KEY` - OpenAI API 密钥 (可选)

## 部署说明

项目支持多种部署方式:
- 前端: Vercel 部署
- 后端: Railway 或 Docker 容器部署
- 数据库: Railway PostgreSQL 或自托管

## 注意事项

1. 项目目前没有配置代码检查工具(ESLint/Prettier)和测试框架
2. 开发时前端和后端需要分别启动
3. 生产环境使用 PostgreSQL，开发环境可使用 SQLite
4. AI 服务需要相应的 API 密钥才能正常工作