import { useState, useEffect, useRef } from 'react';
import { apiCall, API_CONFIG } from '../config/api';

// Custom hook for autocomplete functionality
export const useAutocomplete = (debounceMs = 300) => {
  const [suggestions, setSuggestions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const debounceRef = useRef(null);
  const abortControllerRef = useRef(null);

  const fetchSuggestions = async (query) => {
    if (!query || query.trim().length < 2) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setIsLoading(true);
    try {
      const url = `${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.IDEATION_AUTOCOMPLETE}?q=${encodeURIComponent(query.trim())}`;
      const apiKey = import.meta.env.VITE_API_KEY;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'X-API-Key': apiKey || '',
        },
        signal: abortControllerRef.current.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.suggestions && Array.isArray(data.suggestions)) {
        setSuggestions(data.suggestions);
        setShowSuggestions(data.suggestions.length > 0);
        setSelectedIndex(-1);
      }
    } catch (error) {
      if (error.name !== 'AbortError') {
        console.error('Error fetching autocomplete suggestions:', error);
        setSuggestions([]);
        setShowSuggestions(false);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const debouncedFetchSuggestions = (query) => {
    // Clear previous timeout
    if (debounceRef.current) {
      clearTimeout(debounceRef.current);
    }

    // Set new timeout
    debounceRef.current = setTimeout(() => {
      fetchSuggestions(query);
    }, debounceMs);
  };

  const hideSuggestions = () => {
    setShowSuggestions(false);
    setSelectedIndex(-1);
  };

  const selectSuggestion = (suggestion) => {
    hideSuggestions();
    return suggestion;
  };

  const handleKeyDown = (e, inputValue, onSelect) => {
    if (!showSuggestions || suggestions.length === 0) return false;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        return true;

      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev =>
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        return true;

      case 'Enter':
        if (selectedIndex >= 0 && selectedIndex < suggestions.length) {
          e.preventDefault();
          const selected = suggestions[selectedIndex];
          onSelect(selectSuggestion(selected));
          return true;
        }
        break;

      case 'Escape':
        e.preventDefault();
        hideSuggestions();
        return true;

      default:
        return false;
    }
    return false;
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    suggestions,
    isLoading,
    showSuggestions,
    selectedIndex,
    debouncedFetchSuggestions,
    hideSuggestions,
    selectSuggestion,
    handleKeyDown
  };
};
