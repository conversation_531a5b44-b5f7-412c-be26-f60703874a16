import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  PlusIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  SparklesIcon,
  UserIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../config/api';
import WriterJLogo from './WriterJLogo';

const Dashboard = () => {
  const { user, authenticatedFetch } = useAuth();
  const [tasks, setTasks] = useState([]);
  const [stats, setStats] = useState({ totalTasks: 0, statusBreakdown: [] });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch recent tasks and stats
        const [tasksResponse, statsResponse] = await Promise.all([
          authenticatedFetch(`${API_CONFIG.ENDPOINTS.TASKS.LIST}?limit=5`),
          authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.STATS)
        ]);

        if (tasksResponse.ok) {
          const tasksData = await tasksResponse.json();
          setTasks(tasksData.tasks);
        }

        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [authenticatedFetch]);

  const getStatusIcon = (status) => {
    if (status.includes('Completed')) {
      return <CheckCircleIcon className="w-5 h-5 text-green-500" />;
    } else if (status.includes('Generating')) {
      return <ClockIcon className="w-5 h-5 text-yellow-500 animate-spin" />;
    } else {
      return <DocumentTextIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  const getStatusColor = (status) => {
    if (status.includes('Completed')) return 'bg-green-100 text-green-800';
    if (status.includes('Generating')) return 'bg-yellow-100 text-yellow-800';
    if (status.includes('Review')) return 'bg-purple-100 text-purple-800';
    return 'bg-blue-100 text-blue-800';
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg shadow-blue-500/5">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <WriterJLogo className="w-12 h-12" showText={false} />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Writer J</h1>
                <p className="text-sm text-gray-600">AI Article Generator</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link
                to="/profile"
                className="flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-700 hover:bg-white/50 transition-colors"
              >
                <UserIcon className="w-5 h-5" />
                <span className="hidden sm:block">{user?.fullName || user?.email}</span>
              </Link>
              <Link
                to="/settings"
                className="p-2 rounded-lg text-gray-700 hover:bg-white/50 transition-colors"
              >
                <Cog6ToothIcon className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Welcome back, {user?.fullName || 'Writer'}!
          </h2>
          <p className="text-gray-600">
            Ready to create amazing content? Start a new article task or continue where you left off.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Link
            to="/tasks/new"
            className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 text-white hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            <div className="flex items-center space-x-3">
              <PlusIcon className="w-8 h-8" />
              <div>
                <h3 className="text-lg font-semibold">Start New Article</h3>
                <p className="text-blue-100">Begin with keyword research</p>
              </div>
            </div>
          </Link>

          <Link
            to="/tasks"
            className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
          >
            <div className="flex items-center space-x-3">
              <DocumentTextIcon className="w-8 h-8 text-gray-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">My Tasks</h3>
                <p className="text-gray-600">View and manage all tasks</p>
              </div>
            </div>
          </Link>

          <Link
            to="/generator"
            className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
          >
            <div className="flex items-center space-x-3">
              <SparklesIcon className="w-8 h-8 text-purple-600" />
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Quick Generator</h3>
                <p className="text-gray-600">Generate without saving</p>
              </div>
            </div>
          </Link>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl p-6 shadow-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Tasks</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalTasks}</p>
              </div>
              <DocumentTextIcon className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          {stats.statusBreakdown.slice(0, 3).map((stat, index) => (
            <div key={stat.status} className="bg-white rounded-xl p-6 shadow-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.status}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.count}</p>
                </div>
                {getStatusIcon(stat.status)}
              </div>
            </div>
          ))}
        </div>

        {/* Recent Tasks */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Recent Tasks</h3>
              <Link
                to="/tasks"
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                View all
              </Link>
            </div>
          </div>

          {tasks.length === 0 ? (
            <div className="px-6 py-12 text-center">
              <DocumentTextIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">No articles yet</h4>
              <p className="text-gray-600 mb-4">Start your first article creation process</p>
              <Link
                to="/tasks/new"
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <PlusIcon className="w-4 h-4 mr-2" />
                Start Article
              </Link>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {tasks.map((task) => (
                <Link
                  key={task.id}
                  to={`/tasks/${task.id}`}
                  className="block px-6 py-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(task.status)}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900">{task.name}</h4>
                        <p className="text-xs text-gray-500">
                          Updated {new Date(task.updated_at).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                      {task.status}
                    </span>
                  </div>
                </Link>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default Dashboard;
