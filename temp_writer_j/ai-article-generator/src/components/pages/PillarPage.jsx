import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import {
  CalendarIcon,
  ClockIcon,
  UserIcon,
  TagIcon,
  ArrowLeftIcon,
  ShareIcon,
  BookmarkIcon,
  ChevronRightIcon,
  ArrowTopRightOnSquareIcon
} from '@heroicons/react/24/outline';
import WriterJLogo from '../WriterJLogo';
import { apiCall } from '../../config/api';

const PillarPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [pillarPage, setPillarPage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPillarPage();
  }, [slug]);

  const loadPillarPage = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiCall(`/api/pillar-pages/slug/${slug}`);
      setPillarPage(response);
    } catch (error) {
      console.error('Error loading pillar page:', error);
      if (error.status === 404) {
        setError('Page not found');
      } else {
        setError('Failed to load page');
      }
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const shareArticle = () => {
    if (navigator.share) {
      navigator.share({
        title: pillarPage.title,
        text: pillarPage.excerpt,
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const renderMarkdown = (content) => {
    // Basic markdown rendering
    return content
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold text-gray-900 mb-6 mt-8">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold text-gray-900 mb-4 mt-6">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-bold text-gray-900 mb-3 mt-4">$1</h3>')
      .replace(/\*\*(.*)\*\*/gim, '<strong class="font-semibold">$1</strong>')
      .replace(/\*(.*)\*/gim, '<em class="italic">$1</em>')
      .replace(/^\- (.*$)/gim, '<li class="ml-4">$1</li>')
      .replace(/^(\d+)\. (.*$)/gim, '<li class="ml-4">$2</li>')
      .replace(/\n\n/gim, '</p><p class="text-gray-700 leading-relaxed mb-4">')
      .replace(/^(?!<[h|l|p])/gim, '<p class="text-gray-700 leading-relaxed mb-4">')
      .replace(/$/gim, '</p>');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-600 mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">{error}</h1>
          <button
            onClick={() => navigate('/blog')}
            className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Blog
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <Link to="/home" className="flex items-center space-x-3">
              <WriterJLogo className="w-10 h-10" showText={true} textClassName="text-2xl font-bold text-gray-900" />
            </Link>
            
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/features" className="text-gray-700 hover:text-blue-600 transition-colors">Features</Link>
              <Link to="/pricing" className="text-gray-700 hover:text-blue-600 transition-colors">Pricing</Link>
              <Link to="/blog" className="text-gray-700 hover:text-blue-600 transition-colors">Blog</Link>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-700 hover:text-blue-600 transition-colors"
              >
                Sign In
              </Link>
              <Link
                to="/register"
                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Try Free
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Back Navigation */}
        <Link
          to="/blog"
          className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-8 group"
        >
          <ArrowLeftIcon className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform" />
          Back to Blog
        </Link>

        {/* Pillar Page Content */}
        <article className="bg-white rounded-2xl shadow-xl overflow-hidden">
          {pillarPage.cover_image && (
            <div className="aspect-video overflow-hidden">
              <img
                src={pillarPage.cover_image}
                alt={pillarPage.title}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          <div className="p-8 md:p-12">
            {/* Breadcrumb */}
            <div className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
              <Link to="/blog" className="hover:text-blue-600">Blog</Link>
              <ChevronRightIcon className="w-4 h-4" />
              <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">
                Pillar Guide
              </span>
            </div>

            {/* Meta Information */}
            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-6">
              <div className="flex items-center space-x-1">
                <CalendarIcon className="w-4 h-4" />
                <span>{formatDate(pillarPage.created_at)}</span>
              </div>
              {pillarPage.author && (
                <div className="flex items-center space-x-1">
                  <UserIcon className="w-4 h-4" />
                  <span>{pillarPage.author}</span>
                </div>
              )}
              {pillarPage.featured && (
                <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs font-medium">
                  Featured Guide
                </span>
              )}
            </div>

            {/* Title */}
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              {pillarPage.title}
            </h1>

            {/* Excerpt */}
            {pillarPage.excerpt && (
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {pillarPage.excerpt}
              </p>
            )}

            {/* Category and Actions */}
            <div className="flex flex-wrap items-center justify-between gap-4 mb-8 pb-8 border-b border-gray-200">
              <div className="flex items-center gap-2">
                {pillarPage.category && (
                  <span className="bg-purple-100 text-purple-800 px-3 py-2 rounded-full text-sm font-medium">
                    {pillarPage.category.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={shareArticle}
                  className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                  title="Share guide"
                >
                  <ShareIcon className="w-5 h-5" />
                  <span className="hidden sm:inline">Share</span>
                </button>
                <button
                  className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors"
                  title="Bookmark guide"
                >
                  <BookmarkIcon className="w-5 h-5" />
                  <span className="hidden sm:inline">Save</span>
                </button>
              </div>
            </div>

            {/* Main Content */}
            <div 
              className="prose prose-lg max-w-none mb-12"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(pillarPage.content || '') }}
            />
          </div>
        </article>

        {/* Dive Deeper Section - Cluster Pages */}
        {pillarPage.clusterPages && pillarPage.clusterPages.length > 0 && (
          <section className="mt-16">
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl p-8 md:p-12 text-white">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold mb-4">Dive Deeper</h2>
                <p className="text-purple-100 text-lg">
                  Explore specific topics and practical applications from this guide
                </p>
              </div>

              {pillarPage.clusterPages.length <= 3 ? (
                /* Card Layout for 3 or fewer articles */
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {pillarPage.clusterPages.map((clusterPage) => (
                    <Link
                      key={clusterPage.id}
                      to={`/blog/post/${clusterPage.slug}`}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all duration-200 group border border-white/20"
                    >
                      <div className="flex items-start justify-between mb-4">
                        <div className="bg-white/20 rounded-lg p-2">
                          <ArrowTopRightOnSquareIcon className="w-5 h-5" />
                        </div>
                        <div className="text-right text-sm text-purple-200">
                          <div className="flex items-center space-x-1">
                            <ClockIcon className="w-3 h-3" />
                            <span>{clusterPage.read_time} min</span>
                          </div>
                        </div>
                      </div>
                      
                      <h3 className="text-lg font-bold mb-3 group-hover:text-purple-200 transition-colors">
                        {clusterPage.customTitle || clusterPage.title}
                      </h3>
                      
                      <p className="text-purple-100 text-sm line-clamp-3">
                        {clusterPage.customDescription || clusterPage.excerpt}
                      </p>

                      <div className="mt-4 pt-4 border-t border-white/20">
                        <div className="text-xs text-purple-200">
                          {formatDate(clusterPage.created_at)}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                /* List Layout for more than 3 articles */
                <div className="space-y-4">
                  {pillarPage.clusterPages.map((clusterPage, index) => (
                    <Link
                      key={clusterPage.id}
                      to={`/blog/post/${clusterPage.slug}`}
                      className="bg-white/10 backdrop-blur-sm rounded-xl p-6 hover:bg-white/20 transition-all duration-200 group border border-white/20 flex items-center justify-between"
                    >
                      <div className="flex items-start space-x-4 flex-1">
                        <div className="bg-white/20 rounded-lg p-2 mt-1">
                          <span className="text-sm font-bold">{index + 1}</span>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-bold mb-2 group-hover:text-purple-200 transition-colors">
                            {clusterPage.customTitle || clusterPage.title}
                          </h3>
                          <p className="text-purple-100 text-sm line-clamp-2">
                            {clusterPage.customDescription || clusterPage.excerpt}
                          </p>
                          <div className="flex items-center space-x-4 mt-3 text-xs text-purple-200">
                            <div className="flex items-center space-x-1">
                              <ClockIcon className="w-3 h-3" />
                              <span>{clusterPage.read_time} min read</span>
                            </div>
                            <span>{formatDate(clusterPage.created_at)}</span>
                          </div>
                        </div>
                      </div>
                      <ChevronRightIcon className="w-5 h-5 text-white/60 group-hover:text-white group-hover:translate-x-1 transition-all" />
                    </Link>
                  ))}
                </div>
              )}
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="mt-16 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-8 md:p-12 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Put This into Practice?
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Use Writer J's 7-step process to create your first article based on what you've learned.
          </p>
          <Link
            to="/register"
            className="inline-flex items-center px-8 py-4 bg-white text-blue-600 text-lg font-semibold rounded-xl hover:bg-gray-50 transition-all duration-200 shadow-xl hover:shadow-2xl transform hover:-translate-y-1"
          >
            Start Writing Free
          </Link>
        </section>
      </div>
    </div>
  );
};

export default PillarPage;