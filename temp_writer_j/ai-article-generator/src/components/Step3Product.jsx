import React, { useState } from 'react';
import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';
import PresetSelector from './PresetSelector';
import PresetSaveModal from './PresetSaveModal';

const Step3Product = ({ data, updateData }) => {
  const [featureInput, setFeatureInput] = useState('');
  const [showSaveModal, setShowSaveModal] = useState(false);

  const updateProductInfo = (field, value) => {
    updateData({
      productInfo: {
        ...data.productInfo,
        [field]: value
      }
    });
  };

  const addFeature = () => {
    if (featureInput.trim() && data.productInfo.features.length < 6) {
      updateProductInfo('features', [...data.productInfo.features, featureInput.trim()]);
      setFeatureInput('');
    }
  };

  const removeFeature = (index) => {
    const newFeatures = data.productInfo.features.filter((_, i) => i !== index);
    updateProductInfo('features', newFeatures);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      addFeature();
    }
  };

  const handlePresetSelect = (presetData) => {
    updateData({
      productInfo: {
        name: presetData.name || '',
        link: presetData.link || '',
        description: presetData.description || '',
        features: presetData.features || []
      }
    });
  };

  const handleSavePreset = () => {
    setShowSaveModal(true);
  };

  const handlePresetSaved = () => {
    // Optionally refresh presets or show success message
    setShowSaveModal(false);
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Integrate Your Product</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step 4 of 7:</span> Optionally integrate a product or service into your article.
          If you have a product to promote, provide the details below and the AI will naturally weave this information
          into your content where relevant and appropriate.
        </p>
      </div>

      {/* Preset Selector */}
      <PresetSelector
        presetType="product"
        onPresetSelect={handlePresetSelect}
        onSavePreset={handleSavePreset}
        currentData={data.productInfo}
      />

      {/* Product Name */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Product/Service Name
        </label>
        <input
          type="text"
          value={data.productInfo.name}
          onChange={(e) => updateProductInfo('name', e.target.value)}
          placeholder="Enter product or service name"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Product Link */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Product Link (Optional)
        </label>
        <input
          type="url"
          value={data.productInfo.link}
          onChange={(e) => updateProductInfo('link', e.target.value)}
          placeholder="https://example.com/product"
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Product Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Simple Description
        </label>
        <textarea
          value={data.productInfo.description}
          onChange={(e) => updateProductInfo('description', e.target.value)}
          placeholder="Provide a brief description of what your product/service does..."
          rows={3}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
      </div>

      {/* Features/Benefits */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Features/Benefits (Up to 6)
        </label>
        <p className="text-sm text-gray-600 mb-3">
          Add key features or benefits that make your product/service valuable.
        </p>

        <div className="flex gap-2 mb-4">
          <input
            type="text"
            value={featureInput}
            onChange={(e) => setFeatureInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter a feature or benefit..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={data.productInfo.features.length >= 6}
          />
          <button
            onClick={addFeature}
            disabled={!featureInput.trim() || data.productInfo.features.length >= 6}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            <PlusIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Features Display */}
        {data.productInfo.features.length > 0 && (
          <div className="space-y-2 mb-4">
            {data.productInfo.features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md"
              >
                <span className="text-sm text-gray-700">{feature}</span>
                <button
                  onClick={() => removeFeature(index)}
                  className="text-red-600 hover:text-red-800"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Features Limit Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-sm text-blue-700">
            <strong>Features: {data.productInfo.features.length}/6</strong>
            {data.productInfo.features.length >= 6 && ' (Maximum reached)'}
          </p>
        </div>
      </div>

      {/* Product Preview */}
      {(data.productInfo.name || data.productInfo.description || data.productInfo.features.length > 0) && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="text-md font-medium text-green-800 mb-3">Product Preview</h4>

          {data.productInfo.name && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Name: </span>
              <span className="text-sm text-green-600">{data.productInfo.name}</span>
            </div>
          )}

          {data.productInfo.link && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Link: </span>
              <a href={data.productInfo.link} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-600 hover:underline">
                {data.productInfo.link}
              </a>
            </div>
          )}

          {data.productInfo.description && (
            <div className="mb-2">
              <span className="text-sm font-medium text-green-700">Description: </span>
              <span className="text-sm text-green-600">{data.productInfo.description}</span>
            </div>
          )}

          {data.productInfo.features.length > 0 && (
            <div>
              <span className="text-sm font-medium text-green-700">Features: </span>
              <ul className="text-sm text-green-600 list-disc list-inside mt-1">
                {data.productInfo.features.map((feature, index) => (
                  <li key={index}>{feature}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Save Preset Modal */}
      <PresetSaveModal
        isOpen={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        presetType="product"
        presetData={data.productInfo}
        onPresetSaved={handlePresetSaved}
      />
    </div>
  );
};

export default Step3Product;
