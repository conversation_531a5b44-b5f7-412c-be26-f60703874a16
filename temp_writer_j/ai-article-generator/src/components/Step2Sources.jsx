import React, { useState } from 'react';
import { PlusIcon, XMarkIcon, LinkIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';

const Step2Sources = ({ data, updateData }) => {
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);

  const addUrlSource = async () => {
    if (!urlInput.trim()) return;

    setIsExtracting(true);
    try {
      const extractedData = await apiCall(API_CONFIG.ENDPOINTS.SOURCES_EXTRACT, {
        method: 'POST',
        body: JSON.stringify({ url: urlInput.trim() }),
      });

      updateData({
        sources: [...data.sources, {
          type: 'url',
          url: urlInput.trim(),
          title: extractedData.title,
          content: extractedData.content,
          description: extractedData.description,
          wordCount: extractedData.wordCount
        }]
      });

      setUrlInput('');
    } catch (error) {
      console.error('Error extracting URL content:', error);
      alert('Failed to extract content from URL. Please check the URL and try again.');
    } finally {
      setIsExtracting(false);
    }
  };

  const addTextSource = () => {
    if (!textInput.trim()) return;

    const wordCount = textInput.trim().split(/\s+/).length;

    updateData({
      sources: [...data.sources, {
        type: 'text',
        title: 'Custom Text Block',
        content: textInput.trim(),
        wordCount: wordCount
      }]
    });

    setTextInput('');
  };

  const removeSource = (index) => {
    updateData({
      sources: data.sources.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Source Integration</h3>
        <p className="text-sm text-gray-600 mb-6">
          Add up to 3 sources to provide context and information for your article. You can add URLs for automatic content extraction or paste text blocks directly.
        </p>
      </div>

      {/* URL Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <LinkIcon className="w-4 h-4 inline mr-1" />
          Add URL Source
        </label>
        <div className="flex gap-2 mb-4">
          <input
            type="url"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example.com/article"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={data.sources.length >= 3}
          />
          <button
            onClick={addUrlSource}
            disabled={!urlInput.trim() || data.sources.length >= 3 || isExtracting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isExtracting ? 'Extracting...' : <PlusIcon className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Text Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <DocumentTextIcon className="w-4 h-4 inline mr-1" />
          Add Text Block
        </label>
        <textarea
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          placeholder="Paste your research notes, product descriptions, or any relevant text here..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={data.sources.length >= 3}
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-sm text-gray-500">
            {textInput.trim() ? `${textInput.trim().split(/\s+/).length} words` : ''}
          </span>
          <button
            onClick={addTextSource}
            disabled={!textInput.trim() || data.sources.length >= 3}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Add Text Block
          </button>
        </div>
      </div>

      {/* Sources Limit Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <p className="text-sm text-blue-700">
          <strong>Sources: {data.sources.length}/3</strong>
          {data.sources.length >= 3 && ' (Maximum reached)'}
        </p>
      </div>

      {/* Added Sources Display */}
      {data.sources.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">Added Sources</h4>
          <div className="space-y-3">
            {data.sources.map((source, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{source.title}</h5>
                    {source.type === 'url' && (
                      <p className="text-sm text-blue-600 break-all">{source.url}</p>
                    )}
                    {source.description && (
                      <p className="text-sm text-gray-600 mt-1">{source.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {source.wordCount} words • {source.type === 'url' ? 'URL Source' : 'Text Block'}
                    </p>
                  </div>
                  <button
                    onClick={() => removeSource(index)}
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Content Preview */}
                <div className="bg-gray-50 rounded p-3 mt-2">
                  <p className="text-sm text-gray-700 line-clamp-3">
                    {source.content.substring(0, 200)}
                    {source.content.length > 200 && '...'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Step2Sources;
