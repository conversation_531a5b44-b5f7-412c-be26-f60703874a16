import React from 'react';
import { CheckIcon } from '@heroicons/react/24/solid';

const StepIndicator = ({ steps, currentStep, onStepClick, isTaskCompleted = false }) => {
  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Steps</h3>

      <nav className="space-y-2">
        {steps.map((step, index) => {
          // If task is completed, all steps are completed
          const isCompleted = isTaskCompleted ? true : index < currentStep;
          const isCurrent = isTaskCompleted ? false : index === currentStep;
          const isClickable = index <= currentStep;

          return (
            <div key={step.id} className="relative">
              <div
                className={`flex items-center p-3 rounded-md cursor-pointer transition-colors ${
                  isClickable ? 'hover:bg-gray-50' : 'cursor-not-allowed opacity-60'
                } ${
                  isCurrent ? 'bg-blue-50 border border-blue-200' : ''
                }`}
                onClick={() => isClickable && onStepClick(index)}
              >
                {/* Step Number/Icon */}
                <div className="flex-shrink-0">
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isCompleted
                        ? 'bg-green-500 text-white'
                        : isCurrent
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-200 text-gray-600'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckIcon className="w-4 h-4" />
                    ) : (
                      step.id
                    )}
                  </div>
                </div>

                {/* Step Content */}
                <div className="ml-3 flex-1 min-w-0">
                  <p
                    className={`text-sm font-medium ${
                      isCurrent
                        ? 'text-blue-700'
                        : isCompleted
                        ? 'text-green-700'
                        : 'text-gray-500'
                    }`}
                  >
                    {step.title}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {step.subtitle}
                  </p>
                </div>

                {/* Status Indicator */}
                <div className="flex-shrink-0">
                  {isCurrent && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Current
                    </span>
                  )}
                </div>
              </div>

              {/* Connector Line */}
              {index < steps.length - 1 && (
                <div className="ml-7 w-0.5 h-4 bg-gray-200"></div>
              )}
            </div>
          );
        })}
      </nav>

      {/* Progress Summary */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Progress</span>
          <span className="font-medium text-gray-900">
            {isTaskCompleted ? '100%' : `${Math.round((currentStep / steps.length) * 100)}%`}
          </span>
        </div>
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              isTaskCompleted ? 'bg-green-600' : 'bg-blue-600'
            }`}
            style={{ width: `${isTaskCompleted ? 100 : (currentStep / steps.length) * 100}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-500 mt-2">
          <span>{isTaskCompleted ? steps.length : currentStep} completed</span>
          <span>{isTaskCompleted ? 0 : steps.length - currentStep} remaining</span>
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
