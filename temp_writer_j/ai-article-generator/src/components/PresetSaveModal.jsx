import React, { useState } from 'react';
import { XMarkIcon, BookmarkIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { presetService } from '../config/api';

const PresetSaveModal = ({ 
  isOpen, 
  onClose, 
  presetType, // 'author' or 'product'
  presetData, 
  onPresetSaved 
}) => {
  const [presetName, setPresetName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSave = async (e) => {
    e.preventDefault();

    if (!presetName.trim()) {
      setError('Please enter a name for this profile');
      return;
    }

    // Validate that we have some data to save
    const hasContent = validatePresetData();
    if (!hasContent.isValid) {
      setError(hasContent.message);
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const savedPreset = await presetService.createPreset(
        presetType,
        presetName.trim(),
        presetData
      );

      onPresetSaved(savedPreset);
      handleClose();
    } catch (err) {
      console.error('Error saving preset:', err);
      setError(err.message || 'Failed to save profile');
    } finally {
      setIsLoading(false);
    }
  };

  const validatePresetData = () => {
    if (presetType === 'product') {
      const hasContent = presetData?.name || presetData?.description || presetData?.link ||
                        (presetData?.features && presetData.features.length > 0);

      if (!hasContent) {
        return {
          isValid: false,
          message: 'Please fill in at least one field (name, description, link, or features) before saving'
        };
      }
    } else if (presetType === 'author') {
      const hasContent = presetData?.authorName || presetData?.authorBio ||
                        presetData?.targetAudience || presetData?.articleGoal;

      if (!hasContent) {
        return {
          isValid: false,
          message: 'Please fill in at least one field (author name, bio, target audience, or article goal) before saving'
        };
      }
    }

    return { isValid: true };
  };

  const handleClose = () => {
    setPresetName('');
    setError('');
    onClose();
  };

  const getPreviewData = () => {
    if (presetType === 'product') {
      return {
        name: presetData?.name || '',
        description: presetData?.description || '',
        link: presetData?.link || '',
        features: presetData?.features || []
      };
    } else if (presetType === 'author') {
      return {
        authorName: presetData?.authorName || '',
        authorBio: presetData?.authorBio || '',
        targetAudience: presetData?.targetAudience || '',
        articleGoal: presetData?.articleGoal || ''
      };
    }
    return {};
  };

  const preview = getPreviewData();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <BookmarkIcon className="w-6 h-6 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              Save {presetType === 'product' ? 'Product' : 'Author'} Profile
            </h3>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSave} className="p-6 space-y-4">
          {error && (
            <div className="flex items-center space-x-2 p-3 bg-red-100 border border-red-300 text-red-700 text-sm rounded-md">
              <ExclamationTriangleIcon className="w-4 h-4 flex-shrink-0" />
              <span>{error}</span>
            </div>
          )}

          {/* Profile Name Input */}
          <div>
            <label htmlFor="presetName" className="block text-sm font-medium text-gray-700 mb-2">
              Profile Name *
            </label>
            <input
              type="text"
              id="presetName"
              value={presetName}
              onChange={(e) => setPresetName(e.target.value)}
              placeholder={`e.g., ${presetType === 'product' ? 'My SaaS Product' : 'Marketing Expert Profile'}`}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              maxLength={255}
              required
            />
            <p className="text-xs text-gray-500 mt-1">
              Choose a descriptive name to easily identify this profile later
            </p>
          </div>

          {/* Preview Section */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Profile Preview</h4>
            
            {presetType === 'product' ? (
              <div className="space-y-2 text-sm">
                {preview.name && (
                  <div>
                    <span className="font-medium text-gray-700">Name: </span>
                    <span className="text-gray-600">{preview.name}</span>
                  </div>
                )}
                {preview.link && (
                  <div>
                    <span className="font-medium text-gray-700">Link: </span>
                    <span className="text-gray-600">{preview.link}</span>
                  </div>
                )}
                {preview.description && (
                  <div>
                    <span className="font-medium text-gray-700">Description: </span>
                    <span className="text-gray-600">{preview.description}</span>
                  </div>
                )}
                {preview.features.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700">Features: </span>
                    <span className="text-gray-600">{preview.features.length} feature(s)</span>
                  </div>
                )}
              </div>
            ) : (
              <div className="space-y-2 text-sm">
                {preview.authorName && (
                  <div>
                    <span className="font-medium text-gray-700">Author: </span>
                    <span className="text-gray-600">{preview.authorName}</span>
                  </div>
                )}
                {preview.authorBio && (
                  <div>
                    <span className="font-medium text-gray-700">Bio: </span>
                    <span className="text-gray-600">{preview.authorBio.substring(0, 100)}{preview.authorBio.length > 100 ? '...' : ''}</span>
                  </div>
                )}
                {preview.targetAudience && (
                  <div>
                    <span className="font-medium text-gray-700">Target Audience: </span>
                    <span className="text-gray-600">{preview.targetAudience}</span>
                  </div>
                )}
                {preview.articleGoal && (
                  <div>
                    <span className="font-medium text-gray-700">Article Goal: </span>
                    <span className="text-gray-600">{preview.articleGoal}</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !presetName.trim()}
              className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? 'Saving...' : 'Save Profile'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PresetSaveModal;
