// API Configuration
const API_CONFIG = {
  // Use environment variable if set, otherwise fallback to Railway URL in production, localhost in development
  BASE_URL: import.meta.env.VITE_API_BASE_URL ||
    (import.meta.env.PROD
      ? 'https://j-writer-production.up.railway.app'
      : 'http://localhost:3001'),

  // API endpoints
  ENDPOINTS: {
    HEALTH: '/health',

    // Authentication
    AUTH: {
      LOGIN: '/api/auth/login',
      REGISTER: '/api/auth/register',
      LOGOUT: '/api/auth/logout',
      ME: '/api/auth/me',
      VERIFY_EMAIL: '/api/auth/verify-email',
      FORGOT_PASSWORD: '/api/auth/forgot-password',
      RESET_PASSWORD: '/api/auth/reset-password'
    },

    // Tasks
    TASKS: {
      LIST: '/api/tasks',
      CREATE: '/api/tasks',
      AUTO_CREATE: '/api/tasks/auto-create',
      GET: (id) => `/api/tasks/${id}`,
      UPDATE: (id) => `/api/tasks/${id}`,
      DELETE: (id) => `/api/tasks/${id}`,
      STATS: '/api/tasks/stats',
      GENERATE_ARTICLE: (id) => `/api/tasks/${id}/generate-article`,
      FINISH: (id) => `/api/tasks/${id}/finish`
    },

    // Presets
    PRESETS: {
      LIST: '/api/presets',
      CREATE: '/api/presets',
      GET: (id) => `/api/presets/${id}`,
      UPDATE: (id) => `/api/presets/${id}`,
      DELETE: (id) => `/api/presets/${id}`
    },

    // Admin
    ADMIN: {
      MODELS: '/api/admin/models',
      UPDATE_MODEL: (modelName) => `/api/admin/models/${modelName}`,
      PROMPTS: '/api/admin/prompts',
      UPDATE_PROMPT: (id) => `/api/admin/prompts/${id}`,
      ACTIVE_MODEL: '/api/admin/active-model',
      TEST_MODEL: (modelName) => `/api/admin/test-model/${modelName}`,
      STATS: '/api/admin/stats'
    },

    // Subscription & Billing
    SUBSCRIPTION: {
      STATUS: '/api/subscription/status',
      CHECKOUT: '/api/subscription/checkout',
      CANCEL: '/api/subscription/cancel',
      PORTAL: '/api/subscription/portal',
      USAGE: '/api/subscription/usage',
      PLANS: '/api/subscription/plans',
      WEBHOOK: '/api/subscription/webhook'
    },

    // Legacy article generation endpoints
    IDEATION_SUGGESTIONS: '/api/ideation/suggestions',
    IDEATION_AUTOCOMPLETE: '/api/ideation/autocomplete',
    IDEATION_KEYWORD_RESEARCH: '/api/ideation/keyword-research',
    SOURCES_EXTRACT: '/api/sources/extract',
    SOURCES_EXTRACT_MULTIPLE: '/api/sources/extract-multiple',
    GENERATE_ARTICLE: '/api/generate/article'
  }
};

// Subscription service functions
export const subscriptionService = {
  // Get current subscription status and usage
  async getStatus() {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.STATUS), {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get subscription status: ${response.statusText}`);
    }
    
    return await response.json();
  },

  // Create checkout session for subscription
  async createCheckout(planType = 'PRO') {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.CHECKOUT), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ plan_type: planType })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to create checkout: ${response.statusText}`);
    }
    
    return await response.json();
  },

  // Cancel subscription
  async cancelSubscription(effectiveFrom = 'next_billing_period') {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.CANCEL), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({ effective_from: effectiveFrom })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to cancel subscription: ${response.statusText}`);
    }
    
    return await response.json();
  },

  // Get customer portal URL
  async getPortalUrl() {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.PORTAL), {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to get portal URL: ${response.statusText}`);
    }
    
    return await response.json();
  },

  // Get usage statistics
  async getUsage() {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.USAGE), {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get usage: ${response.statusText}`);
    }
    
    return await response.json();
  },

  // Get available plans
  async getPlans() {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.SUBSCRIPTION.PLANS), {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get plans: ${response.statusText}`);
    }
    
    return await response.json();
  }
};

// Helper function to build full API URLs
export const buildApiUrl = (endpoint) => {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;
  console.log('Building API URL:', {
    baseUrl: API_CONFIG.BASE_URL,
    endpoint,
    finalUrl: url,
    env: import.meta.env.PROD ? 'production' : 'development'
  });
  return url;
};

// Preset API service functions
export const presetService = {
  // Get all presets for a specific type (author or product)
  async getPresets(type = null) {
    const url = type ? `${buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.LIST)}?type=${type}` : buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.LIST);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch presets: ${response.statusText}`);
    }

    const data = await response.json();
    return data.presets || [];
  },

  // Get a specific preset by ID
  async getPreset(id) {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.GET(id)), {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch preset: ${response.statusText}`);
    }

    const data = await response.json();
    return data.preset;
  },

  // Create a new preset
  async createPreset(presetType, presetName, presetData) {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.CREATE), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        presetType,
        presetName,
        presetData
      })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to create preset: ${response.statusText}`);
    }

    const data = await response.json();
    return data.preset;
  },

  // Update an existing preset
  async updatePreset(id, presetName = null, presetData = null) {
    const updatePayload = {};
    if (presetName !== null) updatePayload.presetName = presetName;
    if (presetData !== null) updatePayload.presetData = presetData;

    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.UPDATE(id)), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(updatePayload)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update preset: ${response.statusText}`);
    }

    const data = await response.json();
    return data.preset;
  },

  // Delete a preset
  async deletePreset(id) {
    const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.PRESETS.DELETE(id)), {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to delete preset: ${response.statusText}`);
    }

    return true;
  }
};

// Helper function for making API calls with proper error handling
export const apiCall = async (endpoint, options = {}) => {
  const url = buildApiUrl(endpoint);
  const apiKey = import.meta.env.VITE_API_KEY;
  const token = localStorage.getItem('token');

  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      'X-API-Key': apiKey || '', // API key from environment variable
    },
  };

  // Add Authorization header if token exists
  if (token) {
    defaultOptions.headers['Authorization'] = `Bearer ${token}`;
  }

  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  try {
    const response = await fetch(url, finalOptions);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`API call failed for ${endpoint}:`, error);
    throw error;
  }
};



export { API_CONFIG };
export default API_CONFIG;
