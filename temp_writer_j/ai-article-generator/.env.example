# Writer J Frontend - Environment Configuration Example
# Copy this file to .env.local and update with your actual values

# Backend API URL
VITE_API_URL=http://localhost:3001

# App Configuration
VITE_APP_NAME=Writer J
VITE_APP_VERSION=1.0.0

# Feature Flags (true/false)
VITE_ENABLE_REDDIT_FEATURES=true
VITE_ENABLE_ADMIN_FEATURES=true
VITE_ENABLE_ANALYTICS=false

# External Services (Optional)
VITE_GOOGLE_ANALYTICS_ID=your-ga-id
VITE_SENTRY_DSN=your-sentry-dsn