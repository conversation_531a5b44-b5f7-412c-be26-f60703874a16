// RedditAnalyzer utility for processing Reddit content insights

/**
 * 分析和聚合多个Reddit帖子的洞察数据
 * 用于增强AI内容生成
 */
class RedditAnalyzer {
  
  /**
   * 聚合多个Reddit帖子的洞察数据
   * @param {Array} redditSources - 从用户选择的Reddit帖子数据
   * @returns {Object} 聚合后的洞察数据
   */
  static aggregateRedditInsights(redditSources) {
    if (!redditSources || redditSources.length === 0) {
      return null;
    }

    console.log(`聚合 ${redditSources.length} 个Reddit源的洞察数据`);

    // 聚合所有痛点
    const allPainPoints = [];
    // 聚合所有主题词
    const allThemes = [];
    // 聚合用户问题和关注点
    const userConcerns = [];
    // 聚合解决方案和建议
    const userSolutions = [];
    // 收集社区背景信息
    const communityContext = [];

    redditSources.forEach(source => {
      // 添加痛点
      if (source.user_pain_points && source.user_pain_points.length > 0) {
        allPainPoints.push(...source.user_pain_points);
      }

      // 添加主题词
      if (source.common_themes && source.common_themes.length > 0) {
        allThemes.push(...source.common_themes);
      }

      // 从帖子标题和内容提取用户关注点
      if (source.url) {
        userConcerns.push({
          title: source.url, // 这里应该是帖子标题，需要调整数据结构
          subreddit: source.subreddit,
          upvotes: source.upvotes,
          comments: source.comments
        });
      }

      // 添加社区背景
      communityContext.push({
        subreddit: source.subreddit,
        engagement: source.upvotes + (source.comments * 2),
        community_size: 'Unknown' // 可以后续添加
      });
    });

    // 去重和排序痛点
    const uniquePainPoints = [...new Set(allPainPoints)]
      .slice(0, 8) // 限制最多8个主要痛点
      .map(point => point.length > 150 ? point.substring(0, 150) + '...' : point);

    // 合并和排序主题词
    const themeFrequency = {};
    allThemes.forEach(theme => {
      if (theme.word && theme.count) {
        themeFrequency[theme.word] = (themeFrequency[theme.word] || 0) + theme.count;
      }
    });

    const topThemes = Object.entries(themeFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word, count]) => ({ word, count }));

    // 分析社区偏好
    const subredditAnalysis = this.analyzeCommunityPreferences(communityContext);

    return {
      pain_points: uniquePainPoints,
      trending_themes: topThemes,
      user_concerns: userConcerns.slice(0, 5),
      community_analysis: subredditAnalysis,
      total_sources: redditSources.length,
      engagement_summary: {
        total_upvotes: redditSources.reduce((sum, s) => sum + (s.upvotes || 0), 0),
        total_comments: redditSources.reduce((sum, s) => sum + (s.comments || 0), 0),
        avg_engagement: Math.round(redditSources.reduce((sum, s) => sum + ((s.upvotes || 0) + (s.comments || 0)), 0) / redditSources.length)
      }
    };
  }

  /**
   * 分析社区偏好和特征
   */
  static analyzeCommunityPreferences(communityContext) {
    const subredditStats = {};
    
    communityContext.forEach(context => {
      if (!subredditStats[context.subreddit]) {
        subredditStats[context.subreddit] = {
          name: context.subreddit,
          total_engagement: 0,
          post_count: 0
        };
      }
      subredditStats[context.subreddit].total_engagement += context.engagement;
      subredditStats[context.subreddit].post_count += 1;
    });

    // 计算每个subreddit的平均参与度
    Object.values(subredditStats).forEach(stat => {
      stat.avg_engagement = Math.round(stat.total_engagement / stat.post_count);
    });

    return {
      primary_communities: Object.values(subredditStats)
        .sort((a, b) => b.total_engagement - a.total_engagement)
        .slice(0, 3),
      total_communities: Object.keys(subredditStats).length
    };
  }

  /**
   * 为话题生成准备Reddit洞察数据
   */
  static prepareInsightsForTopicGeneration(aggregatedInsights) {
    if (!aggregatedInsights) {
      return '';
    }

    const insights = [];
    
    // 添加痛点信息
    if (aggregatedInsights.pain_points && aggregatedInsights.pain_points.length > 0) {
      insights.push('**Key User Pain Points:**');
      aggregatedInsights.pain_points.forEach((point, index) => {
        insights.push(`${index + 1}. ${point}`);
      });
      insights.push('');
    }

    // 添加趋势主题
    if (aggregatedInsights.trending_themes && aggregatedInsights.trending_themes.length > 0) {
      insights.push('**Trending Discussion Topics:**');
      aggregatedInsights.trending_themes.slice(0, 5).forEach(theme => {
        insights.push(`- ${theme.word} (mentioned ${theme.count} times)`);
      });
      insights.push('');
    }

    // 添加社区背景
    if (aggregatedInsights.community_analysis) {
      insights.push('**Community Context:**');
      insights.push(`- Analyzed ${aggregatedInsights.total_sources} posts from ${aggregatedInsights.community_analysis.total_communities} communities`);
      insights.push(`- Average engagement: ${aggregatedInsights.engagement_summary.avg_engagement} interactions per post`);
      
      if (aggregatedInsights.community_analysis.primary_communities.length > 0) {
        insights.push('- Primary communities: ' + 
          aggregatedInsights.community_analysis.primary_communities
            .map(c => `r/${c.name}`)
            .join(', ')
        );
      }
    }

    return insights.join('\n');
  }

  /**
   * 为最终文章生成准备Reddit洞察数据
   */
  static prepareInsightsForArticleGeneration(aggregatedInsights) {
    if (!aggregatedInsights) {
      return null;
    }

    return {
      community_discussions: aggregatedInsights.pain_points.slice(0, 5).map(point => ({
        insight: point,
        type: 'user_pain_point'
      })),
      trending_angles: aggregatedInsights.trending_themes.slice(0, 5).map(theme => ({
        topic: theme.word,
        frequency: theme.count,
        relevance: 'high'
      })),
      user_authenticity: {
        total_sources: aggregatedInsights.total_sources,
        communities: aggregatedInsights.community_analysis.primary_communities.map(c => c.name),
        engagement_level: aggregatedInsights.engagement_summary.avg_engagement > 10 ? 'high' : 'medium'
      },
      content_validation: aggregatedInsights.pain_points.slice(0, 3).map(point => ({
        user_quote: point.length > 100 ? point.substring(0, 100) + '...' : point,
        context: 'real_user_discussion'
      }))
    };
  }
}

module.exports = RedditAnalyzer;