const express = require('express');
const router = express.Router();
const contentExtractor = require('../services/contentExtractor');

// POST /api/sources/extract
router.post('/extract', async (req, res) => {
  try {
    const { url } = req.body;

    // Validate input
    if (!url || typeof url !== 'string') {
      return res.status(400).json({
        error: 'URL is required and must be a string'
      });
    }

    const trimmedUrl = url.trim();
    if (!trimmedUrl) {
      return res.status(400).json({
        error: 'URL cannot be empty'
      });
    }

    // Extract content from URL
    const extractedData = await contentExtractor.extractFromUrl(trimmedUrl);

    res.json(extractedData);
  } catch (error) {
    console.error('Error in /api/sources/extract:', error);
    res.status(500).json({
      error: 'Failed to extract content from URL',
      message: error.message
    });
  }
});

// POST /api/sources/extract-multiple
router.post('/extract-multiple', async (req, res) => {
  try {
    const { urls } = req.body;

    // Validate input
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      return res.status(400).json({
        error: 'URLs array is required and must not be empty'
      });
    }

    // Validate URLs
    const validUrls = urls.filter(url => 
      typeof url === 'string' && url.trim().length > 0
    );

    if (validUrls.length === 0) {
      return res.status(400).json({
        error: 'At least one valid URL is required'
      });
    }

    // Limit to maximum 3 URLs for performance
    const limitedUrls = validUrls.slice(0, 3);

    // Extract content from multiple URLs
    const extractedData = await contentExtractor.extractMultipleUrls(limitedUrls);

    res.json(extractedData);
  } catch (error) {
    console.error('Error in /api/sources/extract-multiple:', error);
    res.status(500).json({
      error: 'Failed to extract content from URLs',
      message: error.message
    });
  }
});

module.exports = router;
