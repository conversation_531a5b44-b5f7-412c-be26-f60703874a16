const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const database = require('../config/database');
const aiServiceManager = require('../services/aiServiceManager');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// GET /api/tasks - Get all tasks for the authenticated user
router.get('/', async (req, res) => {
  try {
    const { status, sortBy = 'updated_at', sortOrder = 'DESC', page = 1, limit = 20 } = req.query;

    let query = 'SELECT * FROM tasks WHERE user_id = ?';
    const params = [req.user.id];

    // Add status filter if provided
    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    // Add sorting
    const validSortFields = ['name', 'status', 'created_at', 'updated_at'];
    const validSortOrders = ['ASC', 'DESC'];

    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY updated_at DESC';
    }

    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    query += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);

    const tasks = await database.all(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?';
    const countParams = [req.user.id];

    if (status) {
      countQuery += ' AND status = ?';
      countParams.push(status);
    }

    const { total } = await database.get(countQuery, countParams);

    // Parse JSON fields for response
    const formattedTasks = tasks.map(task => ({
      ...task,
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      redditSources: task.reddit_sources ? JSON.parse(task.reddit_sources) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      // topicSources deprecated - use comprehensiveResources instead
      comprehensiveResources: task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [],
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {}
    }));

    res.json({
      tasks: formattedTasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      error: 'Failed to retrieve tasks',
      message: 'An error occurred while retrieving your tasks'
    });
  }
});

// GET /api/tasks/:id - Get a specific task
router.get('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse JSON fields
    const formattedTask = {
      ...task,
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      redditSources: task.reddit_sources ? JSON.parse(task.reddit_sources) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      // topicSources deprecated - use comprehensiveResources instead
      comprehensiveResources: task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [],
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {}
    };

    res.json({ task: formattedTask });

  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task',
      message: 'An error occurred while retrieving the task'
    });
  }
});

// POST /api/tasks/auto-create - Auto-create a new task with timestamp
router.post('/auto-create', async (req, res) => {
  try {
    const taskId = uuidv4();
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    const taskName = `Article Task - ${timestamp}`;

    // Prepare initial data with defaults
    const defaultData = {
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      redditSources: [],
      sources: [],
      // topicSources deprecated
      comprehensiveResources: [],
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        reddit_sources, sources, topic_sources, comprehensive_resources,
        product_info, eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        JSON.stringify(defaultData.keywords),
        JSON.stringify(defaultData.selectedTopics),
        JSON.stringify(defaultData.topicSuggestions),
        JSON.stringify(defaultData.keywordResearchData),
        JSON.stringify(defaultData.keywordResearchSelections),
        JSON.stringify(defaultData.redditSources),
        JSON.stringify(defaultData.sources),
        JSON.stringify(defaultData.topicSources),
        JSON.stringify(defaultData.comprehensiveResources),
        JSON.stringify(defaultData.productInfo),
        JSON.stringify(defaultData.eeatProfile),
        JSON.stringify(defaultData.outputParameters)
      ]
    );

    // Get the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      keywords: JSON.parse(createdTask.keywords),
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      redditSources: JSON.parse(createdTask.reddit_sources),
      sources: JSON.parse(createdTask.sources),
      // topicSources deprecated
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Auto-create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// POST /api/tasks - Create a new task (legacy endpoint)
router.post('/', async (req, res) => {
  try {
    const { name, initialData = {} } = req.body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid task name',
        message: 'Task name is required and must be a non-empty string'
      });
    }

    const taskId = uuidv4();
    const taskName = name.trim().slice(0, 255); // Limit name length

    // Prepare initial data with defaults
    const defaultData = {
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      redditSources: [],
      sources: [],
      // topicSources deprecated
      comprehensiveResources: [],
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    const mergedData = { ...defaultData, ...initialData };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        reddit_sources, sources, topic_sources, comprehensive_resources,
        product_info, eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        JSON.stringify(mergedData.keywords),
        JSON.stringify(mergedData.selectedTopics),
        JSON.stringify(mergedData.topicSuggestions),
        JSON.stringify(mergedData.keywordResearchData),
        JSON.stringify(mergedData.keywordResearchSelections),
        JSON.stringify(mergedData.redditSources),
        JSON.stringify(mergedData.sources),
        JSON.stringify(mergedData.topicSources),
        JSON.stringify(mergedData.comprehensiveResources || []),
        JSON.stringify(mergedData.productInfo),
        JSON.stringify(mergedData.eeatProfile),
        JSON.stringify(mergedData.outputParameters)
      ]
    );

    // Return the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      keywords: JSON.parse(createdTask.keywords),
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      redditSources: JSON.parse(createdTask.reddit_sources),
      sources: JSON.parse(createdTask.sources),
      // topicSources deprecated
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// PUT /api/tasks/:id - Update a task
router.put('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const {
      name,
      status,
      currentStep,
      keywords,
      selectedTopics,
      topicSuggestions,
      keywordResearchData,
      keywordResearchSelections,
      redditSources,
      sources,
      topicSources,
      comprehensiveResources,
      productInfo,
      eeatProfile,
      outputParameters,
      generatedArticle,
      generated_article  // Accept both camelCase and snake_case
    } = req.body;

    // Use either field name for generated article
    const articleContent = generatedArticle || generated_article;

    // Debug article content updates
    if (articleContent !== undefined) {
      console.log('=== ARTICLE UPDATE DEBUG ===');
      console.log('Received article content length:', articleContent ? articleContent.length : 'null/undefined');
      console.log('Article content preview:', articleContent ? articleContent.substring(0, 100) + '...' : 'null');
      console.log('============================');
    }

    // Build update query dynamically based on provided fields
    const updates = [];
    const params = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name.toString().trim().slice(0, 255));
    }

    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status.toString());
    }

    if (currentStep !== undefined) {
      updates.push('current_step = ?');
      params.push(parseInt(currentStep));
    }

    if (keywords !== undefined) {
      updates.push('keywords = ?');
      params.push(JSON.stringify(keywords));
    }

    if (selectedTopics !== undefined) {
      updates.push('selected_topics = ?');
      params.push(JSON.stringify(selectedTopics));
    }

    if (topicSuggestions !== undefined) {
      updates.push('topic_suggestions = ?');
      params.push(JSON.stringify(topicSuggestions));
    }

    if (keywordResearchData !== undefined) {
      updates.push('keyword_research_data = ?');
      params.push(JSON.stringify(keywordResearchData));
    }

    if (keywordResearchSelections !== undefined) {
      updates.push('keyword_research_selections = ?');
      params.push(JSON.stringify(keywordResearchSelections));
    }

    if (redditSources !== undefined) {
      updates.push('reddit_sources = ?');
      params.push(JSON.stringify(redditSources));
    }

    if (sources !== undefined) {
      updates.push('sources = ?');
      params.push(JSON.stringify(sources));
    }

    if (topicSources !== undefined) {
      updates.push('topic_sources = ?');
      params.push(JSON.stringify(topicSources));
    }

    if (comprehensiveResources !== undefined) {
      updates.push('comprehensive_resources = ?');
      params.push(JSON.stringify(comprehensiveResources));
    }

    if (productInfo !== undefined) {
      updates.push('product_info = ?');
      params.push(JSON.stringify(productInfo));
    }

    if (eeatProfile !== undefined) {
      updates.push('eeat_profile = ?');
      params.push(JSON.stringify(eeatProfile));
    }

    if (outputParameters !== undefined) {
      updates.push('output_parameters = ?');
      params.push(JSON.stringify(outputParameters));
    }

    if (articleContent !== undefined) {
      updates.push('generated_article = ?');
      params.push(articleContent);

      // Auto-update task name to article title when article is completed
      if (articleContent && typeof articleContent === 'string') {
        try {
          // First try to extract SEO title from structured format
          const seoTitleMatch = articleContent.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

          let articleTitle = null;

          if (seoTitleMatch && seoTitleMatch[1]) {
            articleTitle = seoTitleMatch[1].trim();
            // Remove any markdown formatting and brackets
            articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
            // Remove any trailing periods or punctuation that might be part of the format
            articleTitle = articleTitle.replace(/[.]*$/, '').trim();
            // Stop at any meta description content that might have been captured
            articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          } else {
            // Fallback: try to extract from markdown header or first line
            const titleMatch = articleContent.match(/^#\s*(.+)$/m) ||
                             articleContent.match(/^(.+)$/m);

            if (titleMatch && titleMatch[1]) {
              articleTitle = titleMatch[1].trim();
              // Remove any markdown formatting
              articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            }
          }

          if (articleTitle) {
            // Limit title length
            articleTitle = articleTitle.slice(0, 200);
            updates.push('name = ?');
            params.push(articleTitle); // Add to end of params array
          }
        } catch (error) {
          console.warn('Failed to extract article title:', error);
          // Continue without updating the name
        }
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    // Always update the updated_at timestamp
    updates.push('updated_at = CURRENT_TIMESTAMP');

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    const query = `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`;

    // Debug query and params
    console.log('=== UPDATE QUERY DEBUG ===');
    console.log('Query:', query);
    console.log('Params:', params);
    console.log('Updates:', updates);
    console.log('========================');

    const result = await database.run(query, params);

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found or updated'
      });
    }

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      redditSources: updatedTask.reddit_sources ? JSON.parse(updatedTask.reddit_sources) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      // topicSources deprecated
      comprehensiveResources: updatedTask.comprehensive_resources ? JSON.parse(updatedTask.comprehensive_resources) : [],
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {}
    };

    res.json({
      message: 'Task updated successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      error: 'Failed to update task',
      message: 'An error occurred while updating the task'
    });
  }
});

// POST /api/tasks/:id/generate-article - Generate article for a task
router.post('/:id/generate-article', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract data
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse task data
    const selectedTopics = task.selected_topics ? JSON.parse(task.selected_topics) : [];
    // Note: topic_sources is deprecated - we only use comprehensive_resources now
    const sources = task.sources ? JSON.parse(task.sources) : [];
    const productInfo = task.product_info ? JSON.parse(task.product_info) : {};
    const eeatProfile = task.eeat_profile ? JSON.parse(task.eeat_profile) : {};
    const outputParameters = task.output_parameters ? JSON.parse(task.output_parameters) : {};
    const keywordResearchData = task.keyword_research_data ? JSON.parse(task.keyword_research_data) : {};
    const keywordResearchSelections = task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [];
    const redditSources = task.reddit_sources ? JSON.parse(task.reddit_sources) : [];
    const comprehensiveResources = task.comprehensive_resources ? JSON.parse(task.comprehensive_resources) : [];

    // Validate that we have topics to work with
    if (!selectedTopics || selectedTopics.length === 0) {
      return res.status(400).json({
        error: 'No topics selected',
        message: 'Please select at least one topic before generating an article'
      });
    }

    // Extract topics
    const allTopics = selectedTopics.map(t => t.edited || t.original || t);

    // Process sources (for backward compatibility with old 'sources' field)
    const legacySources = sources || [];
    
    console.log('📊 Data sources summary:');
    console.log('- Legacy sources:', legacySources.length);
    console.log('- Comprehensive resources:', comprehensiveResources.length);
    console.log('- Reddit sources:', redditSources.length);

    // Extract keywords - Debug the raw data first
    console.log('=== RAW KEYWORD DATA DEBUG ===');
    console.log('keywordResearchData:', keywordResearchData);
    console.log('keywordResearchSelections type:', typeof keywordResearchSelections);
    console.log('keywordResearchSelections length:', keywordResearchSelections ? keywordResearchSelections.length : 'null/undefined');
    console.log('keywordResearchSelections content:', JSON.stringify(keywordResearchSelections, null, 2));
    console.log('===============================');

    const primaryKeywords = [];
    if (keywordResearchData && keywordResearchData.keyword) {
      primaryKeywords.push(keywordResearchData.keyword);
      console.log('Added keyword from keywordResearchData:', keywordResearchData.keyword);
    }

    if (keywordResearchSelections && Array.isArray(keywordResearchSelections)) {
      keywordResearchSelections.forEach((selection, index) => {
        console.log(`Processing selection ${index}:`, selection);
        if (selection.type === 'autocomplete' || selection.type === 'related' || selection.type === 'custom') {
          const keyword = selection.value || selection.text;
          primaryKeywords.push(keyword);
          console.log(`Added PRIMARY keyword: "${keyword}" (type: ${selection.type})`);
        }
      });
    } else {
      console.log('keywordResearchSelections is not a valid array');
    }

    const secondaryKeywords = [];
    if (keywordResearchSelections && Array.isArray(keywordResearchSelections)) {
      keywordResearchSelections.forEach((selection, index) => {
        console.log(`Processing selection ${index} for secondary:`, selection);
        if (selection.type === 'keyTerm' || selection.type === 'keyTerms' || selection.type === 'paa' || selection.type === 'peopleAlsoAsk') {
          const keyword = selection.value || selection.text;
          secondaryKeywords.push(keyword);
          console.log(`Added SECONDARY keyword: "${keyword}" (type: ${selection.type})`);
        }
      });
    }

    // Prepare article data
    const articleData = {
      topics: allTopics,
      sources: legacySources, // Legacy sources for backward compatibility
      productInfo: productInfo,
      tonality: outputParameters.tonality || 'informative',
      length: outputParameters.length || 'medium_article',
      format: outputParameters.format || 'markdown',
      authorName: eeatProfile.authorName || '',
      authorBio: eeatProfile.authorBio || '',
      targetAudience: eeatProfile.targetAudience || '',
      articleGoal: eeatProfile.articleGoal || '',
      primaryKeywords: [...new Set(primaryKeywords)],
      secondaryKeywords: [...new Set(secondaryKeywords)],
      redditSources: redditSources,
      comprehensiveResources: comprehensiveResources
    };

    console.log('Generating article for task:', req.params.id);
    console.log('Primary keywords extracted:', primaryKeywords);
    console.log('Secondary keywords extracted:', secondaryKeywords);
    console.log('Keyword research selections:', keywordResearchSelections);
    console.log('Full article data:', articleData);

    // Generate article
    const generatedArticle = await aiServiceManager.generateArticle(articleData);

    // Update task with generated article only (don't auto-complete yet)
    await database.run(
      'UPDATE tasks SET generated_article = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [generatedArticle, req.params.id, req.user.id]
    );

    res.json({
      article: generatedArticle,
      message: 'Article generated successfully'
    });

  } catch (error) {
    console.error('Generate article error:', error);
    res.status(500).json({
      error: 'Failed to generate article',
      message: error.message || 'An error occurred while generating the article'
    });
  }
});

// POST /api/tasks/:id/finish - Mark task as completed and update title
router.post('/:id/finish', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract the article
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    if (!task.generated_article) {
      return res.status(400).json({
        error: 'No article to finish',
        message: 'Please generate an article before finishing the task'
      });
    }

    // Prepare update query with title extraction
    const updates = ['status = ?', 'current_step = ?', 'updated_at = CURRENT_TIMESTAMP'];
    const params = ['Completed', 6]; // Set to final step (0-indexed)

    // Extract SEO title from the generated article
    if (task.generated_article && typeof task.generated_article === 'string') {
      try {
        // Look for the SEO Title in the structured output format with more precise regex
        // This regex ensures we only capture the title line and stop at the next line or meta description
        const seoTitleMatch = task.generated_article.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

        if (seoTitleMatch && seoTitleMatch[1]) {
          let articleTitle = seoTitleMatch[1].trim();
          // Remove any markdown formatting and brackets
          articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
          // Remove any trailing periods or punctuation that might be part of the format
          articleTitle = articleTitle.replace(/[.]*$/, '').trim();
          // Stop at any meta description content that might have been captured
          articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          // Limit title length
          articleTitle = articleTitle.slice(0, 200);

          if (articleTitle) {
            updates.push('name = ?');
            params.push(articleTitle);
            console.log('Finishing task and updating name to SEO title:', articleTitle);
          }
        } else {
          // Fallback: try to extract from markdown header or first line
          const fallbackMatch = task.generated_article.match(/^#\s*(.+)$/m) ||
                               task.generated_article.match(/^(.+)$/m);

          if (fallbackMatch && fallbackMatch[1]) {
            let articleTitle = fallbackMatch[1].trim();
            articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            articleTitle = articleTitle.slice(0, 200);

            if (articleTitle) {
              updates.push('name = ?');
              params.push(articleTitle);
              console.log('Finishing task and updating name to fallback title:', articleTitle);
            }
          }
        }
      } catch (error) {
        console.warn('Failed to extract SEO title:', error);
        // Continue without updating the name
      }
    }

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    // Update task to completed status with new title
    await database.run(
      `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
      params
    );

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      redditSources: updatedTask.reddit_sources ? JSON.parse(updatedTask.reddit_sources) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      // topicSources deprecated
      comprehensiveResources: updatedTask.comprehensive_resources ? JSON.parse(updatedTask.comprehensive_resources) : [],
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {}
    };

    res.json({
      message: 'Task completed successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Finish task error:', error);
    res.status(500).json({
      error: 'Failed to finish task',
      message: error.message || 'An error occurred while finishing the task'
    });
  }
});

// DELETE /api/tasks/:id - Delete a task
router.delete('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const result = await database.run(
      'DELETE FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    res.json({
      message: 'Task deleted successfully'
    });

  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      error: 'Failed to delete task',
      message: 'An error occurred while deleting the task'
    });
  }
});

// GET /api/tasks/stats - Get task statistics for the user
router.get('/stats', async (req, res) => {
  try {
    const stats = await database.all(
      `SELECT
        status,
        COUNT(*) as count
      FROM tasks
      WHERE user_id = ?
      GROUP BY status`,
      [req.user.id]
    );

    const totalTasks = await database.get(
      'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?',
      [req.user.id]
    );

    res.json({
      totalTasks: totalTasks.total,
      statusBreakdown: stats
    });

  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task statistics',
      message: 'An error occurred while retrieving task statistics'
    });
  }
});

module.exports = router;
