const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/--+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Helper function to calculate read time
function calculateReadTime(content) {
  const wordsPerMinute = 200;
  const wordCount = content.split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// Public routes

// Get published blog posts with pagination, filtering, and search
router.get('/', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const offset = (page - 1) * limit;
    const category = req.query.category;
    const tag = req.query.tag;
    const search = req.query.search;
    const featured = req.query.featured === 'true';

    let whereConditions = ['published = true'];
    let queryParams = [];
    let paramIndex = 1;

    // Add category filter
    if (category) {
      whereConditions.push(`category = $${paramIndex}`);
      queryParams.push(category);
      paramIndex++;
    }

    // Add tag filter
    if (tag) {
      whereConditions.push(`$${paramIndex} = ANY(tags)`);
      queryParams.push(tag);
      paramIndex++;
    }

    // Add search filter
    if (search) {
      whereConditions.push(`(title ILIKE $${paramIndex} OR content ILIKE $${paramIndex} OR excerpt ILIKE $${paramIndex})`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    // Add featured filter
    if (featured) {
      whereConditions.push('featured = true');
    }

    const whereClause = whereConditions.join(' AND ');

    // Get total count
    const countQuery = `SELECT COUNT(*) FROM blog_posts WHERE ${whereClause}`;
    const countResult = await pool.query(countQuery, queryParams);
    const total = parseInt(countResult.rows[0].count);

    // Get posts
    const postsQuery = `
      SELECT id, title, slug, excerpt, author, cover_image, category, tags, 
             read_time, featured, created_at, updated_at
      FROM blog_posts 
      WHERE ${whereClause}
      ORDER BY featured DESC, created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    queryParams.push(limit, offset);

    const postsResult = await pool.query(postsQuery, queryParams);

    res.json({
      posts: postsResult.rows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    res.status(500).json({ error: 'Failed to fetch blog posts' });
  }
});

// Get single blog post by slug
router.get('/post/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const result = await pool.query(
      'SELECT * FROM blog_posts WHERE slug = $1 AND published = true',
      [slug]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Blog post not found' });
    }

    const post = result.rows[0];

    // Get related posts (same category, excluding current post)
    const relatedResult = await pool.query(
      `SELECT id, title, slug, excerpt, cover_image, category, tags, read_time, created_at
       FROM blog_posts 
       WHERE category = $1 AND slug != $2 AND published = true
       ORDER BY created_at DESC
       LIMIT 3`,
      [post.category, slug]
    );

    res.json({
      post,
      relatedPosts: relatedResult.rows
    });
  } catch (error) {
    console.error('Error fetching blog post:', error);
    res.status(500).json({ error: 'Failed to fetch blog post' });
  }
});

// Get blog categories
router.get('/categories', async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM blog_categories ORDER BY name'
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({ error: 'Failed to fetch categories' });
  }
});

// Get popular tags
router.get('/tags', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT tag, COUNT(*) as count
      FROM blog_posts, unnest(tags) as tag
      WHERE published = true
      GROUP BY tag
      ORDER BY count DESC, tag
      LIMIT 20
    `);

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching tags:', error);
    res.status(500).json({ error: 'Failed to fetch tags' });
  }
});

// Admin routes (require authentication)

// Get all blog posts (including unpublished) for admin
router.get('/all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const offset = (page - 1) * limit;

    // Get total count
    const countResult = await pool.query('SELECT COUNT(*) FROM blog_posts');
    const total = parseInt(countResult.rows[0].count);

    // Get posts
    const result = await pool.query(
      `SELECT * FROM blog_posts 
       ORDER BY created_at DESC
       LIMIT $1 OFFSET $2`,
      [limit, offset]
    );

    res.json({
      posts: result.rows,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching all blog posts:', error);
    res.status(500).json({ error: 'Failed to fetch blog posts' });
  }
});

// Create new blog post
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      title,
      content,
      excerpt,
      author,
      coverImage,
      category,
      tags,
      published,
      featured,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // Generate slug from title
    let slug = generateSlug(title);
    
    // Check if slug exists and make it unique
    let slugCounter = 1;
    let uniqueSlug = slug;
    while (true) {
      const existing = await pool.query('SELECT id FROM blog_posts WHERE slug = $1', [uniqueSlug]);
      if (existing.rows.length === 0) break;
      uniqueSlug = `${slug}-${slugCounter}`;
      slugCounter++;
    }

    // Calculate read time
    const readTime = calculateReadTime(content || '');

    const result = await pool.query(
      `INSERT INTO blog_posts (
        title, slug, content, excerpt, author, cover_image, category, tags,
        read_time, published, featured, seo_title, seo_description, seo_keywords
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *`,
      [
        title, uniqueSlug, content, excerpt, author, coverImage, category, tags || [],
        readTime, published || false, featured || false, seoTitle, seoDescription, seoKeywords || []
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating blog post:', error);
    res.status(500).json({ error: 'Failed to create blog post' });
  }
});

// Update blog post
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      excerpt,
      author,
      coverImage,
      category,
      tags,
      published,
      featured,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // Check if post exists
    const existing = await pool.query('SELECT * FROM blog_posts WHERE id = $1', [id]);
    if (existing.rows.length === 0) {
      return res.status(404).json({ error: 'Blog post not found' });
    }

    const currentPost = existing.rows[0];
    
    // Generate new slug if title changed
    let slug = currentPost.slug;
    if (title && title !== currentPost.title) {
      slug = generateSlug(title);
      
      // Make slug unique (excluding current post)
      let slugCounter = 1;
      let uniqueSlug = slug;
      while (true) {
        const existing = await pool.query('SELECT id FROM blog_posts WHERE slug = $1 AND id != $2', [uniqueSlug, id]);
        if (existing.rows.length === 0) break;
        uniqueSlug = `${slug}-${slugCounter}`;
        slugCounter++;
      }
      slug = uniqueSlug;
    }

    // Calculate read time if content changed
    let readTime = currentPost.read_time;
    if (content && content !== currentPost.content) {
      readTime = calculateReadTime(content);
    }

    const result = await pool.query(
      `UPDATE blog_posts SET
        title = COALESCE($1, title),
        slug = $2,
        content = COALESCE($3, content),
        excerpt = COALESCE($4, excerpt),
        author = COALESCE($5, author),
        cover_image = COALESCE($6, cover_image),
        category = COALESCE($7, category),
        tags = COALESCE($8, tags),
        read_time = $9,
        published = COALESCE($10, published),
        featured = COALESCE($11, featured),
        seo_title = COALESCE($12, seo_title),
        seo_description = COALESCE($13, seo_description),
        seo_keywords = COALESCE($14, seo_keywords),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $15
      RETURNING *`,
      [
        title, slug, content, excerpt, author, coverImage, category, tags,
        readTime, published, featured, seoTitle, seoDescription, seoKeywords, id
      ]
    );

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating blog post:', error);
    res.status(500).json({ error: 'Failed to update blog post' });
  }
});

// Delete blog post
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query('DELETE FROM blog_posts WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Blog post not found' });
    }

    res.json({ message: 'Blog post deleted successfully' });
  } catch (error) {
    console.error('Error deleting blog post:', error);
    res.status(500).json({ error: 'Failed to delete blog post' });
  }
});

// Category management routes

// Create category
router.post('/categories', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { name, description } = req.body;
    const slug = generateSlug(name);

    const result = await pool.query(
      'INSERT INTO blog_categories (name, slug, description) VALUES ($1, $2, $3) RETURNING *',
      [name, slug, description]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating category:', error);
    if (error.code === '23505') { // Unique violation
      res.status(400).json({ error: 'Category name or slug already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create category' });
    }
  }
});

// Update category
router.put('/categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description } = req.body;

    let slug;
    if (name) {
      slug = generateSlug(name);
    }

    const result = await pool.query(
      `UPDATE blog_categories SET
        name = COALESCE($1, name),
        slug = COALESCE($2, slug),
        description = COALESCE($3, description)
      WHERE id = $4
      RETURNING *`,
      [name, slug, description, id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating category:', error);
    res.status(500).json({ error: 'Failed to update category' });
  }
});

// Delete category
router.delete('/categories/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if category is being used
    const usageCheck = await pool.query('SELECT id FROM blog_posts WHERE category = (SELECT slug FROM blog_categories WHERE id = $1) LIMIT 1', [id]);
    
    if (usageCheck.rows.length > 0) {
      return res.status(400).json({ error: 'Cannot delete category that is being used by blog posts' });
    }

    const result = await pool.query('DELETE FROM blog_categories WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Category not found' });
    }

    res.json({ message: 'Category deleted successfully' });
  } catch (error) {
    console.error('Error deleting category:', error);
    res.status(500).json({ error: 'Failed to delete category' });
  }
});

module.exports = router;