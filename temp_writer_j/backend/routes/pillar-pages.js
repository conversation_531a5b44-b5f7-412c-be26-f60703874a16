const express = require('express');
const router = express.Router();
const pool = require('../config/database');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/--+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Public routes

// Get all published pillar pages
router.get('/', async (req, res) => {
  try {
    const result = await pool.query(
      `SELECT id, title, slug, excerpt, author, cover_image, category, menu_order,
              published, featured, created_at, updated_at
       FROM pillar_pages 
       WHERE published = true
       ORDER BY menu_order ASC, created_at DESC`
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching pillar pages:', error);
    res.status(500).json({ error: 'Failed to fetch pillar pages' });
  }
});

// Get pillar page by slug
router.get('/slug/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const result = await pool.query(
      'SELECT * FROM pillar_pages WHERE slug = $1 AND published = true',
      [slug]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Pillar page not found' });
    }

    const pillarPage = result.rows[0];

    // Get cluster pages (related blog posts)
    if (pillarPage.cluster_pages && pillarPage.cluster_pages.length > 0) {
      const blogPostIds = pillarPage.cluster_pages.map(cp => cp.blogPostId);
      
      if (blogPostIds.length > 0) {
        const blogPostsResult = await pool.query(
          `SELECT id, title, slug, excerpt, cover_image, category, tags, read_time, created_at
           FROM blog_posts 
           WHERE id = ANY($1) AND published = true`,
          [blogPostIds]
        );

        // Merge blog post data with cluster page metadata
        const clusterPages = pillarPage.cluster_pages.map(cp => {
          const blogPost = blogPostsResult.rows.find(bp => bp.id === cp.blogPostId);
          return blogPost ? {
            ...blogPost,
            displayOrder: cp.displayOrder,
            customTitle: cp.customTitle,
            customDescription: cp.customDescription
          } : null;
        }).filter(Boolean).sort((a, b) => a.displayOrder - b.displayOrder);

        pillarPage.clusterPages = clusterPages;
      } else {
        pillarPage.clusterPages = [];
      }
    } else {
      pillarPage.clusterPages = [];
    }

    res.json(pillarPage);
  } catch (error) {
    console.error('Error fetching pillar page:', error);
    res.status(500).json({ error: 'Failed to fetch pillar page' });
  }
});

// Get pillar pages by category
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;

    const result = await pool.query(
      `SELECT id, title, slug, excerpt, author, cover_image, category, menu_order,
              published, featured, created_at, updated_at
       FROM pillar_pages 
       WHERE category = $1 AND published = true
       ORDER BY menu_order ASC, created_at DESC`,
      [category]
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching pillar pages by category:', error);
    res.status(500).json({ error: 'Failed to fetch pillar pages' });
  }
});

// Get cluster pages for a pillar page (lazy loading)
router.get('/slug/:slug/cluster-pages', async (req, res) => {
  try {
    const { slug } = req.params;

    // First get the pillar page
    const pillarResult = await pool.query(
      'SELECT cluster_pages FROM pillar_pages WHERE slug = $1 AND published = true',
      [slug]
    );

    if (pillarResult.rows.length === 0) {
      return res.status(404).json({ error: 'Pillar page not found' });
    }

    const clusterPages = pillarResult.rows[0].cluster_pages || [];

    if (clusterPages.length === 0) {
      return res.json([]);
    }

    // Get blog posts for cluster pages
    const blogPostIds = clusterPages.map(cp => cp.blogPostId);
    
    const blogPostsResult = await pool.query(
      `SELECT id, title, slug, excerpt, cover_image, category, tags, read_time, created_at
       FROM blog_posts 
       WHERE id = ANY($1) AND published = true`,
      [blogPostIds]
    );

    // Merge with cluster metadata
    const result = clusterPages.map(cp => {
      const blogPost = blogPostsResult.rows.find(bp => bp.id === cp.blogPostId);
      return blogPost ? {
        ...blogPost,
        displayOrder: cp.displayOrder,
        customTitle: cp.customTitle,
        customDescription: cp.customDescription
      } : null;
    }).filter(Boolean).sort((a, b) => a.displayOrder - b.displayOrder);

    res.json(result);
  } catch (error) {
    console.error('Error fetching cluster pages:', error);
    res.status(500).json({ error: 'Failed to fetch cluster pages' });
  }
});

// Admin routes

// Get all pillar pages for admin
router.get('/all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await pool.query(
      'SELECT * FROM pillar_pages ORDER BY menu_order ASC, created_at DESC'
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching all pillar pages:', error);
    res.status(500).json({ error: 'Failed to fetch pillar pages' });
  }
});

// Get available blog posts for clustering
router.get('/blog-posts/available', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await pool.query(
      `SELECT id, title, slug, excerpt, category, tags, created_at, published
       FROM blog_posts 
       ORDER BY created_at DESC`
    );

    res.json(result.rows);
  } catch (error) {
    console.error('Error fetching available blog posts:', error);
    res.status(500).json({ error: 'Failed to fetch blog posts' });
  }
});

// Create pillar page
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const {
      title,
      content,
      excerpt,
      author,
      coverImage,
      category,
      menuOrder,
      clusterPages,
      published,
      featured,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // Generate slug from title
    let slug = generateSlug(title);
    
    // Check if slug exists and make it unique
    let slugCounter = 1;
    let uniqueSlug = slug;
    while (true) {
      const existing = await pool.query('SELECT id FROM pillar_pages WHERE slug = $1', [uniqueSlug]);
      if (existing.rows.length === 0) break;
      uniqueSlug = `${slug}-${slugCounter}`;
      slugCounter++;
    }

    const result = await pool.query(
      `INSERT INTO pillar_pages (
        title, slug, content, excerpt, author, cover_image, category, menu_order,
        cluster_pages, published, featured, seo_title, seo_description, seo_keywords
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
      RETURNING *`,
      [
        title, uniqueSlug, content, excerpt, author, coverImage, category, menuOrder || 0,
        JSON.stringify(clusterPages || []), published || false, featured || false,
        seoTitle, seoDescription, seoKeywords || []
      ]
    );

    res.status(201).json(result.rows[0]);
  } catch (error) {
    console.error('Error creating pillar page:', error);
    res.status(500).json({ error: 'Failed to create pillar page' });
  }
});

// Update pillar page
router.put('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      title,
      content,
      excerpt,
      author,
      coverImage,
      category,
      menuOrder,
      clusterPages,
      published,
      featured,
      seoTitle,
      seoDescription,
      seoKeywords
    } = req.body;

    // Check if page exists
    const existing = await pool.query('SELECT * FROM pillar_pages WHERE id = $1', [id]);
    if (existing.rows.length === 0) {
      return res.status(404).json({ error: 'Pillar page not found' });
    }

    const currentPage = existing.rows[0];
    
    // Generate new slug if title changed
    let slug = currentPage.slug;
    if (title && title !== currentPage.title) {
      slug = generateSlug(title);
      
      // Make slug unique (excluding current page)
      let slugCounter = 1;
      let uniqueSlug = slug;
      while (true) {
        const existing = await pool.query('SELECT id FROM pillar_pages WHERE slug = $1 AND id != $2', [uniqueSlug, id]);
        if (existing.rows.length === 0) break;
        uniqueSlug = `${slug}-${slugCounter}`;
        slugCounter++;
      }
      slug = uniqueSlug;
    }

    const result = await pool.query(
      `UPDATE pillar_pages SET
        title = COALESCE($1, title),
        slug = $2,
        content = COALESCE($3, content),
        excerpt = COALESCE($4, excerpt),
        author = COALESCE($5, author),
        cover_image = COALESCE($6, cover_image),
        category = COALESCE($7, category),
        menu_order = COALESCE($8, menu_order),
        cluster_pages = COALESCE($9, cluster_pages),
        published = COALESCE($10, published),
        featured = COALESCE($11, featured),
        seo_title = COALESCE($12, seo_title),
        seo_description = COALESCE($13, seo_description),
        seo_keywords = COALESCE($14, seo_keywords),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = $15
      RETURNING *`,
      [
        title, slug, content, excerpt, author, coverImage, category, menuOrder,
        clusterPages ? JSON.stringify(clusterPages) : undefined,
        published, featured, seoTitle, seoDescription, seoKeywords, id
      ]
    );

    res.json(result.rows[0]);
  } catch (error) {
    console.error('Error updating pillar page:', error);
    res.status(500).json({ error: 'Failed to update pillar page' });
  }
});

// Delete pillar page
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const result = await pool.query('DELETE FROM pillar_pages WHERE id = $1 RETURNING *', [id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Pillar page not found' });
    }

    res.json({ message: 'Pillar page deleted successfully' });
  } catch (error) {
    console.error('Error deleting pillar page:', error);
    res.status(500).json({ error: 'Failed to delete pillar page' });
  }
});

// Reorder pillar pages
router.put('/reorder', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { pages } = req.body; // Array of { id, menuOrder }

    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      for (const page of pages) {
        await client.query(
          'UPDATE pillar_pages SET menu_order = $1 WHERE id = $2',
          [page.menuOrder, page.id]
        );
      }

      await client.query('COMMIT');
      res.json({ message: 'Pillar pages reordered successfully' });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error reordering pillar pages:', error);
    res.status(500).json({ error: 'Failed to reorder pillar pages' });
  }
});

module.exports = router;