const express = require('express');
const router = express.Router();
const aiServiceManager = require('../services/aiServiceManager');
const serperService = require('../services/serperService');
const RedditAnalyzer = require('../utils/redditAnalyzer');

// Helper function to fetch Serper autocomplete suggestions
async function fetchSerperAutocomplete(query) {
  try {
    const response = await fetch('https://google.serper.dev/autocomplete', {
      method: 'POST',
      headers: {
        'X-API-KEY': '9326837be69f9dbaa18dd2cd193d57df4dd2febc',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        q: query
      }),
      timeout: 10000
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    // Extract suggestions from Serper response format
    // Response format: { suggestions: [{ value: "suggestion1" }, { value: "suggestion2" }] }
    if (data && data.suggestions && Array.isArray(data.suggestions)) {
      return data.suggestions.map(item => item.value).slice(0, 10); // Return max 10 suggestions
    }

    return [];
  } catch (error) {
    console.error('Error fetching Serper autocomplete:', error);

    // Return fallback suggestions based on common patterns
    return generateFallbackSuggestions(query);
  }
}

// Fallback function to generate basic suggestions when Google API fails
function generateFallbackSuggestions(query) {
  const commonSuffixes = [
    'tips',
    'guide',
    'tutorial',
    'benefits',
    'how to',
    'best practices',
    'examples',
    'ideas',
    'strategies',
    'techniques'
  ];

  const commonPrefixes = [
    'best',
    'top',
    'how to',
    'what is',
    'why',
    'when to',
    'where to'
  ];

  const suggestions = [];

  // Add suffix-based suggestions
  commonSuffixes.forEach(suffix => {
    suggestions.push(`${query} ${suffix}`);
  });

  // Add prefix-based suggestions
  commonPrefixes.forEach(prefix => {
    suggestions.push(`${prefix} ${query}`);
  });

  // Return first 8 suggestions
  return suggestions.slice(0, 8);
}

// POST /api/ideation/suggestions
router.post('/suggestions', async (req, res) => {
  try {
    const { keywords, prompt, redditSources } = req.body;

    // Handle both old format (keywords only) and new format (keywords + prompt)
    if (prompt && typeof prompt === 'string' && keywords && Array.isArray(keywords)) {
      // New format: custom prompt with keywords
      const validKeywords = keywords.filter(keyword =>
        typeof keyword === 'string' && keyword.trim().length > 0
      );

      if (validKeywords.length === 0) {
        return res.status(400).json({
          error: 'At least one valid keyword is required'
        });
      }

      // Process Reddit sources if provided
      let redditInsights = null;
      if (redditSources && Array.isArray(redditSources) && redditSources.length > 0) {
        console.log(`🔗 Processing ${redditSources.length} Reddit sources for enhanced topic generation`);
        redditInsights = RedditAnalyzer.aggregateRedditInsights(redditSources);
      }

      // Debug: Print received data
      console.log('='.repeat(80));
      console.log('🔍 DEBUG: BACKEND RECEIVED DATA');
      console.log('='.repeat(80));
      console.log('📋 Keywords:', validKeywords);
      console.log('📝 Prompt length:', prompt.length, 'characters');
      console.log('📝 Prompt preview:', prompt.substring(0, 200) + '...');
      if (redditInsights) {
        console.log('🗨️ Reddit insights: ', redditInsights.total_sources, 'sources,', redditInsights.pain_points.length, 'pain points');
      }
      console.log('='.repeat(80));

      // Generate topics using database-stored clustered topics prompt
      try {
        let suggestions;
        if (prompt) {
          // Custom prompt from frontend - create a temporary prompt template
          console.log('⚠️ Using custom prompt from frontend (deprecated approach)');
          // For now, we'll ignore the custom prompt and use database prompt
          // This maintains compatibility while forcing database-driven approach
          suggestions = await aiServiceManager.generateClusteredTopics(validKeywords, redditInsights);
        } else {
          // Use database-stored clustered topics prompt (Step0KeywordResearch)
          suggestions = await aiServiceManager.generateClusteredTopics(validKeywords, redditInsights);
        }
        res.json(suggestions);
      } catch (aiError) {
        console.error('AI generation failed:', aiError.message);

        // Provide fallback topic suggestions based on keywords
        const fallbackSuggestions = generateFallbackTopics(validKeywords);

        return res.json({
          suggestions: fallbackSuggestions,
          generatedFromKeywords: validKeywords,
          totalCount: fallbackSuggestions.length,
          fallback: true,
          message: 'AI service temporarily unavailable. Here are some suggested topics based on your keywords.'
        });
      }
    } else if (keywords && Array.isArray(keywords)) {
      // Old format: keywords only
      const validKeywords = keywords.filter(keyword =>
        typeof keyword === 'string' && keyword.trim().length > 0
      );

      if (validKeywords.length === 0) {
        return res.status(400).json({
          error: 'At least one valid keyword is required'
        });
      }

      // Process Reddit sources if provided
      let redditInsights = null;
      if (redditSources && Array.isArray(redditSources) && redditSources.length > 0) {
        console.log(`🔗 Processing ${redditSources.length} Reddit sources for enhanced topic generation`);
        redditInsights = RedditAnalyzer.aggregateRedditInsights(redditSources);
      }

      // Generate topic suggestions using database-stored clustered topics prompt
      try {
        const suggestions = await aiServiceManager.generateClusteredTopics(validKeywords, redditInsights);
        res.json(suggestions);
      } catch (aiError) {
        console.error('AI generation failed:', aiError.message);
        return res.status(503).json({
          error: 'AI service unavailable',
          message: 'Unable to connect to AI service. Please check your internet connection and try again.',
          details: aiError.message
        });
      }
    } else {
      return res.status(400).json({
        error: 'Keywords array is required and must not be empty'
      });
    }
  } catch (error) {
    console.error('Error in /api/ideation/suggestions:', error);
    res.status(500).json({
      error: 'Failed to generate topic suggestions',
      message: error.message
    });
  }
});

// GET /api/ideation/autocomplete
router.get('/autocomplete', async (req, res) => {
  try {
    const { q } = req.query;

    // Validate input
    if (!q || typeof q !== 'string' || q.trim().length === 0) {
      return res.status(400).json({
        error: 'Query parameter "q" is required and must be a non-empty string'
      });
    }

    // Limit query length for safety
    const query = q.trim().slice(0, 100);

    // Fetch autocomplete suggestions
    const suggestions = await fetchSerperAutocomplete(query);

    res.json({
      query: query,
      suggestions: suggestions
    });
  } catch (error) {
    console.error('Error in /api/ideation/autocomplete:', error);
    res.status(500).json({
      error: 'Failed to fetch autocomplete suggestions',
      message: error.message
    });
  }
});

// POST /api/ideation/keyword-research
router.post('/keyword-research', async (req, res) => {
  try {
    const { keyword } = req.body;

    // Validate input
    if (!keyword || typeof keyword !== 'string' || keyword.trim().length === 0) {
      return res.status(400).json({
        error: 'Keyword is required and must be a non-empty string'
      });
    }

    // Limit keyword length for safety
    const cleanKeyword = keyword.trim().slice(0, 100);

    // Get both research data and autocomplete suggestions
    const [researchData, autocompleteSuggestions] = await Promise.all([
      serperService.getKeywordResearch(cleanKeyword),
      fetchSerperAutocomplete(cleanKeyword).catch(() => [])
    ]);

    // Add autocomplete suggestions to the research data
    const enhancedData = {
      ...researchData,
      autocompleteSuggestions: autocompleteSuggestions || []
    };

    res.json(enhancedData);
  } catch (error) {
    console.error('Error in /api/ideation/keyword-research:', error);
    res.status(500).json({
      error: 'Failed to fetch keyword research data',
      message: error.message
    });
  }
});

module.exports = router;
