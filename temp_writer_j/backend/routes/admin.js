const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const aiServiceManager = require('../services/aiServiceManager');
const database = require('../config/database');

const router = express.Router();

// Middleware to check admin role
const requireAdmin = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'Please log in to access this resource'
      });
    }

    // Check if user has admin role
    const user = await database.get(
      'SELECT role FROM users WHERE id = ?',
      [req.user.id]
    );

    if (!user || user.role !== 'admin') {
      return res.status(403).json({
        error: 'Admin access required',
        message: 'You do not have permission to access this resource'
      });
    }

    next();
  } catch (error) {
    console.error('Admin authorization error:', error);
    res.status(500).json({
      error: 'Authorization failed',
      message: 'An error occurred while checking permissions'
    });
  }
};

// All admin routes require authentication and admin role
router.use(authenticateToken);
router.use(requireAdmin);

// GET /api/admin/models - Get all AI models
router.get('/models', async (req, res) => {
  try {
    const models = await aiServiceManager.getAllModels();
    
    // Don't expose API keys in the response
    const safeModels = models.map(model => ({
      ...model,
      api_key: model.api_key ? '***HIDDEN***' : null,
      has_api_key: !!model.api_key
    }));

    res.json({
      models: safeModels
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    res.status(500).json({
      error: 'Failed to fetch models',
      message: error.message
    });
  }
});

// PUT /api/admin/models/:modelName - Update AI model configuration
router.put('/models/:modelName', async (req, res) => {
  try {
    const { modelName } = req.params;
    const { api_key, model_version, is_active, is_default } = req.body;

    // Validate input
    if (typeof is_active !== 'boolean' || typeof is_default !== 'boolean') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'is_active and is_default must be boolean values'
      });
    }

    if (model_version && typeof model_version !== 'string') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'model_version must be a string'
      });
    }

    const updates = {
      model_version: model_version || '',
      is_active,
      is_default
    };

    // Only update API key if provided and not empty/hidden
    if (api_key && api_key !== '***HIDDEN***' && api_key.trim() !== '') {
      updates.api_key = api_key;
    }

    const updatedModel = await aiServiceManager.updateModel(modelName, updates);

    if (!updatedModel) {
      return res.status(404).json({
        error: 'Model not found',
        message: `AI model '${modelName}' not found`
      });
    }

    // Reinitialize the service if API key was updated
    if (updates.api_key && updates.api_key !== '***HIDDEN***') {
      try {
        await aiServiceManager.ensureServiceInitialized(modelName);
        console.log(`Service ${modelName} reinitialized with new API key`);
      } catch (initError) {
        console.warn(`Failed to reinitialize ${modelName} service:`, initError.message);
      }
    }

    // Don't expose API key in response
    const safeModel = {
      ...updatedModel,
      api_key: updatedModel.api_key ? '***HIDDEN***' : null,
      has_api_key: !!updatedModel.api_key
    };

    res.json({
      message: 'Model updated successfully',
      model: safeModel
    });
  } catch (error) {
    console.error('Error updating model:', error);
    res.status(500).json({
      error: 'Failed to update model',
      message: error.message
    });
  }
});

// GET /api/admin/prompts - Get all prompt templates
router.get('/prompts', async (req, res) => {
  try {
    const prompts = await aiServiceManager.getAllPromptTemplates();

    res.json({
      prompts
    });
  } catch (error) {
    console.error('Error fetching prompts:', error);
    res.status(500).json({
      error: 'Failed to fetch prompts',
      message: error.message
    });
  }
});

// PUT /api/admin/prompts/:id - Update prompt template
router.put('/prompts/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { template_name, prompt_content, is_active } = req.body;

    // Validate input
    if (!template_name || typeof template_name !== 'string') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'template_name is required and must be a string'
      });
    }

    if (!prompt_content || typeof prompt_content !== 'string') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'prompt_content is required and must be a string'
      });
    }

    if (typeof is_active !== 'boolean') {
      return res.status(400).json({
        error: 'Invalid input',
        message: 'is_active must be a boolean value'
      });
    }

    const updates = {
      template_name: template_name.trim(),
      prompt_content: prompt_content.trim(),
      is_active
    };

    const updatedPrompt = await aiServiceManager.updatePromptTemplate(parseInt(id), updates);

    if (!updatedPrompt) {
      return res.status(404).json({
        error: 'Prompt template not found',
        message: `Prompt template with ID '${id}' not found`
      });
    }

    res.json({
      message: 'Prompt template updated successfully',
      prompt: updatedPrompt
    });
  } catch (error) {
    console.error('Error updating prompt template:', error);
    res.status(500).json({
      error: 'Failed to update prompt template',
      message: error.message
    });
  }
});

// GET /api/admin/active-model - Get currently active model
router.get('/active-model', async (req, res) => {
  try {
    const activeModelName = await aiServiceManager.getActiveModel();
    const modelConfig = await aiServiceManager.getModelConfig(activeModelName);

    if (!modelConfig) {
      return res.status(404).json({
        error: 'Active model not found',
        message: 'No active AI model configuration found'
      });
    }

    // Don't expose API key
    const safeModel = {
      ...modelConfig,
      api_key: modelConfig.api_key ? '***HIDDEN***' : null,
      has_api_key: !!modelConfig.api_key
    };

    res.json({
      activeModel: safeModel
    });
  } catch (error) {
    console.error('Error fetching active model:', error);
    res.status(500).json({
      error: 'Failed to fetch active model',
      message: error.message
    });
  }
});

// POST /api/admin/test-model/:modelName - Test AI model connection
router.post('/test-model/:modelName', async (req, res) => {
  try {
    const { modelName } = req.params;
    
    // Get model configuration
    const modelConfig = await aiServiceManager.getModelConfig(modelName);
    
    if (!modelConfig) {
      return res.status(404).json({
        error: 'Model not found',
        message: `AI model '${modelName}' not found`
      });
    }

    if (!modelConfig.api_key) {
      return res.status(400).json({
        error: 'API key missing',
        message: `API key not configured for model '${modelName}'`
      });
    }

    // Test the model with a simple topic generation
    const testKeywords = ['test', 'ai', 'connection'];
    
    try {
      // Initialize the specific service for testing
      await aiServiceManager.ensureServiceInitialized(modelName);

      // Get the service directly for testing
      const service = aiServiceManager.services[modelName];
      if (!service) {
        throw new Error(`Service not found for model: ${modelName}`);
      }

      // Test topic generation directly with the service
      const result = await service.generateTopicSuggestions(testKeywords);

      console.log(`✅ ${modelName} test successful:`, result);

      res.json({
        success: true,
        message: `Model '${modelName}' connection test successful`,
        testResult: {
          topicsGenerated: result.totalCount || 0,
          hasResponse: !!result.suggestions
        }
      });
    } catch (testError) {
      res.status(500).json({
        success: false,
        error: 'Model test failed',
        message: `Failed to connect to ${modelName}: ${testError.message}`
      });
    }
  } catch (error) {
    console.error('Error testing model:', error);
    res.status(500).json({
      error: 'Failed to test model',
      message: error.message
    });
  }
});

// GET /api/admin/stats - Get admin dashboard stats
router.get('/stats', async (req, res) => {
  try {
    const [totalUsers, totalTasks, activeModels, totalPrompts] = await Promise.all([
      database.get('SELECT COUNT(*) as count FROM users'),
      database.get('SELECT COUNT(*) as count FROM tasks'),
      database.get('SELECT COUNT(*) as count FROM ai_models WHERE is_active = ?', [true]),
      database.get('SELECT COUNT(*) as count FROM prompt_templates WHERE is_active = ?', [true])
    ]);

    res.json({
      stats: {
        totalUsers: totalUsers.count,
        totalTasks: totalTasks.count,
        activeModels: activeModels.count,
        totalPrompts: totalPrompts.count
      }
    });
  } catch (error) {
    console.error('Error fetching admin stats:', error);
    res.status(500).json({
      error: 'Failed to fetch stats',
      message: error.message
    });
  }
});

// GET /api/admin/tasks/export - Export complete tasks data as CSV
router.get('/tasks/export', async (req, res) => {
  try {
    // Get all tasks with user information
    const tasks = await database.all(`
      SELECT 
        t.id, 
        t.user_id,
        u.email,
        u.full_name,
        t.name,
        t.status,
        t.current_step,
        t.keywords,
        t.selected_topics,
        t.topic_suggestions,
        t.keyword_research_data,
        t.keyword_research_selections,
        t.sources,
        t.topic_sources,
        t.product_info,
        t.eeat_profile,
        t.output_parameters,
        t.generated_article,
        t.created_at,
        t.updated_at
      FROM tasks t
      LEFT JOIN users u ON t.user_id = u.id
      ORDER BY t.created_at DESC
    `);

    // Convert to CSV format with complete data
    const csvHeaders = [
      'Task ID',
      'User ID', 
      'Email',
      'Full Name',
      'Task Name',
      'Status',
      'Current Step',
      'Keywords (JSON)',
      'Selected Topics (JSON)',
      'Topic Suggestions (JSON)',
      'Keyword Research Data (JSON)',
      'Keyword Research Selections (JSON)',
      'Sources (JSON)',
      'Topic Sources (JSON)',
      'Product Info (JSON)',
      'EEAT Profile (JSON)',
      'Output Parameters (JSON)',
      'Generated Article',
      'Created At',
      'Updated At'
    ];

    const csvRows = tasks.map(task => {
      // Helper function to escape CSV fields
      const escapeCsvField = (field) => {
        const str = String(field || '');
        // Always wrap in quotes if field contains comma, quote, newline, or is empty
        if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r') || str === '') {
          return '"' + str.replace(/"/g, '""') + '"';
        }
        return str;
      };

      return [
        escapeCsvField(task.id),
        escapeCsvField(task.user_id),
        escapeCsvField(task.email || ''),
        escapeCsvField(task.full_name || ''),
        escapeCsvField(task.name),
        escapeCsvField(task.status),
        escapeCsvField(task.current_step),
        escapeCsvField(task.keywords || ''),
        escapeCsvField(task.selected_topics || ''),
        escapeCsvField(task.topic_suggestions || ''),
        escapeCsvField(task.keyword_research_data || ''),
        escapeCsvField(task.keyword_research_selections || ''),
        escapeCsvField(task.sources || ''),
        escapeCsvField(task.topic_sources || ''),
        escapeCsvField(task.product_info || ''),
        escapeCsvField(task.eeat_profile || ''),
        escapeCsvField(task.output_parameters || ''),
        escapeCsvField(task.generated_article || ''),
        escapeCsvField(task.created_at),
        escapeCsvField(task.updated_at)
      ];
    });

    // Combine headers and rows
    const csvContent = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');

    // Set response headers for file download
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `tasks_complete_export_${timestamp}.csv`;

    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Cache-Control', 'no-cache');

    // Add BOM for proper UTF-8 encoding in Excel
    const BOM = '\uFEFF';
    res.send(BOM + csvContent);
  } catch (error) {
    console.error('Error exporting tasks:', error);
    res.status(500).json({
      error: 'Failed to export tasks',
      message: error.message
    });
  }
});

module.exports = router;
