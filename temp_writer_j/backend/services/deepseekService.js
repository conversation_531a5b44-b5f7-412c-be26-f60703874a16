const database = require('../config/database');

class DeepSeekService {
  constructor() {
    this.client = null;
    this.initialized = false;
    this.currentApiKey = null;

    console.log('=== DEEPSEEK SERVICE DEBUG ===');
    console.log('DeepSeek service initialized - will load API key from database');
    console.log('===============================');
  }

  async getApiKeyFromDatabase() {
    try {
      const model = await database.get(
        'SELECT api_key FROM ai_models WHERE model_name = ? AND is_active = ?',
        ['deepseek', true]
      );
      return model ? model.api_key : null;
    } catch (error) {
      console.error('Error getting DeepSeek API key from database:', error);
      return null;
    }
  }

  async initializeAI(apiKey = null) {
    try {
      // Get API key from database if not provided
      const dbApiKey = apiKey || await this.getApiKeyFromDatabase();

      if (!dbApiKey) {
        console.error('No DeepSeek API key found in database');
        this.initialized = false;
        return;
      }

      // Only reinitialize if API key changed
      if (this.currentApiKey === dbApiKey && this.client) {
        this.initialized = true;
        return;
      }

      // Import OpenAI dynamically
      const { default: OpenAI } = await import('openai');

      // Initialize DeepSeek client using OpenAI SDK
      this.client = new OpenAI({
        baseURL: 'https://api.deepseek.com',
        apiKey: dbApiKey
      });

      this.currentApiKey = dbApiKey;
      this.initialized = true;
      console.log('DeepSeek AI initialized successfully with database API key');
    } catch (error) {
      console.error('Failed to initialize DeepSeek AI:', error);
      this.initialized = false;
    }
  }

  async ensureInitialized() {
    if (!this.initialized) {
      await this.initializeAI();
    }
  }

  async makeRequest(messages, model = 'deepseek-reasoner') {
    await this.ensureInitialized();

    if (!this.client) {
      throw new Error('DeepSeek API not initialized. Please check your API key and network connection.');
    }

    try {
      const completion = await this.client.chat.completions.create({
        model: model,
        messages: messages,
        temperature: 0.7,
        max_tokens: 4000
      });

      return completion.choices[0].message.content;
    } catch (error) {
      console.error('DeepSeek API request failed:', error);
      throw error;
    }
  }

  async generateTopicSuggestions(keywords) {
    // This method is deprecated - all prompts should now come from database
    // Keeping for backward compatibility only
    console.warn('⚠️ generateTopicSuggestions called - this method is deprecated. Use generateCustomTopics with database prompts instead.');

    // No fallback - force use of database prompts
    throw new Error('This method is deprecated. All prompts must come from database. Use generateCustomTopics instead.');
  }

  async generateCustomTopics(prompt, keywords) {
    try {
      console.log('Generating custom topics with DeepSeek for keywords:', keywords);

      // Debug: Print the complete prompt being sent to DeepSeek
      console.log('='.repeat(80));
      console.log('🤖 DEBUG: PROMPT SENT TO DEEPSEEK AI');
      console.log('='.repeat(80));
      console.log(prompt);
      console.log('='.repeat(80));
      console.log('🎯 Model:', 'deepseek-chat');
      console.log('📊 Keywords count:', keywords.length);
      console.log('='.repeat(80));

      const messages = [
        {
          role: 'user',
          content: prompt
        }
      ];

      console.log('🔄 Attempting to generate content...');
      const response = await this.makeRequest(messages, 'deepseek-reasoner');
      console.log('✅ Content generation successful');

      console.log('🤖 DeepSeek response received (first 500 chars):', response.substring(0, 500) + '...');
      console.log('📄 Full response length:', response.length);

      // Parse the clustered Markdown response
      const lines = response.split('\n').filter(line => line.trim());
      let clusteredTopics = {};
      let allTopics = [];
      let currentCluster = null;

      console.log('📋 Total lines to parse:', lines.length);

      for (const line of lines) {
        const trimmedLine = line.trim();

        // Check for H3 headers (cluster names) - be more flexible
        if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('## ') || trimmedLine.match(/^#{1,3}\s+/)) {
          currentCluster = trimmedLine.replace(/^#{1,3}\s+/, '').trim();
          clusteredTopics[currentCluster] = [];
          console.log('🏷️ Found cluster:', currentCluster);
        }
        // Check for numbered list items
        else if (/^\d+\./.test(trimmedLine) && currentCluster) {
          const topic = trimmedLine.replace(/^\d+\.\s*/, '').trim();
          if (topic.length > 0) {
            clusteredTopics[currentCluster].push(topic);
            allTopics.push(topic);
            console.log(`  ✅ Added topic to ${currentCluster}:`, topic.substring(0, 50) + '...');
          }
        }
        // Also check for bullet points as fallback
        else if (/^[-*]\s+/.test(trimmedLine) && currentCluster) {
          const topic = trimmedLine.replace(/^[-*]\s+/, '').trim();
          if (topic.length > 0) {
            clusteredTopics[currentCluster].push(topic);
            allTopics.push(topic);
            console.log(`  ✅ Added bullet topic to ${currentCluster}:`, topic.substring(0, 50) + '...');
          }
        }
      }

      console.log('🗂️ Final clustered topics:', Object.keys(clusteredTopics));
      console.log('📊 Cluster details:', Object.entries(clusteredTopics).map(([name, topics]) => `${name}: ${topics.length} topics`));

      // If no clusters found, fall back to simple numbered list parsing
      if (Object.keys(clusteredTopics).length === 0) {
        console.log('⚠️ No clusters found, falling back to simple parsing...');
        allTopics = lines
          .filter(line => /^\d+\./.test(line.trim()))
          .map(line => line.replace(/^\d+\.\s*/, '').trim())
          .filter(topic => topic.length > 0);
        console.log('📝 Fallback topics found:', allTopics.length);
      }

      console.log('📊 Final parsed topics:', allTopics.length);
      console.log('🗂️ Final clusters found:', Object.keys(clusteredTopics).length);

      return {
        suggestions: allTopics,
        clustered: clusteredTopics,
        generatedFromKeywords: keywords,
        totalCount: allTopics.length,
        hasClusters: Object.keys(clusteredTopics).length > 0,
        rawResponse: response // Include raw response for debugging
      };
    } catch (error) {
      console.error('Error generating custom topics:', error);
      throw new Error(`Failed to generate custom topics: ${error.message}`);
    }
  }

  async generateArticle(articleData) {
    // This method is deprecated - all prompts should now come from database
    // Keeping for backward compatibility only
    console.warn('⚠️ generateArticle called - this method is deprecated. Use generateArticleWithCustomPrompt with database prompts instead.');

    // No fallback - force use of database prompts
    throw new Error('This method is deprecated. All prompts must come from database. Use generateArticleWithCustomPrompt instead.');
  }

  async generateArticleWithCustomPrompt(articleData, promptTemplate) {
    try {
      const {
        topics, // EXPECTS AN ARRAY of 1 to 5 strings
        sources,
        productInfo,
        tonality,
        length,
        format,
        authorName,
        authorBio,
        targetAudience,
        articleGoal,
        primaryKeywords,
        secondaryKeywords,
        rawRedditData,
        comprehensiveResources,
      } = articleData;

      // Input Validation (Basic)
      if (!Array.isArray(topics) || topics.length === 0 || topics.length > 5) {
        throw new Error("Invalid input: 'topics' must be an array containing 1 to 5 topic strings.");
      }

      const wordCount = this.getWordCountFromLength(length);

      // --- Input Formatting for the Prompt ---
      const topicsText = `
### Core Topics to Synthesize (1 to ${topics.length}):
${topics.map((t, index) => `${index + 1}. ${t}`).join('\n')}`;

      // Debug keyword data
      console.log('=== KEYWORD DEBUG ===');
      console.log('Primary keywords received:', primaryKeywords);
      console.log('Secondary keywords received:', secondaryKeywords);
      console.log('Primary keywords length:', primaryKeywords ? primaryKeywords.length : 0);
      console.log('Secondary keywords length:', secondaryKeywords ? secondaryKeywords.length : 0);
      console.log('====================');

      const keywordsText = `
### Provided Keywords (Use these & derive others):
- Primary: ${primaryKeywords && primaryKeywords.length > 0 ? primaryKeywords.join(', ') : 'N/A'}
- Secondary/LSI: ${secondaryKeywords && secondaryKeywords.length > 0 ? secondaryKeywords.join(', ') : 'N/A'}`;

      const sourcesText = sources && sources.length > 0
        ? `\n\n### Reference Sources (Incorporate these facts/ideas):\n${sources.map((source, index) =>
          `${index + 1}. ${source.title || 'Source'}: ${source.content || source.url}`
        ).join('\n')}`
        : '';

      const productText = productInfo && productInfo.name
        ? `\n\n### Product Information (Integrate subtly as a solution/example):\n- Product: ${productInfo.name}\n- Description: ${productInfo.description}\n- Features: ${productInfo.features ? productInfo.features.join(', ') : 'N/A'}\n- Link: ${productInfo.link || 'N/A'}\n- **CRITICAL: Mention the product a maximum of 1-2 times.** If mentioned twice, the mentions should feel distinct and add unique value in each context.`
        : '';

      const eeatText = `
### E-E-A-T & Goal Focus:
- Author: ${authorName || 'N/A'}
- Expertise: ${authorBio || 'N/A'}
- Target Audience: ${targetAudience || 'General audience'}
- Article Goal: ${articleGoal || 'Educate, inform, and build trust'}`;

      // Format raw Reddit data if available for AI analysis
      const rawRedditText = rawRedditData && rawRedditData.length > 0 ? `

### Raw Reddit Data for AI Analysis:
${rawRedditData.map((post, index) => `
**Reddit Post ${index + 1}:**
- **Subreddit**: r/${post.subreddit}
- **Title**: "${post.title || 'No title'}"
- **Content**: ${post.content && post.content.trim() ? `"${post.content.substring(0, 500)}${post.content.length > 500 ? '...' : ''}"` : 'No text content (likely link post)'}
- **Author**: u/${post.author || 'unknown'}
- **Upvotes**: ${post.upvotes} | **Comments**: ${post.comments}
- **Engagement Score**: ${(post.upvotes || 0) + (post.comments || 0) * 2}
- **URL**: ${post.url}
${post.flair ? `- **Flair**: ${post.flair}` : ''}
`).join('\n')}

**AI ANALYSIS INSTRUCTIONS:** 
Analyze the Reddit posts above to extract:
1. **Real User Pain Points**: What problems and frustrations do users express?
2. **Trending Topics**: What themes and discussions are most common?
3. **User Language**: How do real users talk about these topics?
4. **Solution Gaps**: What solutions are users seeking but not finding?
5. **Community Insights**: What unique perspectives emerge from these communities?

Use these insights naturally throughout your article to make it more authentic and user-focused. Reference real user concerns and validate problems with actual community discussions.` : '';

      // Format comprehensive resources if available
      const comprehensiveResourcesText = comprehensiveResources && comprehensiveResources.length > 0 ? `

### Comprehensive Resources (Evaluate for Quality & Relevance):
${comprehensiveResources.map((resource, index) => {
  if (resource.type === 'url') {
    return `${index + 1}. **URL Resource**: ${resource.title}
   - URL: ${resource.url}
   - Description: ${resource.description || 'N/A'}
   - Content Preview: ${(resource.content || '').substring(0, 300)}${resource.content && resource.content.length > 300 ? '...' : ''}
   - Word Count: ${resource.wordCount} words`;
  } else if (resource.type === 'text') {
    return `${index + 1}. **Text Block**: ${resource.title}
   - Content: ${resource.content}
   - Word Count: ${resource.wordCount} words`;
  }
  return `${index + 1}. **Resource**: ${resource.title || 'Untitled'}`;
}).join('\n\n')}

**INTEGRATION INSTRUCTIONS:** Evaluate each resource above for:
1. **Relevance**: Does it support your central thesis? (High/Medium/Low)
2. **Quality**: Is the information credible and valuable? (High/Medium/Low) 
3. **Fit**: Can it be integrated naturally without forcing? (Good Fit/Forced/Poor Fit)

**ONLY USE** resources that score High/Medium relevance + High/Medium quality + Good Fit. Integrate them seamlessly as supporting evidence, expert insights, or validation for your arguments. Do not force integration if resources don't naturally enhance your narrative.` : '';

      // Build the article inputs section
      const articleInputs = `
### Key Concepts & Angles to Cover:
${topicsText}

${keywordsText}

### Article Properties:
- Target Word Count: ~${wordCount} words
- Tone: ${tonality}
- Format: ${format} (Use Markdown if 'markdown')
${eeatText}
${sourcesText}
${productText}
${rawRedditText}
${comprehensiveResourcesText}`;

      // Replace the placeholder in the prompt template
      const finalPrompt = promptTemplate.replace('{article_inputs}', articleInputs);

      // Debug: Show the final prompt being sent to DeepSeek
      console.log('=== DEEPSEEK ARTICLE GENERATION DEBUG ===');
      console.log('🤖 Using database-stored prompt template');
      console.log('📊 Topics count:', topics.length);
      console.log('📄 Final prompt length:', finalPrompt.length);
      console.log('='.repeat(50));

      const messages = [
        {
          role: 'user',
          content: finalPrompt
        }
      ];

      const response = await this.makeRequest(messages, 'deepseek-reasoner');
      return response;
    } catch (error) {
      console.error('Error generating article with custom prompt:', error);
      throw new Error(`Failed to generate article: ${error.message}`);
    }
  }

  getWordCountFromLength(length) {
    const lengthMap = {
      'snippet': 500,
      'short_post': 800,       // Corresponds to 'Short Post'
      'medium_article': 1500,  // Corresponds to 'Medium Article'
      'long_guide': 2500,      // Corresponds to 'Long-Form Guide'
      'pillar_module': 700     // Corresponds to 'Pillar Page Module'
    };

    const selectedKey = typeof length === 'string' ? length.toLowerCase() : 'medium_article';
    return lengthMap[selectedKey] || 1500;
  }

  // Legacy method removed - AI now analyzes raw Reddit data directly

  extractListFromText(text, type) {
    const lines = text.split('\n');
    const items = [];

    for (const line of lines) {
      if (line.trim() && (line.includes('?') || line.includes(type))) {
        const cleaned = line.replace(/^\d+\.?\s*/, '').replace(/^[-*]\s*/, '').trim();
        if (cleaned && !items.includes(cleaned)) {
          items.push(cleaned);
        }
      }
    }

    return items.slice(0, 5);
  }
}

module.exports = new DeepSeekService();
