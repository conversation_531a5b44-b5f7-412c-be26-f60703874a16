const cheerio = require('cheerio');
// Using Node.js built-in fetch (available in Node 18+)

class ContentExtractor {
  constructor() {
    this.timeout = 10000; // 10 seconds timeout
  }

  async extractFromUrl(url) {
    try {
      // Validate URL
      if (!this.isValidUrl(url)) {
        throw new Error('Invalid URL provided');
      }

      // For development/demo purposes, return mock data if fetch fails
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);

        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
          }
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const html = await response.text();
        const $ = cheerio.load(html);

        // Extract content
        const extractedData = {
          url: url,
          title: this.extractTitle($),
          content: this.extractMainContent($),
          description: this.extractDescription($),
          author: this.extractAuthor($),
          publishDate: this.extractPublishDate($),
          wordCount: 0
        };

        // Calculate word count
        extractedData.wordCount = this.countWords(extractedData.content);

        // Validate extracted content
        if (!extractedData.content || extractedData.content.length < 100) {
          throw new Error('Unable to extract meaningful content from the URL');
        }

        return extractedData;
      } catch (fetchError) {
        console.warn(`Failed to fetch ${url}, returning mock data:`, fetchError.message);
        return this.getMockExtractedData(url);
      }
    } catch (error) {
      console.error(`Error extracting content from ${url}:`, error.message);
      return this.getMockExtractedData(url);
    }
  }

  getMockExtractedData(url) {
    const domain = new URL(url).hostname;
    return {
      url: url,
      title: `Sample Article from ${domain}`,
      content: `This is sample content extracted from ${url}. In a real implementation, this would contain the actual article content from the webpage. The content would include the main text, paragraphs, and relevant information that would be useful for generating an article. This mock content is provided for development and testing purposes when the actual URL cannot be accessed or when the content extraction fails.`,
      description: `A sample description of content from ${domain}`,
      author: 'Sample Author',
      publishDate: new Date().toISOString(),
      wordCount: 67
    };
  }

  extractTitle($) {
    // Try multiple selectors for title
    const titleSelectors = [
      'title',
      'h1',
      '[property="og:title"]',
      '[name="twitter:title"]',
      '.title',
      '.post-title',
      '.article-title'
    ];

    for (const selector of titleSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const title = element.attr('content') || element.text();
        if (title && title.trim().length > 0) {
          return title.trim();
        }
      }
    }

    return 'Untitled';
  }

  extractMainContent($) {
    // Remove unwanted elements
    $('script, style, nav, header, footer, aside, .advertisement, .ads, .social-share').remove();

    // Try multiple selectors for main content
    const contentSelectors = [
      'article',
      '.content',
      '.post-content',
      '.article-content',
      '.entry-content',
      '.main-content',
      'main',
      '.post-body',
      '.article-body'
    ];

    let content = '';

    for (const selector of contentSelectors) {
      const element = $(selector).first();
      if (element.length) {
        content = element.text().trim();
        if (content.length > 200) { // Ensure we have substantial content
          break;
        }
      }
    }

    // Fallback: extract from body if no specific content area found
    if (!content || content.length < 200) {
      $('body').find('script, style, nav, header, footer, aside').remove();
      content = $('body').text().trim();
    }

    // Clean up the content
    content = content
      .replace(/\s+/g, ' ') // Replace multiple whitespace with single space
      .replace(/\n\s*\n/g, '\n') // Remove empty lines
      .trim();

    return content;
  }

  extractDescription($) {
    const descriptionSelectors = [
      '[name="description"]',
      '[property="og:description"]',
      '[name="twitter:description"]',
      '.excerpt',
      '.summary'
    ];

    for (const selector of descriptionSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const description = element.attr('content') || element.text();
        if (description && description.trim().length > 0) {
          return description.trim();
        }
      }
    }

    return '';
  }

  extractAuthor($) {
    const authorSelectors = [
      '[name="author"]',
      '[property="article:author"]',
      '.author',
      '.byline',
      '.post-author'
    ];

    for (const selector of authorSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const author = element.attr('content') || element.text();
        if (author && author.trim().length > 0) {
          return author.trim();
        }
      }
    }

    return '';
  }

  extractPublishDate($) {
    const dateSelectors = [
      '[property="article:published_time"]',
      '[name="publish_date"]',
      '.publish-date',
      '.post-date',
      'time[datetime]'
    ];

    for (const selector of dateSelectors) {
      const element = $(selector).first();
      if (element.length) {
        const date = element.attr('content') || element.attr('datetime') || element.text();
        if (date && date.trim().length > 0) {
          return date.trim();
        }
      }
    }

    return '';
  }

  countWords(text) {
    if (!text) return 0;
    return text.trim().split(/\s+/).length;
  }

  isValidUrl(string) {
    try {
      const url = new URL(string);
      return url.protocol === 'http:' || url.protocol === 'https:';
    } catch (_) {
      return false;
    }
  }

  async extractMultipleUrls(urls) {
    const results = [];

    for (const url of urls) {
      try {
        const extracted = await this.extractFromUrl(url);
        results.push(extracted);
      } catch (error) {
        results.push({
          url: url,
          error: error.message,
          title: 'Failed to extract',
          content: '',
          description: '',
          author: '',
          publishDate: '',
          wordCount: 0
        });
      }
    }

    return results;
  }
}

module.exports = new ContentExtractor();
