version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: writer-j-postgres
    environment:
      POSTGRES_DB: writer_j_dev
      POSTGRES_USER: writer_j_user
      POSTGRES_PASSWORD: writer_j_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U writer_j_user -d writer_j_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local