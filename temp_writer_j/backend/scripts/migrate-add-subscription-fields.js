require('dotenv').config();
const database = require('../config/database');

async function addSubscriptionFields() {
  try {
    await database.connect();
    
    console.log('Adding subscription management fields...');
    
    // Add subscription fields to users table
    const subscriptionFields = [
      // Paddle subscription fields
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS paddle_customer_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS paddle_subscription_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_status VARCHAR(50) DEFAULT \'free\'', // free, active, past_due, canceled, trialing
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_current_period_start TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS subscription_current_period_end TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMP',
      
      // Usage tracking fields
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS monthly_article_count INTEGER DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS monthly_reset_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS total_article_count INTEGER DEFAULT 0'
    ];
    
    for (const sql of subscriptionFields) {
      try {
        await database.run(sql);
        console.log('✓ Executed:', sql);
      } catch (error) {
        if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
          console.log('✓ Column already exists:', sql);
        } else {
          console.error('✗ Error executing:', sql, error.message);
        }
      }
    }
    
    // Create subscription events table for tracking Paddle webhooks
    const createSubscriptionEventsTable = database.isPostgres 
      ? `CREATE TABLE IF NOT EXISTS subscription_events (
          id SERIAL PRIMARY KEY,
          user_id INTEGER,
          paddle_event_id VARCHAR(255) UNIQUE NOT NULL,
          event_type VARCHAR(100) NOT NULL,
          subscription_id VARCHAR(255),
          customer_id VARCHAR(255),
          event_data JSONB,
          processed BOOLEAN DEFAULT FALSE,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`
      : `CREATE TABLE IF NOT EXISTS subscription_events (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER,
          paddle_event_id TEXT UNIQUE NOT NULL,
          event_type TEXT NOT NULL,
          subscription_id TEXT,
          customer_id TEXT,
          event_data TEXT,
          processed BOOLEAN DEFAULT FALSE,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`;
    
    await database.run(createSubscriptionEventsTable);
    console.log('✓ Created subscription_events table');
    
    // Create usage logs table for detailed tracking
    const createUsageLogsTable = database.isPostgres
      ? `CREATE TABLE IF NOT EXISTS usage_logs (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          action_type VARCHAR(50) NOT NULL, -- 'article_generation', 'keyword_research', etc.
          resource_consumed INTEGER DEFAULT 1,
          metadata JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`
      : `CREATE TABLE IF NOT EXISTS usage_logs (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          action_type TEXT NOT NULL,
          resource_consumed INTEGER DEFAULT 1,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`;
    
    await database.run(createUsageLogsTable);
    console.log('✓ Created usage_logs table');
    
    // Create indexes for better performance
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_paddle_customer ON users(paddle_customer_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_paddle_subscription ON users(paddle_subscription_id)',
      'CREATE INDEX IF NOT EXISTS idx_users_subscription_status ON users(subscription_status)',
      'CREATE INDEX IF NOT EXISTS idx_subscription_events_paddle_id ON subscription_events(paddle_event_id)',
      'CREATE INDEX IF NOT EXISTS idx_usage_logs_user_action ON usage_logs(user_id, action_type)',
      'CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON usage_logs(created_at)'
    ];
    
    for (const index of indexes) {
      try {
        await database.run(index);
        console.log('✓ Created index:', index);
      } catch (error) {
        console.log('✓ Index already exists or error:', error.message);
      }
    }
    
    console.log('✅ Subscription fields migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await database.close();
  }
}

if (require.main === module) {
  addSubscriptionFields();
}

module.exports = addSubscriptionFields;