const database = require('../config/database');

async function deployMigration() {
  console.log('🚀 Running production database migration...');
  console.log('Environment:', process.env.NODE_ENV);
  console.log('Database URL exists:', !!process.env.DATABASE_URL);
  console.log('Is PostgreSQL:', process.env.DATABASE_URL && process.env.DATABASE_URL.startsWith('postgresql://'));
  
  try {
    // Connect to database
    await database.connect();
    console.log('✅ Connected to production database');
    
    let migrationsNeeded = [];
    
    // Check if role column already exists
    try {
      const testQuery = 'SELECT role FROM users LIMIT 1';
      await database.get(testQuery);
      console.log('✅ Role column already exists');
    } catch (error) {
      if (error.message.includes('column "role" does not exist') || error.message.includes('no such column: role')) {
        console.log('📝 Role column not found, will add it...');
        migrationsNeeded.push('role');
      } else {
        console.error('❌ Unexpected error checking role column:', error);
        throw error;
      }
    }
    
    // Check if reddit_sources column already exists
    try {
      const testQuery = 'SELECT reddit_sources FROM tasks LIMIT 1';
      await database.get(testQuery);
      console.log('✅ Reddit_sources column already exists');
    } catch (error) {
      if (error.message.includes('column "reddit_sources" does not exist') || error.message.includes('no such column: reddit_sources')) {
        console.log('📝 Reddit_sources column not found, will add it...');
        migrationsNeeded.push('reddit_sources');
      } else {
        console.error('❌ Unexpected error checking reddit_sources column:', error);
        throw error;
      }
    }
    
    // Check if comprehensive_resources column already exists
    try {
      const testQuery = 'SELECT comprehensive_resources FROM tasks LIMIT 1';
      await database.get(testQuery);
      console.log('✅ Comprehensive_resources column already exists');
    } catch (error) {
      if (error.message.includes('column "comprehensive_resources" does not exist') || error.message.includes('no such column: comprehensive_resources')) {
        console.log('📝 Comprehensive_resources column not found, will add it...');
        migrationsNeeded.push('comprehensive_resources');
      } else {
        console.error('❌ Unexpected error checking comprehensive_resources column:', error);
        throw error;
      }
    }
    
    if (migrationsNeeded.length === 0) {
      console.log('✅ All columns exist, no migration needed');
      await database.close();
      return;
    }
    
    // Run needed migrations
    for (const migration of migrationsNeeded) {
      if (migration === 'role') {
        // Add role column to users table
        if (database.isPostgres) {
          console.log('🔧 Adding role column to PostgreSQL users table...');
          await database.run('ALTER TABLE users ADD COLUMN role VARCHAR(20) DEFAULT \'user\'');
        } else {
          console.log('🔧 Adding role column to SQLite users table...');
          await database.run('ALTER TABLE users ADD COLUMN role TEXT DEFAULT \'user\'');
        }
        console.log('✅ Role column added successfully');
        
        // Verify the column was added
        try {
          await database.get('SELECT role FROM users LIMIT 1');
          console.log('✅ Role column verified');
        } catch (error) {
          console.error('❌ Failed to verify role column:', error);
          throw error;
        }
      }
      
      if (migration === 'reddit_sources') {
        // Add reddit_sources column to tasks table
        if (database.isPostgres) {
          console.log('🔧 Adding reddit_sources column to PostgreSQL tasks table...');
          await database.run('ALTER TABLE tasks ADD COLUMN reddit_sources TEXT');
        } else {
          console.log('🔧 Adding reddit_sources column to SQLite tasks table...');
          await database.run('ALTER TABLE tasks ADD COLUMN reddit_sources TEXT');
        }
        console.log('✅ Reddit_sources column added successfully');
        
        // Verify the column was added
        try {
          await database.get('SELECT reddit_sources FROM tasks LIMIT 1');
          console.log('✅ Reddit_sources column verified');
        } catch (error) {
          console.error('❌ Failed to verify reddit_sources column:', error);
          throw error;
        }
      }
      
      if (migration === 'comprehensive_resources') {
        // Add comprehensive_resources column to tasks table
        if (database.isPostgres) {
          console.log('🔧 Adding comprehensive_resources column to PostgreSQL tasks table...');
          await database.run('ALTER TABLE tasks ADD COLUMN comprehensive_resources TEXT');
        } else {
          console.log('🔧 Adding comprehensive_resources column to SQLite tasks table...');
          await database.run('ALTER TABLE tasks ADD COLUMN comprehensive_resources TEXT');
        }
        console.log('✅ Comprehensive_resources column added successfully');
        
        // Verify the column was added
        try {
          await database.get('SELECT comprehensive_resources FROM tasks LIMIT 1');
          console.log('✅ Comprehensive_resources column verified');
        } catch (error) {
          console.error('❌ Failed to verify comprehensive_resources column:', error);
          throw error;
        }
      }
    }
    
    // Close database connection
    await database.close();
    console.log('🎉 All migrations completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail
    });
    process.exit(1);
  }
}

// Run the migration
deployMigration();
