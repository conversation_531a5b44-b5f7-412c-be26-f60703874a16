require('dotenv').config();
const database = require('../config/database');

async function enhanceSubscriptionSystem() {
  try {
    await database.connect();
    
    console.log('🚀 Enhancing subscription system with trial period and billing features...');
    
    // 更新用户表，添加试用期和计费字段
    const enhancedUserFields = [
      // 试用期管理
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS trial_starts_at TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS trial_ends_at TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS is_in_trial BOOLEAN DEFAULT FALSE',
      
      // 计费周期管理
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS billing_interval VARCHAR(20) DEFAULT \'month\'', // 'month' or 'year'
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS next_billing_date TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS current_period_start TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS current_period_end TIMESTAMP',
      
      // 取消和状态管理
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS cancellation_effective_date TIMESTAMP',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS cancel_reason VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS payment_status VARCHAR(50) DEFAULT \'free\'', // free, trial, confirmed, failed, cancelled
      
      // Paddle详细信息
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS paddle_product_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS paddle_price_id VARCHAR(255)',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_paddle_event_id VARCHAR(255)',
      
      // 计费重试
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS billing_retry_count INTEGER DEFAULT 0',
      'ALTER TABLE users ADD COLUMN IF NOT EXISTS last_billing_attempt TIMESTAMP'
    ];
    
    for (const sql of enhancedUserFields) {
      try {
        await database.run(sql);
        console.log('✓ Enhanced:', sql.split('ADD COLUMN IF NOT EXISTS')[1]?.split(' ')[0]);
      } catch (error) {
        if (error.message.includes('already exists') || error.message.includes('duplicate column')) {
          console.log('✓ Column already exists');
        } else {
          console.error('✗ Error:', error.message);
        }
      }
    }
    
    // 创建支付记录表
    const createPaymentsTable = database.isPostgres 
      ? `CREATE TABLE IF NOT EXISTS payments (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          subscription_id VARCHAR(255),
          
          -- Paddle信息
          paddle_transaction_id VARCHAR(255) UNIQUE,
          paddle_invoice_id VARCHAR(255),
          paddle_receipt_url VARCHAR(500),
          
          -- 支付详情
          amount DECIMAL(10,2),
          currency VARCHAR(10) DEFAULT 'USD',
          status VARCHAR(50) DEFAULT 'pending', -- pending, completed, failed, cancelled, refunded
          
          -- 计费周期
          period_start TIMESTAMP,
          period_end TIMESTAMP,
          
          -- 支付类型
          payment_type VARCHAR(50), -- trial_conversion, recurring, one_time, refund
          
          -- 失败重试
          failure_reason TEXT,
          retry_count INTEGER DEFAULT 0,
          next_retry_at TIMESTAMP,
          
          -- 税费
          tax_amount DECIMAL(10,2) DEFAULT 0,
          net_amount DECIMAL(10,2),
          
          -- 元数据
          paddle_event_data JSONB,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`
      : `CREATE TABLE IF NOT EXISTS payments (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          subscription_id TEXT,
          
          -- Paddle信息
          paddle_transaction_id TEXT UNIQUE,
          paddle_invoice_id TEXT,
          paddle_receipt_url TEXT,
          
          -- 支付详情
          amount REAL,
          currency TEXT DEFAULT 'USD',
          status TEXT DEFAULT 'pending',
          
          -- 计费周期
          period_start DATETIME,
          period_end DATETIME,
          
          -- 支付类型
          payment_type TEXT,
          
          -- 失败重试
          failure_reason TEXT,
          retry_count INTEGER DEFAULT 0,
          next_retry_at DATETIME,
          
          -- 税费
          tax_amount REAL DEFAULT 0,
          net_amount REAL,
          
          -- 元数据
          paddle_event_data TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`;
    
    await database.run(createPaymentsTable);
    console.log('✓ Created payments table');
    
    // 创建订阅快照表（用于记录订阅状态变更历史）
    const createSubscriptionHistoryTable = database.isPostgres
      ? `CREATE TABLE IF NOT EXISTS subscription_history (
          id SERIAL PRIMARY KEY,
          user_id INTEGER NOT NULL,
          paddle_subscription_id VARCHAR(255),
          
          -- 状态变更
          from_status VARCHAR(50),
          to_status VARCHAR(50),
          from_plan VARCHAR(50),
          to_plan VARCHAR(50),
          
          -- 变更原因
          change_reason VARCHAR(100), -- trial_start, trial_end, payment_success, payment_failed, user_cancel, etc.
          change_data JSONB,
          
          -- 时间戳
          effective_date TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`
      : `CREATE TABLE IF NOT EXISTS subscription_history (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          user_id INTEGER NOT NULL,
          paddle_subscription_id TEXT,
          
          -- 状态变更
          from_status TEXT,
          to_status TEXT,
          from_plan TEXT,
          to_plan TEXT,
          
          -- 变更原因
          change_reason TEXT,
          change_data TEXT,
          
          -- 时间戳
          effective_date DATETIME,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          
          FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
        )`;
    
    await database.run(createSubscriptionHistoryTable);
    console.log('✓ Created subscription_history table');
    
    // 创建新的索引
    const newIndexes = [
      'CREATE INDEX IF NOT EXISTS idx_users_trial_ends_at ON users(trial_ends_at)',
      'CREATE INDEX IF NOT EXISTS idx_users_payment_status ON users(payment_status)',
      'CREATE INDEX IF NOT EXISTS idx_users_next_billing_date ON users(next_billing_date)',
      'CREATE INDEX IF NOT EXISTS idx_payments_user_id ON payments(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status)',
      'CREATE INDEX IF NOT EXISTS idx_payments_paddle_transaction ON payments(paddle_transaction_id)',
      'CREATE INDEX IF NOT EXISTS idx_subscription_history_user_id ON subscription_history(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_subscription_history_change_reason ON subscription_history(change_reason)'
    ];
    
    for (const index of newIndexes) {
      try {
        await database.run(index);
        console.log('✓ Created index:', index.split(' ON ')[0].split(' ')[-1]);
      } catch (error) {
        console.log('✓ Index already exists or error:', error.message);
      }
    }
    
    console.log('✅ Enhanced subscription system migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await database.close();
  }
}

if (require.main === module) {
  enhanceSubscriptionSystem();
}

module.exports = enhanceSubscriptionSystem;