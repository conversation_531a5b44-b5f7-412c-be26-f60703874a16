const database = require('../config/database');
const aiServiceManager = require('../services/aiServiceManager');

async function testDatabaseApiKeys() {
  console.log('🧪 Testing Database-driven API Keys...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Initialize AI service manager
    await aiServiceManager.initialize();
    
    console.log('\n📊 Current AI Models Configuration:');
    const models = await aiServiceManager.getAllModels();
    
    models.forEach(model => {
      console.log(`- ${model.model_name.toUpperCase()}:`);
      console.log(`  Provider: ${model.provider}`);
      console.log(`  Version: ${model.model_version}`);
      console.log(`  Active: ${model.is_active ? '✅' : '❌'}`);
      console.log(`  Default: ${model.is_default ? '✅' : '❌'}`);
      console.log(`  API Key: ${model.api_key ? '✅ Configured' : '❌ Missing'}`);
      console.log('');
    });
    
    // Test API key retrieval
    console.log('🔑 Testing API Key Retrieval:');
    
    for (const model of models) {
      if (model.is_active) {
        console.log(`\n🔍 Testing ${model.model_name}...`);
        
        try {
          const service = aiServiceManager.services[model.model_name];
          if (service) {
            const apiKey = await service.getApiKeyFromDatabase();
            console.log(`  API Key from DB: ${apiKey ? '✅ Retrieved' : '❌ Not found'}`);
            
            if (apiKey) {
              console.log(`  API Key length: ${apiKey.length} characters`);
              console.log(`  API Key preview: ${apiKey.substring(0, 10)}...`);
              
              // Test service initialization
              try {
                await service.initializeAI(apiKey);
                console.log(`  Service initialization: ${service.initialized ? '✅ Success' : '❌ Failed'}`);
              } catch (initError) {
                console.log(`  Service initialization: ❌ Failed - ${initError.message}`);
              }
            }
          } else {
            console.log(`  Service: ❌ Not found`);
          }
        } catch (error) {
          console.log(`  Error: ❌ ${error.message}`);
        }
      }
    }
    
    // Test updating API key via admin interface simulation
    console.log('\n🔧 Testing API Key Update:');
    
    const testModel = models.find(m => m.model_name === 'gemini');
    if (testModel) {
      console.log(`\n📝 Updating ${testModel.model_name} API key...`);
      
      const originalApiKey = testModel.api_key;
      const testApiKey = 'test_api_key_12345';
      
      try {
        // Update API key
        await aiServiceManager.updateModel(testModel.model_name, {
          api_key: testApiKey,
          model_version: testModel.model_version,
          is_active: testModel.is_active,
          is_default: testModel.is_default
        });
        
        console.log('  ✅ API key updated in database');
        
        // Verify the update
        const updatedModel = await aiServiceManager.getModelConfig(testModel.model_name);
        console.log(`  ✅ Verification: ${updatedModel.api_key === testApiKey ? 'Success' : 'Failed'}`);
        
        // Test service reinitialization
        try {
          await aiServiceManager.ensureServiceInitialized(testModel.model_name);
          console.log('  ✅ Service reinitialized with new API key');
        } catch (reinitError) {
          console.log(`  ❌ Service reinitialization failed: ${reinitError.message}`);
        }
        
        // Restore original API key
        await aiServiceManager.updateModel(testModel.model_name, {
          api_key: originalApiKey,
          model_version: testModel.model_version,
          is_active: testModel.is_active,
          is_default: testModel.is_default
        });
        
        console.log('  ✅ Original API key restored');
        
      } catch (updateError) {
        console.log(`  ❌ Update failed: ${updateError.message}`);
      }
    }
    
    // Test prompt templates
    console.log('\n📝 Testing Prompt Templates:');
    
    const prompts = await aiServiceManager.getAllPromptTemplates();
    console.log(`Found ${prompts.length} prompt templates:`);
    
    prompts.forEach(prompt => {
      console.log(`- ${prompt.model_name.toUpperCase()} - ${prompt.template_type}: ${prompt.template_name}`);
      console.log(`  Active: ${prompt.is_active ? '✅' : '❌'}`);
      console.log(`  Length: ${prompt.prompt_content.length} characters`);
    });
    
    // Close database connection
    await database.close();
    console.log('\n🎉 Database API Keys test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testDatabaseApiKeys();
