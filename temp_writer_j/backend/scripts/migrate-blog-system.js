const fs = require('fs');
const path = require('path');

// Database migration script for blog system
const blogMigration = `
-- Blog Posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE NOT NULL,
    content TEXT,
    excerpt TEXT,
    author <PERSON><PERSON><PERSON><PERSON>(255),
    cover_image VARCHAR(500),
    category VARCHAR(100),
    tags TEXT[],
    read_time INTEGER DEFAULT 0,
    published BOOLEAN DEFAULT false,
    featured BOOLEAN DEFAULT false,
    seo_title VARCHAR(500),
    seo_description TEXT,
    seo_keywords TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blog Categories table
CREATE TABLE IF NOT EXISTS blog_categories (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL UNIQUE,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Blog Images table
CREATE TABLE IF NOT EXISTS blog_images (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    thumbnail_url VARCHAR(500),
    category VARCHAR(100),
    tags TEXT[],
    photographer VARCHAR(255),
    source VARCHAR(255),
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Pillar Pages table
CREATE TABLE IF NOT EXISTS pillar_pages (
    id SERIAL PRIMARY KEY,
    title VARCHAR(500) NOT NULL,
    slug VARCHAR(500) UNIQUE NOT NULL,
    content TEXT,
    excerpt TEXT,
    author VARCHAR(255),
    cover_image VARCHAR(500),
    category VARCHAR(100),
    menu_order INTEGER DEFAULT 0,
    cluster_pages JSONB DEFAULT '[]',
    published BOOLEAN DEFAULT false,
    featured BOOLEAN DEFAULT false,
    seo_title VARCHAR(500),
    seo_description TEXT,
    seo_keywords TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug);
CREATE INDEX IF NOT EXISTS idx_blog_posts_published ON blog_posts(published);
CREATE INDEX IF NOT EXISTS idx_blog_posts_category ON blog_posts(category);
CREATE INDEX IF NOT EXISTS idx_blog_posts_created_at ON blog_posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_blog_posts_featured ON blog_posts(featured);

CREATE INDEX IF NOT EXISTS idx_blog_categories_slug ON blog_categories(slug);

CREATE INDEX IF NOT EXISTS idx_blog_images_category ON blog_images(category);
CREATE INDEX IF NOT EXISTS idx_blog_images_usage_count ON blog_images(usage_count DESC);

CREATE INDEX IF NOT EXISTS idx_pillar_pages_slug ON pillar_pages(slug);
CREATE INDEX IF NOT EXISTS idx_pillar_pages_published ON pillar_pages(published);
CREATE INDEX IF NOT EXISTS idx_pillar_pages_category ON pillar_pages(category);
CREATE INDEX IF NOT EXISTS idx_pillar_pages_menu_order ON pillar_pages(menu_order);

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_blog_posts_updated_at BEFORE UPDATE ON blog_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pillar_pages_updated_at BEFORE UPDATE ON pillar_pages FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default categories
INSERT INTO blog_categories (name, slug, description) VALUES
('AI Writing', 'ai-writing', 'Articles about AI-powered content creation and writing tools'),
('Content Marketing', 'content-marketing', 'Strategies and tips for effective content marketing'),
('SEO', 'seo', 'Search engine optimization techniques and best practices'),
('Productivity', 'productivity', 'Tips and tools for improving writing productivity'),
('Tutorials', 'tutorials', 'Step-by-step guides and how-to articles'),
('News & Updates', 'news-updates', 'Latest news and product updates')
ON CONFLICT (slug) DO NOTHING;

-- Insert sample blog post
INSERT INTO blog_posts (
    title, 
    slug, 
    content, 
    excerpt, 
    author, 
    category, 
    tags, 
    read_time, 
    published, 
    featured,
    seo_title,
    seo_description,
    seo_keywords
) VALUES (
    'Getting Started with Writer J: Your 7-Step Guide to Better Articles',
    'getting-started-writer-j-7-step-guide',
    '# Getting Started with Writer J: Your 7-Step Guide to Better Articles

Writing great articles can be challenging, especially when you''re staring at a blank page. That''s where Writer J comes in with our revolutionary 7-step process that guides you from keyword research to publication-ready content.

## Why the 7-Step Process Works

Most AI writing tools give you a blank page and expect magic to happen. Writer J is different. We''ve broken down the article writing process into 7 clear, actionable steps:

### Step 1: Keyword Research
Start with AI-powered keyword research to find topics people are actually searching for.

### Step 2: Topic Selection  
Choose from AI-generated topic suggestions based on search intent.

### Step 3: Source Integration
Add credible sources to support your content with our built-in research tools.

### Step 4: Author Profile
Build your E-E-A-T authority with detailed author profiles.

### Step 5: Style & Format
Define your writing style and formatting preferences.

### Step 6: AI Generation
Generate your complete article using all previous inputs.

### Step 7: Review & Export
Review, edit, and export your publication-ready article.

## Start Writing Better Articles Today

Ready to transform your content creation process? Sign up for Writer J and experience the difference our structured approach makes.',
    'Learn how Writer J''s revolutionary 7-step process helps you create better articles faster. From keyword research to publication, we guide you through every step.',
    'Writer J Team',
    'ai-writing',
    ARRAY['writing', 'ai', 'content creation', 'tutorial'],
    5,
    true,
    true,
    'Getting Started with Writer J: 7-Step Guide to Better Articles | Writer J',
    'Discover how Writer J''s 7-step process revolutionizes article writing. From keyword research to publication-ready content, learn our proven methodology.',
    ARRAY['writer j', 'ai writing', '7-step process', 'article writing', 'content creation']
) ON CONFLICT (slug) DO NOTHING;

-- Insert sample pillar page
INSERT INTO pillar_pages (
    title,
    slug,
    content,
    excerpt,
    author,
    category,
    menu_order,
    published,
    featured,
    seo_title,
    seo_description,
    seo_keywords,
    cluster_pages
) VALUES (
    'The Complete Guide to AI-Powered Content Creation',
    'complete-guide-ai-content-creation',
    '# The Complete Guide to AI-Powered Content Creation

The landscape of content creation has been revolutionized by artificial intelligence. This comprehensive guide will walk you through everything you need to know about leveraging AI for better, faster content creation.

## What is AI-Powered Content Creation?

AI-powered content creation refers to using artificial intelligence tools and techniques to assist in the writing, editing, and optimization of content. Unlike traditional writing methods, AI can help with research, ideation, drafting, and even optimization.

## Why Traditional Writing Methods Fall Short

Most writers face the same challenges:
- Blank page syndrome
- Inconsistent quality
- Time-consuming research
- SEO optimization complexity
- Lack of structured process

## The Writer J Approach

Our 7-step methodology addresses each of these challenges systematically:

1. **Structured Research**: AI-powered keyword and topic research
2. **Guided Writing**: Step-by-step content creation process  
3. **Quality Assurance**: Built-in optimization and review tools
4. **Consistency**: Repeatable process for reliable results

## Getting the Most from AI Writing Tools

To maximize your success with AI writing tools, consider these best practices:

### Choose the Right Tool
Not all AI writing tools are created equal. Look for:
- Structured workflows
- Research integration
- SEO optimization
- Quality control features

### Develop a Process
The key to success is having a repeatable process that works for your specific needs.

## Dive Deeper

Explore these related topics to master AI-powered content creation:',
    'Master AI-powered content creation with our comprehensive guide. Learn proven strategies, best practices, and how to leverage AI tools effectively.',
    'Writer J Team',
    'ai-writing',
    1,
    true,
    true,
    'Complete Guide to AI-Powered Content Creation | Writer J',
    'Master AI-powered content creation with Writer J. Learn proven strategies, tools, and techniques for creating better content faster.',
    ARRAY['ai content creation', 'ai writing tools', 'content strategy', 'writer j'],
    '[
        {
            "blogPostId": 1,
            "displayOrder": 1,
            "customTitle": "Getting Started with Writer J",
            "customDescription": "Learn the basics of our 7-step writing process"
        }
    ]'::jsonb
) ON CONFLICT (slug) DO NOTHING;
`;

// Write migration file
console.log('Creating blog system migration...');
fs.writeFileSync('/tmp/blog_migration.sql', blogMigration);
console.log('Blog system migration created at /tmp/blog_migration.sql');

module.exports = { blogMigration };