# Database Configuration
DATABASE_URL=postgresql://writer_j_user:writer_j_password@localhost:5432/writer_j_dev

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# AI Model API Configuration (Optional - can be configured via admin interface)
GEMINI_API_KEY=your_gemini_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here
SERPER_API_KEY=your_serper_api_key

# Paddle Configuration
PADDLE_API_KEY=your_paddle_api_key
PADDLE_WEBHOOK_SECRET=your_paddle_webhook_secret
PADDLE_ENVIRONMENT=sandbox
PADDLE_PRODUCT_ID=pro_01jxvxwt0caq7y2c3ae6n6rkvn
PADDLE_PRO_PRICE_ID=pri_01jxwq6phx8gpzdhffvgdqraks
PADDLE_UNLIMITED_PRICE_ID=pri_01jxwqaahzjfqnxheqea92f27m

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration (optional)
FRONTEND_URL=http://localhost:5173

# Reddit OAuth Configuration
REDDIT_CLIENT_ID=your_reddit_app_client_id
REDDIT_CLIENT_SECRET=your_reddit_app_client_secret
REDDIT_REDIRECT_URI=http://localhost:3001/api/reddit-oauth/callback

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>
