const sqlite3 = require('sqlite3').verbose();
const { Pool } = require('pg');
const path = require('path');

class Database {
  constructor() {
    this.db = null;
    this.pool = null;
    this.isPostgres = process.env.DATABASE_URL && process.env.DATABASE_URL.startsWith('postgresql://');
  }

  async connect() {
    try {
      if (this.isPostgres) {
        // PostgreSQL connection
        this.pool = new Pool({
          connectionString: process.env.DATABASE_URL,
          ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
        });

        // Test the connection
        const client = await this.pool.connect();
        client.release();
        console.log('Connected to PostgreSQL database');

        await this.initializeTables();
        return Promise.resolve();
      } else {
        // SQLite connection (fallback for local development)
        return new Promise((resolve, reject) => {
          const dbPath = process.env.NODE_ENV === 'production'
            ? path.join(__dirname, '../data/production.db')
            : path.join(__dirname, '../data/development.db');

          this.db = new sqlite3.Database(dbPath, (err) => {
            if (err) {
              console.error('Error opening database:', err.message);
              reject(err);
            } else {
              console.log('Connected to SQLite database');
              this.initializeTables().then(resolve).catch(reject);
            }
          });
        });
      }
    } catch (error) {
      console.error('Database connection error:', error);
      throw error;
    }
  }

  async initializeTables() {
    const tables = this.isPostgres ? this.getPostgresTables() : this.getSQLiteTables();

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes for better performance
    const indexes = this.isPostgres ? this.getPostgresIndexes() : this.getSQLiteIndexes();

    for (const index of indexes) {
      await this.run(index);
    }

    console.log('Database tables initialized successfully');
  }

  getPostgresTables() {
    return [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        plan_type VARCHAR(50) DEFAULT 'V1_DEFAULT_ACCESS',
        role VARCHAR(20) DEFAULT 'user', -- 'user' or 'admin'
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tasks table
      `CREATE TABLE IF NOT EXISTS tasks (
        id VARCHAR(255) PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        status VARCHAR(100) DEFAULT 'Draft - Step 1',
        current_step INTEGER DEFAULT 0,
        keywords TEXT, -- JSON array
        selected_topics TEXT, -- JSON array
        topic_suggestions TEXT, -- JSON array
        keyword_research_data TEXT, -- JSON object
        keyword_research_selections TEXT, -- JSON array
        reddit_sources TEXT, -- JSON array
        sources TEXT, -- JSON array (legacy)
        topic_sources TEXT, -- JSON object
        product_info TEXT, -- JSON object
        eeat_profile TEXT, -- JSON object
        output_parameters TEXT, -- JSON object
        generated_article TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User presets table
      `CREATE TABLE IF NOT EXISTS user_presets (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        preset_type VARCHAR(50) NOT NULL, -- 'author' or 'product'
        preset_name VARCHAR(255) NOT NULL,
        preset_data TEXT NOT NULL, -- JSON object
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Blog posts table
      `CREATE TABLE IF NOT EXISTS blog_posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT,
        featured_image VARCHAR(500),
        author VARCHAR(255) DEFAULT 'J-Writer Team',
        status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'published', 'unpublished'
        categories TEXT, -- JSON array
        tags TEXT, -- JSON array
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        published_at TIMESTAMP
      )`,

      // User sessions table (for JWT blacklisting)
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // AI Models configuration table
      `CREATE TABLE IF NOT EXISTS ai_models (
        id SERIAL PRIMARY KEY,
        model_name VARCHAR(100) NOT NULL UNIQUE,
        provider VARCHAR(50) NOT NULL,
        api_key VARCHAR(500),
        model_version VARCHAR(100),
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Prompt templates table
      `CREATE TABLE IF NOT EXISTS prompt_templates (
        id SERIAL PRIMARY KEY,
        template_name VARCHAR(100) NOT NULL,
        template_type VARCHAR(50) NOT NULL, -- 'clustered_topics' or 'article_generation'
        model_name VARCHAR(100) NOT NULL,
        prompt_content TEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_name) REFERENCES ai_models (model_name) ON DELETE CASCADE,
        UNIQUE(template_type, model_name)
      )`,

      // User OAuth tokens table
      `CREATE TABLE IF NOT EXISTS user_oauth_tokens (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        provider VARCHAR(50) NOT NULL, -- 'reddit', 'twitter', etc.
        access_token VARCHAR(1000),
        refresh_token VARCHAR(1000),
        expires_at TIMESTAMP,
        scope TEXT,
        reddit_username VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, provider)
      )`
    ];
  }

  getSQLiteTables() {
    return [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email TEXT UNIQUE NOT NULL,
        password_hash TEXT NOT NULL,
        full_name TEXT,
        plan_type TEXT DEFAULT 'V1_DEFAULT_ACCESS',
        role TEXT DEFAULT 'user', -- 'user' or 'admin'
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token TEXT,
        reset_token TEXT,
        reset_token_expires DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tasks table
      `CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        status TEXT DEFAULT 'Draft - Step 1',
        current_step INTEGER DEFAULT 0,
        keywords TEXT, -- JSON array
        selected_topics TEXT, -- JSON array
        topic_suggestions TEXT, -- JSON array
        keyword_research_data TEXT, -- JSON object
        keyword_research_selections TEXT, -- JSON array
        reddit_sources TEXT, -- JSON array
        sources TEXT, -- JSON array (legacy)
        topic_sources TEXT, -- JSON object
        product_info TEXT, -- JSON object
        eeat_profile TEXT, -- JSON object
        output_parameters TEXT, -- JSON object
        generated_article TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User presets table
      `CREATE TABLE IF NOT EXISTS user_presets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        preset_type TEXT NOT NULL, -- 'author' or 'product'
        preset_name TEXT NOT NULL,
        preset_data TEXT NOT NULL, -- JSON object
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Blog posts table
      `CREATE TABLE IF NOT EXISTS blog_posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT,
        featured_image TEXT,
        author TEXT DEFAULT 'J-Writer Team',
        status TEXT DEFAULT 'draft', -- 'draft', 'published', 'unpublished'
        categories TEXT, -- JSON array
        tags TEXT, -- JSON array
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        published_at DATETIME
      )`,

      // User sessions table (for JWT blacklisting)
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        token_hash TEXT NOT NULL,
        expires_at DATETIME NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // AI Models configuration table
      `CREATE TABLE IF NOT EXISTS ai_models (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        model_name TEXT NOT NULL UNIQUE,
        provider TEXT NOT NULL,
        api_key TEXT,
        model_version TEXT,
        is_active BOOLEAN DEFAULT TRUE,
        is_default BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Prompt templates table
      `CREATE TABLE IF NOT EXISTS prompt_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        template_name TEXT NOT NULL,
        template_type TEXT NOT NULL, -- 'clustered_topics' or 'article_generation'
        model_name TEXT NOT NULL,
        prompt_content TEXT NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (model_name) REFERENCES ai_models (model_name) ON DELETE CASCADE,
        UNIQUE(template_type, model_name)
      )`,

      // User OAuth tokens table
      `CREATE TABLE IF NOT EXISTS user_oauth_tokens (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        provider TEXT NOT NULL, -- 'reddit', 'twitter', etc.
        access_token TEXT,
        refresh_token TEXT,
        expires_at DATETIME,
        scope TEXT,
        reddit_username TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
        UNIQUE(user_id, provider)
      )`
    ];
  }

  getPostgresIndexes() {
    return [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_presets_user_id ON user_presets(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_presets_type ON user_presets(preset_type)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_provider ON ai_models(provider)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_active ON ai_models(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_type ON prompt_templates(template_type)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_model ON prompt_templates(model_name)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_user_provider ON user_oauth_tokens(user_id, provider)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires ON user_oauth_tokens(expires_at)'
    ];
  }

  getSQLiteIndexes() {
    return [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_presets_user_id ON user_presets(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_presets_type ON user_presets(preset_type)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_provider ON ai_models(provider)',
      'CREATE INDEX IF NOT EXISTS idx_ai_models_active ON ai_models(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_type ON prompt_templates(template_type)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_model ON prompt_templates(model_name)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_user_provider ON user_oauth_tokens(user_id, provider)',
      'CREATE INDEX IF NOT EXISTS idx_oauth_tokens_expires ON user_oauth_tokens(expires_at)'
    ];
  }

  // Convert SQLite-style ? placeholders to PostgreSQL-style $1, $2, etc.
  convertSqlForPostgres(sql) {
    let paramCount = 1;
    return sql.replace(/\?/g, () => `$${paramCount++}`);
  }

  async run(sql, params = []) {
    if (this.isPostgres) {
      try {
        let pgSql = this.convertSqlForPostgres(sql);

        // For INSERT statements, add RETURNING id to get the inserted ID
        if (pgSql.trim().toUpperCase().startsWith('INSERT') && !pgSql.toUpperCase().includes('RETURNING')) {
          pgSql += ' RETURNING id';
        }

        const result = await this.pool.query(pgSql, params);
        return {
          id: result.rows[0]?.id || null,
          changes: result.rowCount || 0,
          rows: result.rows
        };
      } catch (error) {
        throw error;
      }
    } else {
      return new Promise((resolve, reject) => {
        this.db.run(sql, params, function(err) {
          if (err) {
            reject(err);
          } else {
            resolve({ id: this.lastID, changes: this.changes });
          }
        });
      });
    }
  }

  async get(sql, params = []) {
    if (this.isPostgres) {
      try {
        const pgSql = this.convertSqlForPostgres(sql);
        const result = await this.pool.query(pgSql, params);
        return result.rows[0] || null;
      } catch (error) {
        throw error;
      }
    } else {
      return new Promise((resolve, reject) => {
        this.db.get(sql, params, (err, row) => {
          if (err) {
            reject(err);
          } else {
            resolve(row);
          }
        });
      });
    }
  }

  async all(sql, params = []) {
    if (this.isPostgres) {
      try {
        const pgSql = this.convertSqlForPostgres(sql);
        const result = await this.pool.query(pgSql, params);
        return result.rows;
      } catch (error) {
        throw error;
      }
    } else {
      return new Promise((resolve, reject) => {
        this.db.all(sql, params, (err, rows) => {
          if (err) {
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });
    }
  }

  async close() {
    if (this.isPostgres) {
      try {
        await this.pool.end();
        console.log('PostgreSQL connection pool closed');
      } catch (error) {
        throw error;
      }
    } else {
      return new Promise((resolve, reject) => {
        this.db.close((err) => {
          if (err) {
            reject(err);
          } else {
            console.log('SQLite database connection closed');
            resolve();
          }
        });
      });
    }
  }
}

module.exports = new Database();
