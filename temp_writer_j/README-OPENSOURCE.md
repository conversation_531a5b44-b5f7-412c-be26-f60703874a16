# Writer J - Free Open Source Edition

## 🚀 AI-Driven Article Generation Platform

Writer J is a comprehensive AI-powered article generation platform that guides users through a systematic 7-step workflow to create high-quality, SEO-optimized content. This free open source edition provides the core functionality for individuals and small teams.

## ✨ Key Features

### 🎯 7-Step Article Generation Workflow
1. **Keyword Research** - Discover content opportunities with AI-powered keyword analysis
2. **Topic Selection** - Choose from AI-generated topic suggestions
3. **Resource Integration** - Add supporting materials (URLs, text, Reddit insights)
4. **Product Integration** - Optional product mentions and promotions
5. **E-E-A-T Profile** - Establish expertise, experience, authoritativeness, and trustworthiness
6. **Style & Format** - Customize writing tone, length, and output format
7. **Article Generation** - Create comprehensive articles with AI assistance

### 🤖 Multi-AI Service Support
- **Google Gemini API** - Primary AI service with advanced reasoning
- **DeepSeek API** - Cost-effective alternative with strong performance
- **OpenAI API** - Compatible with GPT models
- **Intelligent Routing** - Automatic fallback between services

### 📊 Advanced Content Features
- **Reddit Insights Integration** - Real user discussions and community insights
- **Comprehensive Resource Management** - Unified system for all content sources
- **SEO Optimization** - Built-in keyword research and optimization
- **Multiple Output Formats** - Markdown, HTML, and plain text
- **Real-time Preview** - Live markdown rendering with custom styling

### 👥 User Management
- **JWT Authentication** - Secure user sessions
- **Role-based Access** - User and admin roles
- **Task Management** - Save and resume article projects
- **Preset Configurations** - Save frequently used settings

## 🏗️ Technology Stack

### Frontend
- **React 19.1.0** + **Vite** - Modern development environment
- **Tailwind CSS** - Utility-first styling
- **React Router** - Client-side routing
- **Headless UI** + **Heroicons** - Accessible components

### Backend
- **Node.js** + **Express.js** - RESTful API server
- **PostgreSQL** (Production) / **SQLite** (Development) - Database
- **JWT + bcrypt** - Authentication and security
- **Serper API** - Search functionality

### Deployment
- **Frontend**: Vercel, Netlify, or any static hosting
- **Backend**: Railway, Heroku, or Docker containers
- **Database**: Railway PostgreSQL, Supabase, or self-hosted

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ 
- PostgreSQL (for production) or SQLite (for development)
- AI API keys (Google Gemini, DeepSeek, or OpenAI)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/Hicruben/j-writer.git
cd j-writer
```

2. **Backend Setup**
```bash
cd backend
npm install
cp .env.example .env
# Edit .env with your configuration
npm run init-db
npm run dev
```

3. **Frontend Setup**
```bash
cd ../ai-article-generator
npm install
cp .env.example .env.local
# Edit .env.local with your API URLs
npm run dev
```

4. **Access the application**
- Frontend: http://localhost:5173
- Backend API: http://localhost:3001

### Environment Variables

#### Backend (.env)
```bash
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/writer_j
# or for development:
# DATABASE_URL=sqlite:./database.db

# JWT
JWT_SECRET=your-super-secret-jwt-key

# AI Services
GOOGLE_API_KEY=your-google-gemini-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key
OPENAI_API_KEY=your-openai-api-key

# Search
SERPER_API_KEY=your-serper-api-key
```

#### Frontend (.env.local)
```bash
VITE_API_URL=http://localhost:3001
```

## 📖 Usage Guide

### Creating Your First Article

1. **Register/Login** - Create an account or sign in
2. **Start New Task** - Click "Create New Article"
3. **Follow the 7-Step Workflow**:
   - Research keywords related to your topic
   - Select from AI-generated topic suggestions
   - Add supporting resources and Reddit insights
   - Configure product mentions (optional)
   - Set up your authority profile
   - Choose writing style and format
   - Generate and edit your article

### Key Tips
- **Keyword Research**: Use specific, long-tail keywords for better results
- **Reddit Insights**: Add relevant community discussions for authenticity
- **Resource Quality**: Include authoritative sources for better AI output
- **E-E-A-T Profile**: Complete your expertise profile for authority

## 🔧 Development

### Project Structure
```
j-writer/
├── ai-article-generator/     # React frontend
│   ├── src/
│   │   ├── components/       # React components
│   │   ├── contexts/         # Context providers
│   │   └── config/          # Configuration
├── backend/                  # Express.js backend
│   ├── routes/              # API routes
│   ├── services/            # Business logic
│   ├── middleware/          # Express middleware
│   └── config/              # Server configuration
└── docs/                    # Documentation
```

### Development Commands

```bash
# Backend
npm run dev          # Start development server
npm run migrate      # Run database migrations
npm test            # Run tests

# Frontend  
npm run dev         # Start development server
npm run build       # Build for production
npm run preview     # Preview production build
```

### Adding New AI Services

1. Create service file in `backend/services/`
2. Implement the standard interface:
   ```javascript
   class NewAIService {
     async generateArticle(data) { /* implementation */ }
     async generateTopics(keywords) { /* implementation */ }
     async keywordResearch(keyword) { /* implementation */ }
   }
   ```
3. Register in `aiServiceManager.js`

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Ways to Contribute
- 🐛 Report bugs and issues
- 💡 Suggest new features
- 📝 Improve documentation
- 🔧 Submit pull requests
- 🌍 Help with translations

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Community Support
- **GitHub Issues** - Bug reports and feature requests
- **Discussions** - Community Q&A and ideas
- **Discord** - Real-time community chat (coming soon)

### Documentation
- **API Documentation** - `/docs/api.md`
- **Deployment Guide** - `/docs/deployment.md`
- **Troubleshooting** - `/docs/troubleshooting.md`

## 🗺️ Roadmap

### v1.1.0 (Next Release)
- [ ] Enhanced Reddit integration with authentication
- [ ] Bulk article generation
- [ ] Template system for common article types
- [ ] Advanced SEO analysis

### v1.2.0 (Future)
- [ ] Multi-language support
- [ ] Team collaboration features
- [ ] Plugin system for custom AI services
- [ ] WordPress integration

### v2.0.0 (Long-term)
- [ ] Visual content generation
- [ ] Social media integration
- [ ] Advanced analytics dashboard
- [ ] Mobile application

## 🙏 Acknowledgments

- **AI Services**: Google Gemini, DeepSeek, OpenAI for powering the content generation
- **Community**: Reddit communities for providing authentic user insights
- **Open Source**: All the amazing open source projects that made this possible

## 📊 Stats

- **Languages**: JavaScript, React, Node.js
- **Database**: PostgreSQL, SQLite
- **AI Models**: Gemini, DeepSeek, GPT
- **License**: MIT
- **Version**: 1.0.0-free

---

**Made with ❤️ by the Writer J team**

*Transform your content creation process with AI-powered article generation.*