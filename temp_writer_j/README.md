# Writer J - AI Article Generator

A sophisticated AI-powered article generation tool that implements a strategic 7-step workflow to create high-quality, SEO-friendly articles with E-E-A-T (Experience, Expertise, Authoritativeness, Trustworthiness) integration.

## Features

### 7-Step Article Generation Workflow

1. **Keyword Research & Topic Discovery**
   - Comprehensive keyword research with autocomplete suggestions
   - Related keywords and key terms analysis
   - People Also Ask (PAA) questions extraction
   - SERP analysis for content opportunities

2. **Topic Selection**
   - AI-generated topic suggestions based on keyword research
   - Strategic topic clustering and organization
   - Multiple topic selection (1-5 topics) for comprehensive articles
   - Editable topic customization

3. **Source Integration**
   - URL content extraction with automatic summarization
   - Custom text block integration
   - Topic-specific source organization
   - Reference management and citation

4. **Product Integration (Optional)**
   - Product/service information input
   - Feature and benefit mapping
   - Natural product placement strategy
   - Link integration for product mentions

5. **E-E-A-T Authority Profile**
   - Author name and credentials
   - Professional bio and experience
   - Expertise demonstration
   - Authority establishment for content credibility

6. **Style & Format Parameters**
   - Customizable tone and writing style
   - Article length options (Short ~800, Medium ~1500, Long ~2500 words)
   - Target audience specification
   - SEO optimization settings

7. **Article Generation & Refinement**
   - AI-powered article generation with streaming
   - Real-time editing capabilities
   - Auto-save functionality
   - Export and task management

## Technology Stack

### Frontend
- **React** - Modern UI framework
- **Vite** - Fast build tool and dev server
- **Tailwind CSS** - Utility-first CSS framework
- **Headless UI** - Accessible UI components
- **Heroicons** - Beautiful SVG icons

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **Google Gemini API** - AI content generation
- **Cheerio** - Web scraping for content extraction
- **CORS, Helmet, Morgan** - Security and logging middleware

## Installation & Setup

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Google Gemini API key

### Backend Setup

1. Navigate to the backend directory:
```bash
cd backend
```

2. Install dependencies:
```bash
npm install
```

3. Create environment file:
```bash
cp .env.example .env
```

4. Add your Google Gemini API key to `.env`:
```
GEMINI_API_KEY=your_actual_api_key_here
```

5. Start the backend server:
```bash
npm run dev
```

The backend will run on `http://localhost:3001`

### Frontend Setup

1. Navigate to the frontend directory:
```bash
cd ai-article-generator
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

The frontend will run on `http://localhost:5173`

## Usage

1. **Start with Keywords**: Enter relevant keywords for your article topic
2. **Generate Topics**: Click "Generate Topic Suggestions" to get AI-powered topic ideas
3. **Select Topic**: Choose from high-intent questions, trending angles, or problem-focused topics
4. **Add Sources** (Optional): Include up to 3 URL sources or text blocks for reference
5. **Product Info** (Optional): Add product/service details for natural integration
6. **Configure Output**: Set tone, length, format, and author information
7. **Generate Article**: Create your article and edit as needed
8. **Export**: Copy to clipboard or download as file

## API Endpoints

### Ideation
- `POST /api/ideation/suggestions` - Generate topic suggestions from keywords

### Sources
- `POST /api/sources/extract` - Extract content from a single URL
- `POST /api/sources/extract-multiple` - Extract content from multiple URLs

### Generation
- `POST /api/generate/article` - Generate complete article based on all inputs

## Development Features

- **Mock Mode**: Backend works without API key for development/testing
- **Error Handling**: Graceful fallbacks for failed API calls
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Validation**: Input validation and user feedback
- **Progress Tracking**: Visual step indicator with completion status

## Environment Variables

### Backend (.env)
```
GEMINI_API_KEY=your_gemini_api_key
PORT=3001
NODE_ENV=development
FRONTEND_URL=http://localhost:5173
```

## Project Structure

```
├── ai-article-generator/          # Frontend React app
│   ├── src/
│   │   ├── components/           # React components
│   │   ├── App.jsx              # Main app component
│   │   └── main.jsx             # App entry point
│   ├── public/                  # Static assets
│   └── package.json
├── backend/                     # Backend API
│   ├── routes/                  # API route handlers
│   ├── services/               # Business logic services
│   ├── utils/                  # Utility functions
│   ├── server.js              # Express server
│   └── package.json
├── docs/                       # Documentation
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For issues and questions, please create an issue in the GitHub repository.
