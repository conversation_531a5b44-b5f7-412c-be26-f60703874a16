import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import Dashboard from './components/Dashboard';
import ArticleGenerator from './components/ArticleGenerator';
import TaskList from './components/tasks/TaskList';
import StartArticle from './components/tasks/StartArticle';
import TaskEditor from './components/tasks/TaskEditor';
import Settings from './components/Settings';
import Profile from './components/Profile';
import AdminPanel from './components/AdminPanel';
import ContentCategories from './components/ContentCategories';
import CategoryView from './components/CategoryView';
import Homepage from './components/pages/Homepage';
import NewHomepage from './components/pages/NewHomepage';
import Features from './components/pages/Features';
import Pricing from './components/pages/Pricing';
import PublicArticles from './components/public/PublicArticles';
import PublicArticleDetail from './components/public/PublicArticleDetail';
import AuthTest from './components/pages/AuthTest';
import LanguageRouter from './components/LanguageRouter';

function App() {
  return (
    <AuthProvider>
      <Router future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}>
        <LanguageRouter>
          <Routes>
            {/* Language-prefixed routes */}
            <Route path="/:lang" element={<NewHomepage />} />
            <Route path="/:lang/marketing" element={<Homepage />} />
            <Route path="/:lang/features" element={<Features />} />
            <Route path="/:lang/pricing" element={<Pricing />} />
            <Route path="/:lang/login" element={<Login />} />
            <Route path="/:lang/register" element={<Register />} />

            {/* Public article routes with language prefix */}
            <Route path="/:lang/articles" element={<PublicArticles />} />
            <Route path="/:lang/articles/:slug" element={<PublicArticleDetail />} />
            <Route path="/:lang/category/:contentType" element={<PublicArticles />} />

            {/* Protected routes with language prefix */}
            <Route path="/:lang/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            <Route path="/:lang/generator" element={
              <ProtectedRoute>
                <ArticleGenerator />
              </ProtectedRoute>
            } />

            <Route path="/:lang/tasks" element={
              <ProtectedRoute>
                <TaskList />
              </ProtectedRoute>
            } />

            <Route path="/:lang/tasks/new" element={
              <ProtectedRoute>
                <StartArticle />
              </ProtectedRoute>
            } />

            <Route path="/:lang/tasks/:taskId/edit" element={
              <ProtectedRoute>
                <TaskEditor />
              </ProtectedRoute>
            } />

            <Route path="/:lang/tasks/:taskId" element={
              <ProtectedRoute>
                <TaskEditor />
              </ProtectedRoute>
            } />

            <Route path="/:lang/content/categories" element={
              <ProtectedRoute>
                <ContentCategories />
              </ProtectedRoute>
            } />

            <Route path="/:lang/content/category/:contentType" element={
              <ProtectedRoute>
                <CategoryView />
              </ProtectedRoute>
            } />

            <Route path="/:lang/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />

            <Route path="/:lang/admin" element={
              <ProtectedRoute>
                <AdminPanel />
              </ProtectedRoute>
            } />

            <Route path="/:lang/auth-test" element={<AuthTest />} />

            <Route path="/:lang/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />

            {/* Root redirect to default language */}
            <Route path="/" element={<Navigate to="/en" replace />} />

            {/* Catch all - redirect to homepage with language */}
            <Route path="*" element={<Navigate to="/en" replace />} />
          </Routes>
        </LanguageRouter>
      </Router>
    </AuthProvider>
  );
}

export default App;
