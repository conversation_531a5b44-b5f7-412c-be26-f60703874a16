/**
 * Utility functions for extracting structured data from article content
 */

/**
 * Extract SEO metadata from article content
 * @param {string} content - Article content
 * @returns {Object} SEO metadata object
 */
export const extractSEOData = (content) => {
  if (!content) return { title: '', description: '', keywords: [], tags: [] };

  const seoData = {
    title: '',
    description: '',
    keywords: [],
    tags: []
  };

  // Extract SEO Title - various formats
  const seoTitleMatch = content.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);
  
  if (seoTitleMatch && seoTitleMatch[1]) {
    seoData.title = seoTitleMatch[1]
      .trim()
      .replace(/[#*_`\[\]]/g, '') // Remove markdown
      .replace(/[.]*$/, '') // Remove trailing periods
      .split(/meta\s*description/i)[0] // Stop at meta description
      .trim();
  }

  // Extract Meta Description
  const metaDescMatch = content.match(/\*\*Meta Description:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/Meta Description:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);
  
  if (metaDescMatch && metaDescMatch[1]) {
    seoData.description = metaDescMatch[1]
      .trim()
      .replace(/[#*_`\[\]]/g, '')
      .trim();
  }

  // Extract Keywords
  const keywordsMatch = content.match(/\*\*Focus Keywords:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/Focus Keywords:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                       content.match(/\*\*Keywords:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);
  
  if (keywordsMatch && keywordsMatch[1]) {
    seoData.keywords = keywordsMatch[1]
      .split(',')
      .map(k => k.trim())
      .filter(k => k.length > 0);
  }

  // Extract Tags
  const tagsMatch = content.match(/\*\*Tags:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                   content.match(/Tags:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i);
  
  if (tagsMatch && tagsMatch[1]) {
    seoData.tags = tagsMatch[1]
      .split(',')
      .map(t => t.trim())
      .filter(t => t.length > 0);
  }

  return seoData;
};

/**
 * Clean article content by removing SEO metadata section
 * @param {string} content - Original article content
 * @returns {string} Cleaned content without SEO metadata
 */
export const cleanArticleContent = (content) => {
  if (!content) return '';

  // Remove the SEO metadata section
  // This typically includes SEO Title, Meta Description, Focus Keywords, Tags
  let cleanedContent = content;

  // Remove SEO Title line
  cleanedContent = cleanedContent.replace(/\*?\*?SEO Title:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  
  // Remove Meta Description line
  cleanedContent = cleanedContent.replace(/\*?\*?Meta Description:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  
  // Remove Focus Keywords line
  cleanedContent = cleanedContent.replace(/\*?\*?(?:Focus )?Keywords:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');
  
  // Remove Tags line
  cleanedContent = cleanedContent.replace(/\*?\*?Tags:\*?\*?\s*\[?[^\]\n]+?\]?(?:\s*\n)?/gi, '');

  // Remove Visual Aid Suggestions section
  cleanedContent = cleanedContent.replace(/\*?\*?Visual Aid Suggestions:\*?\*?[\s\S]*?(?=\n\n|\n#|$)/gi, '');

  // Remove any empty lines at the beginning
  cleanedContent = cleanedContent.replace(/^\s*\n+/, '');

  return cleanedContent;
};

/**
 * Extract Visual Aid Suggestions from content (to be removed per requirements)
 * @param {string} content - Article content
 * @returns {Array} Empty array (feature being removed)
 */
export const extractVisualAidSuggestions = (content) => {
  // Feature removed per requirements
  return [];
};