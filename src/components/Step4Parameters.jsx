import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { API_CONFIG } from '../config/api';

const Step4Parameters = ({ data, updateData }) => {
  const { authenticatedFetch } = useAuth();
  const [contentTypes, setContentTypes] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchContentTypes();
  }, []);

  const fetchContentTypes = async () => {
    try {
      const response = await authenticatedFetch('/api/content/types');
      if (response.ok) {
        const data = await response.json();
        setContentTypes(data.contentTypes);
      } else {
        // If API fails, use fallback
        setContentTypes(getDefaultContentTypes());
      }
    } catch (error) {
      console.error('Error fetching content types:', error);
      // Fallback to hardcoded types if API fails
      setContentTypes(getDefaultContentTypes());
    } finally {
      setLoading(false);
    }
  };

  const updateParameters = (field, value) => {
    updateData({
      outputParameters: {
        ...data.outputParameters,
        [field]: value
      }
    });
  };

  const getContentTypeInfo = (contentType) => {
    const typeMap = {
      'casino_review': { icon: '🎰', label: 'Casino Review', description: 'Comprehensive casino platform reviews with games, bonuses, and user experience analysis' },
      'game_guide': { icon: '🎮', label: 'Game Guide', description: 'Strategic guides for casino games including rules, strategies, and tips' },
      'strategy_article': { icon: '📊', label: 'Strategy Article', description: 'Mathematical analysis and evidence-based gaming strategies' },
      'brand_copy': { icon: '✨', label: 'Brand Copy', description: 'Compelling marketing content and brand messaging' },
      'industry_news': { icon: '📰', label: 'Industry News', description: 'Latest developments in the gambling and casino industry' },
      'sports_betting': { icon: '⚽', label: 'Sports Betting', description: 'Sports betting guides, predictions, and platform reviews' },
      'generic': { icon: '📝', label: 'General Content', description: 'General purpose content' }
    };

    return typeMap[contentType] || { 
      icon: '📄', 
      label: contentType ? contentType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) : 'Unknown Type', 
      description: 'Custom content type' 
    };
  };

  const getDefaultContentTypes = () => [
    {
      content_type: 'casino_review',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'game_guide',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'strategy_article',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'brand_copy',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'industry_news',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'sports_betting',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'bonus_analysis',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'regulatory_update',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    },
    {
      content_type: 'generic',
      template_count: 1,
      total_tasks: 0,
      completed_tasks: 0
    }
  ];

  const tonalityOptions = [
    { value: 'informative', label: 'Informative', description: 'Educational and fact-based' },
    { value: 'persuasive', label: 'Persuasive', description: 'Convincing and compelling' },
    { value: 'casual', label: 'Casual', description: 'Friendly and conversational' },
    { value: 'formal', label: 'Formal', description: 'Professional and structured' },
    { value: 'technical', label: 'Technical', description: 'Detailed and expert-level' },
  ];

const lengthOptions = [
  {
    value: 'snippet',
    label: 'Snippet (~500 words)', // New shorter option
    description: 'Very short, focused piece, ideal for quick updates, single ideas, or FAQ answers'
  },
  {
    value: 'short_post', // Corresponds to your previous 'short'
    label: 'Short Post (~800 words)',
    description: 'Concise content, good for explaining a specific concept or a typical blog post (usually suitable for 1-2 topics)'
  },
  {
    value: 'medium_article', // Corresponds to your previous 'medium'
    label: 'Medium Article (~1500 words)',
    description: 'Balanced depth and readability, suitable for an in-depth exploration of a topic (usually suitable for 2-4 topics)'
  },
  {
    value: 'long_guide', // Corresponds to your previous 'long'
    label: 'Long-Form Guide (~2500 words)',
    description: 'Comprehensive and detailed coverage of a single, focused topic (monitor single-generation quality for optimal results; usually suitable for 3-5 closely related topics)'
  },
  {
    value: 'pillar_module', // New strategic option
    label: 'Pillar Page Module (~700 words)',
    description: 'A focused chapter/section designed for building a larger pillar page (recommended for the "modular assembly" strategy, typically for 1 sub-topic)'
  }
];

  const formatOptions = [
    { value: 'markdown', label: 'Markdown', description: 'With headings and formatting' },
    { value: 'plain', label: 'Plain Text', description: 'Simple text without formatting' },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Customize Your Article Style</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step 6 of 6:</span> Configure how your article should be written.
          Choose the tone, length, and format that best suits your audience and goals.
          These settings will guide the AI to create content that matches your specific requirements.
        </p>
      </div>

      {/* Content Type Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-3">
          Content Type
        </label>
        <p className="text-gray-600 mb-4 text-sm">
          Choose the type of content you want to create. This will customize the AI prompts and structure for your specific needs.
        </p>
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span className="ml-3 text-gray-600">Loading content types...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {contentTypes.map((type) => {
              const typeInfo = getContentTypeInfo(type.content_type);
              return (
                <label
                  key={type.content_type}
                  className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none transition-all hover:shadow-md ${
                    data.outputParameters?.contentType === type.content_type
                      ? 'border-blue-500 bg-blue-50 shadow-md'
                      : 'border-gray-200 bg-white hover:border-gray-300'
                  }`}
                >
                  <input
                    type="radio"
                    name="contentType"
                    value={type.content_type}
                    checked={data.outputParameters?.contentType === type.content_type}
                    onChange={(e) => updateParameters('contentType', e.target.value)}
                    className="sr-only"
                  />
                  <div className="flex flex-1">
                    <div className="flex flex-col">
                      <div className="flex items-center mb-2">
                        <span className="text-2xl mr-3">{typeInfo.icon}</span>
                        <span className={`block text-base font-semibold ${
                          data.outputParameters?.contentType === type.content_type ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {typeInfo.label}
                        </span>
                      </div>
                      <span className={`text-sm leading-relaxed ${
                        data.outputParameters?.contentType === type.content_type ? 'text-blue-700' : 'text-gray-600'
                      }`}>
                        {typeInfo.description}
                      </span>
                      {type.total_tasks > 0 && (
                        <div className="mt-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full inline-block w-fit">
                          {type.total_tasks} created
                        </div>
                      )}
                    </div>
                  </div>
                </label>
              );
            })}
          </div>
        )}
      </div>

      {/* Tonality Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-3">
          Tonality
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {tonalityOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none transition-all hover:shadow-md ${
                data.outputParameters?.tonality === option.value
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="tonality"
                value={option.value}
                checked={data.outputParameters?.tonality === option.value}
                onChange={(e) => updateParameters('tonality', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className={`block text-base font-semibold ${
                    data.outputParameters?.tonality === option.value ? 'text-blue-900' : 'text-gray-900'
                  }`}>
                    {option.label}
                  </span>
                  <span className={`block text-sm ${
                    data.outputParameters?.tonality === option.value ? 'text-blue-700' : 'text-gray-600'
                  }`}>
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Article Length */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-3">
          Article Length
        </label>
        <div className="space-y-3">
          {lengthOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none transition-all hover:shadow-md ${
                data.outputParameters?.length === option.value
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="length"
                value={option.value}
                checked={data.outputParameters?.length === option.value}
                onChange={(e) => updateParameters('length', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className={`block text-base font-semibold ${
                    data.outputParameters?.length === option.value ? 'text-blue-900' : 'text-gray-900'
                  }`}>
                    {option.label}
                  </span>
                  <span className={`block text-sm ${
                    data.outputParameters?.length === option.value ? 'text-blue-700' : 'text-gray-600'
                  }`}>
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Response Format */}
      <div>
        <label className="block text-sm font-medium text-gray-900 mb-3">
          Response Format
        </label>
        <div className="space-y-3">
          {formatOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none transition-all hover:shadow-md ${
                data.outputParameters?.format === option.value
                  ? 'border-blue-500 bg-blue-50 shadow-md'
                  : 'border-gray-200 bg-white hover:border-gray-300'
              }`}
            >
              <input
                type="radio"
                name="format"
                value={option.value}
                checked={data.outputParameters?.format === option.value}
                onChange={(e) => updateParameters('format', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className={`block text-base font-semibold ${
                    data.outputParameters?.format === option.value ? 'text-blue-900' : 'text-gray-900'
                  }`}>
                    {option.label}
                  </span>
                  <span className={`block text-sm ${
                    data.outputParameters?.format === option.value ? 'text-blue-700' : 'text-gray-600'
                  }`}>
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>



      {/* Parameters Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">📋 Style Configuration Summary</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <span className="font-semibold text-gray-900">Content Type:</span>
            <div className="text-blue-600 mt-1">
              {(() => {
                const selectedType = contentTypes.find(type => type.content_type === data.outputParameters?.contentType);
                return selectedType ? getContentTypeInfo(selectedType.content_type).label : 'Generic Content';
              })()}
            </div>
          </div>
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <span className="font-semibold text-gray-900">Tone:</span>
            <div className="text-blue-600 mt-1 capitalize">{data.outputParameters?.tonality || 'informative'}</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <span className="font-semibold text-gray-900">Length:</span>
            <div className="text-blue-600 mt-1 capitalize">{data.outputParameters?.length || 'medium_article'}</div>
          </div>
          <div className="bg-gray-50 rounded-lg p-3 border border-gray-200">
            <span className="font-semibold text-gray-900">Format:</span>
            <div className="text-blue-600 mt-1 capitalize">{data.outputParameters?.format || 'markdown'}</div>
          </div>
        </div>
      </div>

      {/* Ready for Next Step */}
      <div className="casino-card border border-casino-gold-500/50 rounded-lg p-4 bg-gradient-to-r from-casino-gold-500/20 to-casino-gold-400/20">
        <p className="text-sm text-casino-gold-100">
          <strong>Style configured!</strong> Your article will be written as a <strong>
          {(() => {
            const selectedType = contentTypes.find(type => type.content_type === data.outputParameters?.contentType);
            return selectedType ? getContentTypeInfo(selectedType.content_type).label : 'Generic Content';
          })()}</strong> in a <strong>{data.outputParameters?.tonality || 'informative'}</strong> tone,
          approximately <strong>{(() => {
            const length = data.outputParameters?.length || 'medium_article';
            switch(length) {
              case 'snippet': return '500';
              case 'short_post': return '800';
              case 'medium_article': return '1500';
              case 'long_guide': return '2500';
              case 'pillar_module': return '700';
              default: return '1500';
            }
          })()} words</strong>,
          formatted as <strong>{data.outputParameters?.format || 'markdown'}</strong>.
        </p>
      </div>
    </div>
  );
};

export default Step4Parameters;
