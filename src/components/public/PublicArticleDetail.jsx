import React, { useState, useEffect, useRef } from 'react';
import { useParams, Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  ArrowLeftIcon,
  ClockIcon,
  UserIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';
import {
  calculateReadingTime,
  generateTableOfContents,
  addHeadingIds,
  formatDate,
  scrollToElement
} from '../../utils/articleUtils';
import { enrichArticleWithContentType, enrichArticlesWithContentType } from '../../utils/contentTypeUtils';
import { getContentTypeImage } from '../../utils/imageUtils';
import { useCurrentLanguage } from '../../utils/languageUtils';
import { extractSEOData, cleanArticleContent } from '../../utils/extractArticleData';
import MainNavigation from '../layout/MainNavigation';
import ArticleLayoutRenderer from '../layouts/ArticleLayoutRenderer';

const PublicArticleDetail = () => {
  const { t } = useTranslation();
  const currentLanguage = useCurrentLanguage();
  const { slug } = useParams();
  const [article, setArticle] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [relatedArticles, setRelatedArticles] = useState([]);
  const [readingProgress, setReadingProgress] = useState(0);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [tableOfContents, setTableOfContents] = useState([]);
  const [estimatedReadTime, setEstimatedReadTime] = useState(0);
  const contentRef = useRef(null);

  useEffect(() => {
    fetchArticle();
  }, [slug, currentLanguage]);

  useEffect(() => {
    if (article) {
      fetchRelatedArticles();
      generateTableOfContentsFromArticle();
      calculateReadingTimeFromArticle();

      // Use pre-extracted SEO data or fallback to extracting from current content
      const seoData = article.seoData || extractSEOData(article.content || '');

      // Update page title with SEO title if available
      const pageTitle = seoData.title || article.title;
      document.title = `${pageTitle} | Writer 777`;

      // Update meta description with SEO description if available
      const metaDescContent = seoData.description || article.excerpt || article.title;
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', metaDescContent);
      } else {
        const meta = document.createElement('meta');
        meta.name = 'description';
        meta.content = metaDescContent;
        document.head.appendChild(meta);
      }

      // Update meta keywords if available
      if (seoData.keywords && seoData.keywords.length > 0) {
        const metaKeywords = document.querySelector('meta[name="keywords"]');
        const keywordsContent = seoData.keywords.join(', ');
        if (metaKeywords) {
          metaKeywords.setAttribute('content', keywordsContent);
        } else {
          const meta = document.createElement('meta');
          meta.name = 'keywords';
          meta.content = keywordsContent;
          document.head.appendChild(meta);
        }
      }
    }
  }, [article]);

  // Reading progress tracking
  useEffect(() => {
    const handleScroll = () => {
      if (!contentRef.current) return;

      const element = contentRef.current;
      const windowHeight = window.innerHeight;
      const documentHeight = element.offsetHeight;
      const scrollTop = window.scrollY;
      const elementTop = element.offsetTop;

      const progress = Math.min(
        Math.max((scrollTop - elementTop + windowHeight) / documentHeight, 0),
        1
      );

      setReadingProgress(progress * 100);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [article]);

  const fetchArticle = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/articles/${slug}?language=${currentLanguage}`);
      
      if (response.ok) {
        const data = await response.json();
        // Enrich article with content type information
        const enrichedArticle = enrichArticleWithContentType(data.article);

        // Extract SEO data BEFORE cleaning content
        if (enrichedArticle.content) {
          const seoData = extractSEOData(enrichedArticle.content);
          enrichedArticle.seoData = seoData;

          // Clean article content to remove SEO metadata and casino info JSON
          enrichedArticle.content = cleanArticleContent(enrichedArticle.content);
        }

        setArticle(enrichedArticle);
      } else if (response.status === 404) {
        setError('Article not found');
      } else {
        setError('Failed to load article');
      }
    } catch (error) {
      console.error('Error fetching article:', error);
      setError('Failed to load article');
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedArticles = async () => {
    if (!article) return;
    
    try {
      const response = await fetch(
        `${API_CONFIG.BASE_URL}/api/public/articles/category/${article.content_type}?limit=3&language=${currentLanguage}`
      );
      
      if (response.ok) {
        const data = await response.json();
        // Filter out current article and enrich with content type info
        const filtered = data.articles.filter(a => a.slug !== slug);
        const enrichedRelated = enrichArticlesWithContentType(filtered.slice(0, 3));
        setRelatedArticles(enrichedRelated);
      }
    } catch (error) {
      console.error('Error fetching related articles:', error);
    }
  };

  const generateTableOfContentsFromArticle = () => {
    if (!article?.content) return;

    const toc = generateTableOfContents(article.content);
    setTableOfContents(toc);
  };

  const calculateReadingTimeFromArticle = () => {
    if (!article?.content) return;

    const readTime = calculateReadingTime(article.content);
    setEstimatedReadTime(readTime);
  };


  const scrollToHeading = (headingId) => {
    scrollToElement(headingId, 120); // 120px offset for fixed header
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-400 mx-auto"></div>
            <p className="mt-4 text-slate-300">{t('article.loading')}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
        <MainNavigation />
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white mb-4">{error}</h1>
            <Link
              to="/"
              className="inline-flex items-center px-6 py-3 glass-button text-white rounded-lg hover:scale-105 transition-all duration-200"
            >
              <ArrowLeftIcon className="w-5 h-5 mr-2" />
              {t('article.backToArticles')}
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!article) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      {/* Reading Progress Bar */}
      <div className="fixed top-0 left-0 w-full h-1 bg-slate-800/50 z-50">
        <div
          className="h-full bg-gradient-to-r from-blue-400 to-cyan-400 transition-all duration-300"
          style={{ width: `${readingProgress}%` }}
        />
      </div>

      {/* Main Navigation */}
      <MainNavigation />

      {/* Use the new layout renderer */}
      <ArticleLayoutRenderer
        article={article}
        tableOfContents={tableOfContents}
        estimatedReadTime={estimatedReadTime}
        readingProgress={readingProgress}
        contentRef={contentRef}
        scrollToHeading={scrollToHeading}
        showTableOfContents={showTableOfContents}
        setShowTableOfContents={setShowTableOfContents}
      />

      {/* Related Articles */}
      {relatedArticles && relatedArticles.length > 0 && (
        <section className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 border-t border-blue-500/30">
          <h2 className="text-2xl font-bold text-white mb-8">{t('article.relatedArticles')}</h2>
          <div className="grid md:grid-cols-3 gap-6">
            {relatedArticles.map((relatedArticle) => (
              <Link
                key={relatedArticle.id}
                to={`/articles/${relatedArticle.slug}`}
                className="glass-card rounded-xl overflow-hidden glass-interactive"
              >
                <div className="p-6">
                  <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${relatedArticle.contentTypeInfo?.color || 'from-blue-500 to-cyan-500'} text-white mb-3`}>
                    <span>{relatedArticle.contentTypeInfo?.icon || '📄'}</span>
                    <span>{relatedArticle.contentTypeInfo?.label || 'Article'}</span>
                  </div>
                  
                  <h3 className="text-lg font-semibold text-white mb-2 line-clamp-2">
                    {relatedArticle.title}
                  </h3>
                  
                  <p className="text-slate-300 text-sm mb-4 line-clamp-3">
                    {relatedArticle.excerpt}
                  </p>

                  <div className="flex items-center justify-between text-xs text-slate-400">
                    <div className="flex items-center space-x-1">
                      <UserIcon className="w-4 h-4" />
                      <span>{relatedArticle.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <ClockIcon className="w-4 h-4" />
                      <span>{formatDate(relatedArticle.published_at)}</span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-gradient-to-r from-slate-900 via-slate-800 to-indigo-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Brand */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-cyan-400 rounded-lg flex items-center justify-center">
                  <SparklesIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">{t('footer.brand')}</h3>
                  <p className="text-xs text-slate-400">{t('footer.subtitle')}</p>
                </div>
              </div>
              <p className="text-slate-300 text-sm">
                {t('footer.description')}
              </p>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-4">{t('footer.quickLinks')}</h4>
              <ul className="space-y-2 text-sm">
                <li><Link to="/" className="text-slate-300 hover:text-white transition-colors">{t('nav.home')}</Link></li>
                <li><Link to="/articles" className="text-slate-300 hover:text-white transition-colors">{t('nav.allArticles')}</Link></li>
                <li><Link to="/category/game_guide" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.game_guide')}</Link></li>
                <li><Link to="/category/casino_review" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.casino_review')}</Link></li>
              </ul>
            </div>

            {/* Categories */}
            <div>
              <h4 className="text-lg font-semibold mb-4">{t('nav.categories')}</h4>
              <ul className="space-y-2 text-sm">
                <li><Link to="/category/strategy_article" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.strategy_article')}</Link></li>
                <li><Link to="/category/industry_news" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.industry_news')}</Link></li>
                <li><Link to="/category/bonus_analysis" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.bonus_analysis')}</Link></li>
                <li><Link to="/category/sports_betting" className="text-slate-300 hover:text-white transition-colors">{t('contentTypes.sports_betting')}</Link></li>
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h4 className="text-lg font-semibold mb-4">{t('footer.legal')}</h4>
              <ul className="space-y-2 text-sm">
                <li><a href="#" className="text-slate-300 hover:text-white transition-colors">{t('footer.privacyPolicy')}</a></li>
                <li><a href="#" className="text-slate-300 hover:text-white transition-colors">{t('footer.termsOfService')}</a></li>
                <li><a href="#" className="text-slate-300 hover:text-white transition-colors">{t('footer.responsibleGaming')}</a></li>
                <li><a href="#" className="text-slate-300 hover:text-white transition-colors">{t('footer.contactUs')}</a></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-blue-500/30 mt-8 pt-8 text-center">
            <p className="text-slate-400 text-sm">
              {t('footer.copyright')} | 
              <span className="text-slate-300"> {t('footer.gambleResponsibly')}</span>
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PublicArticleDetail;
