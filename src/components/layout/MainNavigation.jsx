import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguagePath, useCurrentLanguage } from '../../utils/languageUtils';
import { 
  Bars3Icon, 
  XMarkIcon,
  HomeIcon,
  SparklesIcon,
  BuildingOffice2Icon,
  PuzzlePieceIcon,
  TrophyIcon,
  GiftIcon,
  NewspaperIcon,
  ScaleIcon
} from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';
import LanguageSwitcher from '../LanguageSwitcher';

const MainNavigation = () => {
  const { t } = useTranslation();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [contentTypes, setContentTypes] = useState([]);
  const location = useLocation();
  const getLanguagePath = useLanguagePath();
  const currentLanguage = useCurrentLanguage();

  useEffect(() => {
    fetchContentTypes();
  }, [currentLanguage]);

  const fetchContentTypes = async () => {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}/api/public/content-types?language=${currentLanguage}`);
      if (response.ok) {
        const data = await response.json();
        setContentTypes(data.contentTypes);
      }
    } catch (error) {
      console.error('Error fetching content types:', error);
    }
  };

  const mainNavItems = [
    {
      name: t('nav.home'),
      href: getLanguagePath(''),
      icon: HomeIcon,
      current: location.pathname.endsWith('/')
    },
    {
      name: t('nav.casino'),
      href: getLanguagePath('category/casino_review'),
      icon: BuildingOffice2Icon,
      current: location.pathname.includes('/category/casino_review')
    },
    {
      name: t('nav.game'),
      href: getLanguagePath('category/game_guide'),
      icon: PuzzlePieceIcon,
      current: location.pathname.includes('/category/game_guide')
    },
    {
      name: t('nav.sports'),
      href: getLanguagePath('category/sports_betting'),
      icon: TrophyIcon,
      current: location.pathname.includes('/category/sports_betting')
    },
    {
      name: t('nav.bonus'),
      href: getLanguagePath('category/bonus_analysis'),
      icon: GiftIcon,
      current: location.pathname.includes('/category/bonus_analysis')
    },
    {
      name: t('nav.news'),
      href: getLanguagePath('category/industry_news'),
      icon: NewspaperIcon,
      current: location.pathname.includes('/category/industry_news')
    },
    {
      name: t('nav.regulatory'),
      href: getLanguagePath('category/regulatory_update'),
      icon: ScaleIcon,
      current: location.pathname.includes('/category/regulatory_update')
    }
  ];

  return (
    <nav className="glass-nav z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                <SparklesIcon className="w-6 h-6 text-white" />
              </div>
              <div className="hidden md:block">
                <h1 className="text-xl font-bold text-white">{t('nav.brand')}</h1>
                <p className="text-xs text-slate-400 whitespace-nowrap">{t('nav.subtitle')}</p>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {mainNavItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                    item.current
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-white border border-cyan-500/30'
                      : 'text-slate-300 hover:text-white glass-interactive'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {/* Language Switcher */}
            <LanguageSwitcher />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="p-2 rounded-lg text-slate-300 hover:text-white hover:bg-cyan-600/50 transition-colors"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="md:hidden glass-nav border-t border-slate-500/30">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {mainNavItems.map((item) => {
              const Icon = item.icon;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                    item.current
                      ? 'bg-gradient-to-r from-blue-500/20 to-cyan-500/20 text-white border border-cyan-500/30'
                      : 'text-slate-300 hover:text-white glass-interactive'
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.name}</span>
                </Link>
              );
            })}

            {/* Mobile Language Switcher */}
            <div className="pt-2 mt-2 border-t border-slate-500/30">
              <div className="px-3 py-2">
                <LanguageSwitcher />
              </div>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default MainNavigation;