import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeftIcon, CogIcon, DocumentTextIcon, NewspaperIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { CONTENT_TYPE_CONFIG } from '../utils/contentTypeUtils';
import ArticleEditor from './pages/ArticleEditor';
import AuthenticatedLayout from './layout/AuthenticatedLayout';

const AdminPanel = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('articles');
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [settingsLoading, setSettingsLoading] = useState(false);
  
  // Article management state
  const [articles, setArticles] = useState([]);
  const [articleStats, setArticleStats] = useState({});
  const [editingArticle, setEditingArticle] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState(null);
  const [articleFilters, setArticleFilters] = useState({
    status: 'all',
    contentType: 'all',
    search: '',
    sortBy: 'updated_at',
    sortOrder: 'DESC',
    page: 1,
    limit: 10
  });
  
  // Website images state
  const [websiteImages, setWebsiteImages] = useState({
    hero: {
      main: '',
      tech: '',
      business: '',
      gaming: ''
    },
    contentTypes: {}
  });
  const [imageUploading, setImageUploading] = useState(false);
  const [imageUploadError, setImageUploadError] = useState('');

  // Check if user is admin
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="casino-card p-8 text-center">
            <h1 className="text-2xl font-bold text-casino-gold-100 mb-4">Access Denied</h1>
            <p className="text-casino-gold-400 mb-6">You don't have permission to access the admin panel.</p>
            <Link to="/dashboard" className="btn-casino-primary">
              Back to Dashboard
            </Link>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  const makeRequest = async (endpoint, options = {}) => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://writer777-production.up.railway.app' 
      : 'http://localhost:3008';
    
    const response = await fetch(`${baseURL}/api/admin${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`Request failed: ${response.statusText}`);
    }

    return response.json();
  };

  const makeArticleRequest = async (endpoint, options = {}) => {
    const token = localStorage.getItem('token');
    const baseURL = process.env.NODE_ENV === 'production' 
      ? 'https://writer777-production.up.railway.app' 
      : 'http://localhost:3008';
    
    const response = await fetch(`${baseURL}/api/articles${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      },
      ...options
    });

    if (!response.ok) {
      throw new Error(`Request failed: ${response.statusText}`);
    }

    return response.json();
  };


  const loadSettings = async () => {
    try {
      const response = await makeRequest('/settings');
      setSettings(response.data || {});
    } catch (error) {
      setMessage({ text: `Failed to load settings: ${error.message}`, type: 'error' });
    }
  };

  const updateModel = async (newModel) => {
    setSettingsLoading(true);
    try {
      await makeRequest('/settings', {
        method: 'PUT',
        body: JSON.stringify({ ai_model: newModel })
      });
      setMessage({ text: 'AI Model updated successfully!', type: 'success' });
      loadSettings();
      // Clear the input after successful update
      const input = document.getElementById('ai-model-input');
      if (input) input.value = newModel;
    } catch (error) {
      setMessage({ text: `Failed to update model: ${error.message}`, type: 'error' });
    } finally {
      setSettingsLoading(false);
    }
  };

  const updateApiKey = async (newApiKey) => {
    setSettingsLoading(true);
    try {
      await makeRequest('/settings', {
        method: 'PUT',
        body: JSON.stringify({ api_key: newApiKey })
      });
      setMessage({ text: 'API Key updated successfully!', type: 'success' });
      loadSettings();
    } catch (error) {
      setMessage({ text: `Failed to update API key: ${error.message}`, type: 'error' });
    } finally {
      setSettingsLoading(false);
    }
  };


  const loadArticles = async () => {
    try {
      const queryParams = new URLSearchParams({
        page: articleFilters.page,
        limit: articleFilters.limit,
        sortBy: articleFilters.sortBy,
        sortOrder: articleFilters.sortOrder,
        ...(articleFilters.status !== 'all' && { status: articleFilters.status }),
        ...(articleFilters.contentType !== 'all' && { content_type: articleFilters.contentType }),
        ...(articleFilters.search && { search: articleFilters.search })
      });

      const response = await makeArticleRequest(`?${queryParams}`);
      setArticles(response.articles || []);
    } catch (error) {
      setMessage({ text: `Failed to load articles: ${error.message}`, type: 'error' });
    }
  };

  const loadArticleStats = async () => {
    try {
      const response = await makeArticleRequest('/admin/stats');
      setArticleStats(response.stats || {});
    } catch (error) {
      console.error('Failed to load article stats:', error);
    }
  };

  const handleDeleteArticle = async (id) => {
    try {
      await makeArticleRequest(`/${id}`, { method: 'DELETE' });
      setArticles(articles.filter(a => a.id !== id));
      setDeleteConfirm(null);
      loadArticleStats();
      setMessage({ text: 'Article deleted successfully!', type: 'success' });
    } catch (error) {
      setMessage({ text: `Failed to delete article: ${error.message}`, type: 'error' });
    }
  };

  const handleStatusToggle = async (article) => {
    try {
      const newStatus = article.status === 'published' ? 'draft' : 'published';
      
      await makeArticleRequest(`/${article.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          title: article.title,
          content: article.content,
          content_type: article.content_type,
          status: newStatus
        })
      });

      setArticles(articles.map(a => 
        a.id === article.id ? { ...a, status: newStatus } : a
      ));
      loadArticleStats();
      setMessage({ text: 'Article status updated successfully!', type: 'success' });
    } catch (error) {
      setMessage({ text: `Failed to update article status: ${error.message}`, type: 'error' });
    }
  };

  const loadFullArticle = async (articleId) => {
    try {
      const response = await makeArticleRequest(`/${articleId}`);
      return response.article;
    } catch (error) {
      setMessage({ text: `Failed to load article: ${error.message}`, type: 'error' });
      return null;
    }
  };

  const handleEditArticle = async (article) => {
    setLoading(true);
    try {
      const fullArticle = await loadFullArticle(article.id);
      if (fullArticle) {
        setEditingArticle(fullArticle);
      }
    } catch (error) {
      setMessage({ text: `Failed to load article for editing: ${error.message}`, type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleEditSave = (savedArticle) => {
    if (editingArticle && editingArticle.id) {
      setArticles(articles.map(a => 
        a.id === savedArticle.id ? { ...a, ...savedArticle } : a
      ));
    } else {
      setArticles([savedArticle, ...articles]);
    }
    setEditingArticle(null);
    loadArticleStats();
    setMessage({ text: 'Article saved successfully!', type: 'success' });
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Website images management functions
  const loadWebsiteImages = async () => {
    try {
      const response = await makeRequest('/website-images');
      if (response.data) {
        setWebsiteImages(response.data);
      }
    } catch (error) {
      console.warn('Website images not yet configured:', error.message);
      // Use default empty state if no images configured yet
    }
  };

  const handleImageUpload = async (file, category, type = 'main') => {
    setImageUploading(true);
    setImageUploadError('');

    try {
      const formData = new FormData();
      formData.append('image', file);

      const token = localStorage.getItem('token');
      const baseURL = process.env.NODE_ENV === 'production' 
        ? 'https://writer777-production.up.railway.app' 
        : 'http://localhost:3008';

      const response = await fetch(`${baseURL}/api/upload/image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();
      const uploadedUrl = `${baseURL}${result.fileUrl}`;
      
      // Update the image in state
      const updatedImages = { ...websiteImages };
      if (category === 'hero') {
        updatedImages.hero[type] = uploadedUrl;
      } else if (category === 'contentTypes') {
        if (!updatedImages.contentTypes) updatedImages.contentTypes = {};
        updatedImages.contentTypes[type] = uploadedUrl;
      }
      
      setWebsiteImages(updatedImages);
      
      // Save to backend
      await saveWebsiteImages(updatedImages);
      
      setMessage({ text: 'Image uploaded successfully!', type: 'success' });
      
    } catch (err) {
      setImageUploadError(err.message);
      setMessage({ text: `Upload failed: ${err.message}`, type: 'error' });
    } finally {
      setImageUploading(false);
    }
  };

  const saveWebsiteImages = async (images) => {
    try {
      await makeRequest('/website-images', {
        method: 'PUT',
        body: JSON.stringify({ images })
      });
    } catch (error) {
      throw new Error('Failed to save image settings');
    }
  };

  const handleFileSelect = (e, category, type) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setImageUploadError('Only image files are allowed (JPEG, PNG, GIF, WebP)');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setImageUploadError('File too large. Maximum size is 5MB.');
        return;
      }

      handleImageUpload(file, category, type);
    }
    
    // Clear the file input
    e.target.value = '';
  };

  const getContentTypeLabel = (contentType) => {
    const config = CONTENT_TYPE_CONFIG[contentType];
    return config ? config.label : contentType;
  };

  const getContentTypeColor = (contentType) => {
    const config = CONTENT_TYPE_CONFIG[contentType];
    return config ? config.color : 'bg-gray-500';
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        await loadSettings();
        if (activeTab === 'articles') {
          await Promise.all([loadArticles(), loadArticleStats()]);
        } else if (activeTab === 'website') {
          await loadWebsiteImages();
        }
      } catch (error) {
        setMessage({ text: `Failed to load admin data: ${error.message}`, type: 'error' });
      }
      setLoading(false);
    };

    loadData();
  }, []);

  useEffect(() => {
    if (activeTab === 'articles') {
      loadArticles();
      loadArticleStats();
    } else if (activeTab === 'website') {
      loadWebsiteImages();
    }
  }, [activeTab, articleFilters]);

  if (loading) {
    return (
      <AuthenticatedLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-casino-gold-500 mx-auto"></div>
            <p className="mt-4 text-casino-gold-400">Loading admin panel...</p>
          </div>
        </div>
      </AuthenticatedLayout>
    );
  }

  return (
    <AuthenticatedLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-casino-gold-100 mb-2">Admin Panel</h1>
          <p className="text-casino-gold-400">Manage templates, settings, and content</p>
        </div>

        {/* Message Display */}
        {message.text && (
          <div className={`mb-6 p-4 rounded-xl ${
            message.type === 'success' 
              ? 'bg-green-500/20 border border-green-500/30 text-green-300' 
              : 'bg-red-500/20 border border-red-500/30 text-red-300'
          }`}>
            {message.text}
          </div>
        )}

        {/* Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'settings'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <CogIcon className="w-5 h-5" />
              <span>Settings</span>
            </button>
            <button
              onClick={() => setActiveTab('articles')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'articles'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <NewspaperIcon className="w-5 h-5" />
              <span>Articles</span>
            </button>
            <button
              onClick={() => setActiveTab('website')}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                activeTab === 'website'
                  ? 'bg-casino-gold-500 text-casino-dark-900 font-semibold'
                  : 'text-casino-gold-300 hover:text-casino-gold-100'
              }`}
            >
              <PhotoIcon className="w-5 h-5" />
              <span>Website Images</span>
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">

          {activeTab === 'settings' && (
            <div className="bg-casino-dark-700 rounded-lg p-6">
              <h2 className="text-xl font-semibold text-casino-gold-100 mb-6">System Settings</h2>
              <div className="space-y-6">
                <div className="border border-casino-gold-500/20 rounded-lg p-4">
                  <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                    AI Model
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="text"
                      id="ai-model-input"
                      placeholder="Enter AI model (e.g. gemini-pro, gemini-1.5-pro)"
                      className="bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 flex-1"
                      defaultValue={settings.ai_model || ''}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const value = e.target.value.trim();
                          if (!value) {
                            setMessage({ text: 'Please enter an AI model name', type: 'error' });
                            return;
                          }
                          if (value === settings.ai_model) {
                            setMessage({ text: 'No changes to save', type: 'error' });
                            return;
                          }
                          updateModel(value);
                        }
                      }}
                    />
                    <button
                      onClick={() => {
                        const input = document.getElementById('ai-model-input');
                        const value = input.value.trim();
                        if (!value) {
                          setMessage({ text: 'Please enter an AI model name', type: 'error' });
                          return;
                        }
                        if (value === settings.ai_model) {
                          setMessage({ text: 'No changes to save', type: 'error' });
                          return;
                        }
                        updateModel(value);
                      }}
                      disabled={settingsLoading}
                      className={`px-4 py-2 rounded-lg font-semibold transition-colors ${
                        settingsLoading
                          ? 'bg-casino-gold-300 text-casino-dark-700 cursor-not-allowed'
                          : 'bg-casino-gold-500 text-casino-dark-900 hover:bg-casino-gold-400'
                      }`}
                    >
                      {settingsLoading ? 'Updating...' : 'Update'}
                    </button>
                  </div>
                  <div className="mt-2 text-casino-gold-400 text-sm">
                    Current: {settings.ai_model || 'Not configured'}
                  </div>
                </div>

                <div className="border border-casino-gold-500/20 rounded-lg p-4">
                  <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                    API Key
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="password"
                      id="api-key-input"
                      placeholder="Enter new API key"
                      className="bg-casino-dark-600 border border-casino-gold-500/30 rounded-lg px-3 py-2 text-casino-gold-100 flex-1"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const value = e.target.value.trim();
                          if (!value) {
                            setMessage({ text: 'Please enter an API key', type: 'error' });
                            return;
                          }
                          if (value.length < 10) {
                            setMessage({ text: 'API key seems too short (minimum 10 characters)', type: 'error' });
                            return;
                          }
                          updateApiKey(value);
                          e.target.value = '';
                        }
                      }}
                    />
                    <button
                      onClick={() => {
                        const input = document.getElementById('api-key-input');
                        const value = input.value.trim();
                        if (!value) {
                          setMessage({ text: 'Please enter an API key', type: 'error' });
                          return;
                        }
                        if (value.length < 10) {
                          setMessage({ text: 'API key seems too short (minimum 10 characters)', type: 'error' });
                          return;
                        }
                        updateApiKey(value);
                        input.value = '';
                      }}
                      disabled={settingsLoading}
                      className={`px-4 py-2 rounded-lg font-semibold transition-colors ${
                        settingsLoading
                          ? 'bg-casino-gold-300 text-casino-dark-700 cursor-not-allowed'
                          : 'bg-casino-gold-500 text-casino-dark-900 hover:bg-casino-gold-400'
                      }`}
                    >
                      {settingsLoading ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                  <div className="mt-2 text-casino-gold-400 text-sm">
                    Status: {settings.api_key ? 'Configured' : 'Not configured'}
                  </div>
                </div>

                <div className="border border-casino-gold-500/20 rounded-lg p-4">
                  <label className="block text-casino-gold-300 text-sm font-medium mb-2">
                    System Status
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-4">
                      <span className="text-green-400">✓ System is running normally</span>
                    </div>
                    <div className="text-casino-gold-400 text-sm">
                      Active User: {user.email}
                    </div>
                    <div className="text-casino-gold-400 text-sm">
                      Environment: {process.env.NODE_ENV === 'production' ? 'Production' : 'Development'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'articles' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-casino-gold-100">Article Management</h2>
                <div className="text-casino-gold-400 text-sm">
                  Total: {articleStats.total || 0} | 
                  Published: {articleStats.published || 0} | 
                  Drafts: {articleStats.drafts || 0}
                </div>
              </div>

              {/* Article Filters */}
              <div className="bg-casino-dark-700 p-4 rounded-lg">
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-casino-gold-300 text-sm mb-1">Status</label>
                    <select
                      value={articleFilters.status}
                      onChange={(e) => setArticleFilters({...articleFilters, status: e.target.value, page: 1})}
                      className="w-full bg-casino-dark-600 border border-casino-gold-500/30 rounded px-3 py-2 text-casino-gold-100 text-sm"
                    >
                      <option value="all">All Status</option>
                      <option value="published">Published</option>
                      <option value="draft">Draft</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-casino-gold-300 text-sm mb-1">Content Type</label>
                    <select
                      value={articleFilters.contentType}
                      onChange={(e) => setArticleFilters({...articleFilters, contentType: e.target.value, page: 1})}
                      className="w-full bg-casino-dark-600 border border-casino-gold-500/30 rounded px-3 py-2 text-casino-gold-100 text-sm"
                    >
                      <option value="all">All Types</option>
                      {Object.entries(CONTENT_TYPE_CONFIG).map(([key, config]) => (
                        <option key={key} value={key}>{config.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-casino-gold-300 text-sm mb-1">Sort By</label>
                    <select
                      value={articleFilters.sortBy}
                      onChange={(e) => setArticleFilters({...articleFilters, sortBy: e.target.value})}
                      className="w-full bg-casino-dark-600 border border-casino-gold-500/30 rounded px-3 py-2 text-casino-gold-100 text-sm"
                    >
                      <option value="updated_at">Last Updated</option>
                      <option value="created_at">Created Date</option>
                      <option value="title">Title</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-casino-gold-300 text-sm mb-1">Search</label>
                    <input
                      type="text"
                      value={articleFilters.search}
                      onChange={(e) => setArticleFilters({...articleFilters, search: e.target.value, page: 1})}
                      placeholder="Search articles..."
                      className="w-full bg-casino-dark-600 border border-casino-gold-500/30 rounded px-3 py-2 text-casino-gold-100 text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Articles List */}
              {articles.length === 0 ? (
                <div className="text-center py-12">
                  <NewspaperIcon className="w-16 h-16 text-casino-gold-500/50 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-casino-gold-300 mb-2">No Articles Found</h3>
                  <p className="text-casino-gold-400 mb-4">
                    {articleFilters.status !== 'all' || articleFilters.contentType !== 'all' || articleFilters.search
                      ? 'Try adjusting your filters to see more articles.'
                      : 'Articles created by users will appear here.'}
                  </p>
                  {(articleFilters.status !== 'all' || articleFilters.contentType !== 'all' || articleFilters.search) && (
                    <button
                      onClick={() => setArticleFilters({
                        status: 'all',
                        contentType: 'all',
                        search: '',
                        sortBy: 'updated_at',
                        sortOrder: 'DESC',
                        page: 1,
                        limit: 10
                      })}
                      className="bg-casino-gold-500 text-casino-dark-900 px-4 py-2 rounded-lg font-semibold hover:bg-casino-gold-400 transition-colors"
                    >
                      Clear Filters
                    </button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {articles.map((article) => (
                    <div key={article.id} className="bg-casino-dark-700 p-6 rounded-lg border border-casino-gold-500/20 hover:border-casino-gold-500/40 transition-colors">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h3 className="font-semibold text-casino-gold-100 mb-2">{article.title}</h3>
                          <div className="flex items-center space-x-4 text-sm text-casino-gold-400">
                            <span className={`px-2 py-1 rounded ${getContentTypeColor(article.content_type)}`}>
                              {getContentTypeLabel(article.content_type)}
                            </span>
                            <span className={`px-2 py-1 rounded ${
                              article.status === 'published' ? 'bg-green-500/20 text-green-300' : 'bg-yellow-500/20 text-yellow-300'
                            }`}>
                              {article.status}
                            </span>
                            <span>Updated: {formatDate(article.updated_at)}</span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleEditArticle(article)}
                            className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 text-sm transition-colors"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleStatusToggle(article)}
                            className={`px-3 py-1 rounded text-sm transition-colors ${
                              article.status === 'published' 
                                ? 'bg-yellow-600 hover:bg-yellow-700 text-white'
                                : 'bg-green-600 hover:bg-green-700 text-white'
                            }`}
                          >
                            {article.status === 'published' ? 'Unpublish' : 'Publish'}
                          </button>
                          <button
                            onClick={() => setDeleteConfirm(article)}
                            className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700 text-sm transition-colors"
                          >
                            Delete
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {activeTab === 'website' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-xl font-semibold text-casino-gold-100">Website Images Management</h2>
                <p className="text-casino-gold-400 text-sm">Upload and manage hero backgrounds and content type images</p>
              </div>

              {/* Upload Error Display */}
              {imageUploadError && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                  <div className="flex">
                    <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                    <p className="ml-3 text-sm text-red-800">{imageUploadError}</p>
                    <button
                      onClick={() => setImageUploadError('')}
                      className="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg p-1.5 hover:bg-red-100 inline-flex h-8 w-8"
                    >
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              )}

              {/* Hero Section Images */}
              <div className="bg-casino-dark-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-casino-gold-100 mb-4">Hero Section Backgrounds</h3>
                <p className="text-casino-gold-400 text-sm mb-6">Upload background images for different hero section themes (recommended: 1200x600px)</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {[
                    { key: 'main', label: 'Main Hero', description: 'Primary homepage background' },
                    { key: 'tech', label: 'Tech Theme', description: 'Technology-focused background' },
                    { key: 'business', label: 'Business Theme', description: 'Business-oriented background' },
                    { key: 'gaming', label: 'Gaming Theme', description: 'Gaming-focused background' }
                  ].map((heroType) => (
                    <div key={heroType.key} className="border border-casino-gold-500/20 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-casino-gold-100">{heroType.label}</h4>
                          <p className="text-sm text-casino-gold-400">{heroType.description}</p>
                        </div>
                        <label className="bg-casino-gold-500 text-casino-dark-900 px-3 py-2 rounded-md text-sm cursor-pointer hover:bg-casino-gold-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-casino-gold-500">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileSelect(e, 'hero', heroType.key)}
                            className="sr-only"
                            disabled={imageUploading}
                          />
                          {imageUploading ? (
                            <span className="flex items-center">
                              <svg className="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Uploading...
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <PhotoIcon className="mr-1 h-3 w-3" />
                              Upload
                            </span>
                          )}
                        </label>
                      </div>
                      
                      {websiteImages.hero[heroType.key] && (
                        <div className="mt-3">
                          <p className="text-xs text-casino-gold-400 mb-2">Current image:</p>
                          <img
                            src={websiteImages.hero[heroType.key]}
                            alt={`${heroType.label} preview`}
                            className="w-full h-24 object-cover rounded border border-casino-gold-500/30"
                            onError={(e) => {
                              e.target.style.display = 'none';
                            }}
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              {/* Content Type Images */}
              <div className="bg-casino-dark-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-casino-gold-100 mb-4">Content Type Backgrounds</h3>
                <p className="text-casino-gold-400 text-sm mb-6">Upload background images for different content categories (recommended: 800x400px)</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {Object.entries(CONTENT_TYPE_CONFIG).map(([contentType, config]) => (
                    <div key={contentType} className="border border-casino-gold-500/20 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-casino-gold-100">{config.label}</h4>
                          <p className="text-sm text-casino-gold-400">Background for {config.label.toLowerCase()} articles</p>
                        </div>
                        <label className="bg-casino-gold-500 text-casino-dark-900 px-3 py-2 rounded-md text-sm cursor-pointer hover:bg-casino-gold-400 focus-within:outline-none focus-within:ring-2 focus-within:ring-casino-gold-500">
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleFileSelect(e, 'contentTypes', contentType)}
                            className="sr-only"
                            disabled={imageUploading}
                          />
                          {imageUploading ? (
                            <span className="flex items-center">
                              <svg className="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Uploading...
                            </span>
                          ) : (
                            <span className="flex items-center">
                              <PhotoIcon className="mr-1 h-3 w-3" />
                              Upload
                            </span>
                          )}
                        </label>
                      </div>
                      
                      {websiteImages.contentTypes && websiteImages.contentTypes[contentType] && (
                        <div className="mt-3">
                          <p className="text-xs text-casino-gold-400 mb-2">Current image:</p>
                          <img
                            src={websiteImages.contentTypes[contentType]}
                            alt={`${config.label} preview`}
                            className="w-full h-20 object-cover rounded border border-casino-gold-500/30"
                            onError={(e) => {
                              e.target.style.display = 'none';
                            }}
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>


        {/* Article Editor Modal */}
        {editingArticle && (
          <ArticleEditor
            article={editingArticle}
            onSave={handleEditSave}
            onCancel={() => setEditingArticle(null)}
          />
        )}

        {/* Delete Confirmation Modal */}
        {deleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-casino-dark-800">
              <div className="mt-3 text-center">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-500/20">
                  <svg className="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-casino-gold-100 mt-4">Confirm Delete</h3>
                <p className="text-sm text-casino-gold-400 mt-2">
                  Are you sure you want to delete the article "{deleteConfirm.title}"? This action cannot be undone.
                </p>
                <div className="flex justify-center space-x-3 mt-6">
                  <button
                    onClick={() => setDeleteConfirm(null)}
                    className="px-4 py-2 bg-casino-dark-600 text-casino-gold-300 rounded-lg hover:bg-casino-dark-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => handleDeleteArticle(deleteConfirm.id)}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AuthenticatedLayout>
  );
};

export default AdminPanel;