import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  StarIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  DevicePhoneMobileIcon,
  ChatBubbleLeftRightIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  CreditCardIcon,
  GlobeAltIcon,
  InformationCircleIcon,
  QuestionMarkCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CameraIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';
import { extractSEOData, cleanArticleContent } from '../../utils/extractArticleData';

/**
 * Professional Casino Review Layout - Comprehensive casino review design
 * Based on industry standards from AskGamblers, Casino.guru, etc.
 */
const CasinoReviewLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  const { t } = useTranslation();
  const [extractedData, setExtractedData] = useState(null);
  const [activeSection, setActiveSection] = useState('overview');
  const [seoData, setSeoData] = useState(null);
  const [cleanedContent, setCleanedContent] = useState('');
  const [expandedFAQ, setExpandedFAQ] = useState({});
  const [selectedImage, setSelectedImage] = useState(null);

  useEffect(() => {
    if (article?.content) {
      const data = extractCasinoData(article.content);
      setExtractedData(data);
      
      // Extract SEO data and clean content
      const seo = extractSEOData(article.content);
      setSeoData(seo);
      
      const cleaned = cleanArticleContent(article.content);
      setCleanedContent(cleaned);
    }
  }, [article?.content]);

  // Update document title and meta description with SEO data
  useEffect(() => {
    if (seoData?.title) {
      document.title = seoData.title;
    }
    
    if (seoData?.description) {
      // Update or create meta description
      let metaDescription = document.querySelector('meta[name="description"]');
      if (!metaDescription) {
        metaDescription = document.createElement('meta');
        metaDescription.name = 'description';
        document.head.appendChild(metaDescription);
      }
      metaDescription.content = seoData.description;
    }

    // Update or create meta keywords
    if (seoData?.keywords && seoData.keywords.length > 0) {
      let metaKeywords = document.querySelector('meta[name="keywords"]');
      if (!metaKeywords) {
        metaKeywords = document.createElement('meta');
        metaKeywords.name = 'keywords';
        document.head.appendChild(metaKeywords);
      }
      metaKeywords.content = seoData.keywords.join(', ');
    }

    // Cleanup function to restore original title when component unmounts
    return () => {
      document.title = 'Writer 777';
    };
  }, [seoData]);

  // Guard against undefined article
  if (!article) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading casino review...</p>
        </div>
      </div>
    );
  }

  // Extract comprehensive casino data from content
  const extractCasinoData = (content) => {
    // Extract ratings
    const ratingPatterns = {
      overall: /overall.*?rating.*?(\d+(?:\.\d+)?)/i,
      games: /games.*?rating.*?(\d+(?:\.\d+)?)/i,
      bonuses: /bonus.*?rating.*?(\d+(?:\.\d+)?)/i,
      support: /support.*?rating.*?(\d+(?:\.\d+)?)/i,
      security: /security.*?rating.*?(\d+(?:\.\d+)?)/i,
      mobile: /mobile.*?rating.*?(\d+(?:\.\d+)?)/i,
      payments: /payment.*?rating.*?(\d+(?:\.\d+)?)/i,
      usability: /usability.*?rating.*?(\d+(?:\.\d+)?)/i
    };

    const ratings = {};
    Object.entries(ratingPatterns).forEach(([key, pattern]) => {
      const match = content.match(pattern);
      if (match) {
        ratings[key] = parseFloat(match[1]);
      }
    });

    // Extract casino info JSON block first
    const casinoInfoMatch = content.match(/<!-- CASINO_INFO_START -->([\s\S]*?)<!-- CASINO_INFO_END -->/i);
    let casinoInfoFromContent = null;
    
    if (casinoInfoMatch) {
      try {
        casinoInfoFromContent = JSON.parse(casinoInfoMatch[1].trim());
      } catch (error) {
        console.error('Error parsing casino info JSON:', error);
      }
    }

    // Extract pros and cons from content or JSON
    let pros = [];
    let cons = [];
    
    if (casinoInfoFromContent?.pros) {
      pros = casinoInfoFromContent.pros;
    } else {
      const prosMatch = content.match(/(?:pros?|advantages?)[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/i);
      pros = prosMatch ? 
        prosMatch[1].split(/[-*•]\s*/).filter(item => item.trim()).map(item => item.trim()) : 
        [];
    }
    
    if (casinoInfoFromContent?.cons) {
      cons = casinoInfoFromContent.cons;
    } else {
      const consMatch = content.match(/(?:cons?|disadvantages?)[:\s]*\n?((?:[-*•]\s*.*?\n?)+)/i);
      cons = consMatch ? 
        consMatch[1].split(/[-*•]\s*/).filter(item => item.trim()).map(item => item.trim()) : 
        [];
    }

    // Extract key facts
    const licenseMatch = content.match(/licens[e|ed].*?by[:\s]*(.*?)(?:\n|\.)/i);
    const establishedMatch = content.match(/established[:\s]*(\d{4})/i);
    const ownerMatch = content.match(/owner[:\s]*(.*?)(?:\n|\.)/i);

    // Use JSON data if available, otherwise fallback to extracted or default values
    return {
      ratings: casinoInfoFromContent?.ratings || (Object.keys(ratings).length > 0 ? ratings : {
        overall: 4.2,
        games: 4.5,
        bonuses: 4.0,
        support: 3.8,
        security: 4.7,
        mobile: 4.1,
        payments: 4.3,
        usability: 4.4
      }),
      pros: pros.length > 0 ? pros : [
        'Wide selection of games',
        'Generous welcome bonus',
        'Multiple payment options',
        'Mobile-friendly platform'
      ],
      cons: cons.length > 0 ? cons : [
        'Limited live chat hours',
        'Withdrawal processing time',
        'Geographic restrictions'
      ],
      license: casinoInfoFromContent?.license || (licenseMatch ? licenseMatch[1].trim() : 'Curacao Gaming License'),
      established: casinoInfoFromContent?.established || (establishedMatch ? establishedMatch[1] : '2020'),
      owner: casinoInfoFromContent?.owner || (ownerMatch ? ownerMatch[1].trim() : 'Unknown'),
      welcome_bonus: casinoInfoFromContent?.welcome_bonus || '100% up to $500 + Free Spins',
      total_games: casinoInfoFromContent?.total_games || '2000+',
      support_hours: casinoInfoFromContent?.support_hours || '24/7',
      min_deposit: casinoInfoFromContent?.min_deposit || '$10',
      withdrawal_time: casinoInfoFromContent?.withdrawal_time || '1-3 business days',
      screenshots: casinoInfoFromContent?.screenshots || [],
      faq: casinoInfoFromContent?.faq || [
        {
          question: 'Is this casino legitimate and safe?',
          answer: 'Yes, the casino is licensed and uses SSL encryption to protect player data. The casino follows strict regulatory guidelines to ensure fair play and secure transactions.'
        },
        {
          question: 'What payment methods are accepted?',
          answer: 'The casino accepts various payment methods including Visa, Mastercard, e-wallets like Skrill and Neteller, cryptocurrencies, and bank transfers. Check the banking section for full details.'
        },
        {
          question: 'How long do withdrawals take?',
          answer: 'Withdrawal times vary by payment method. E-wallets typically process within 24 hours, while bank transfers may take 3-5 business days.'
        },
        {
          question: 'Can I play on mobile devices?',
          answer: 'Yes, the casino is fully optimized for mobile browsers and offers a seamless gaming experience on both iOS and Android devices.'
        },
        {
          question: 'What games are available?',
          answer: 'The casino offers a wide selection of games including slots, table games, live dealer games from top providers like NetEnt, Microgaming, and Evolution Gaming.'
        }
      ]
    };
  };

  const renderStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarIconSolid key={i} className="w-5 h-5 text-yellow-400" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative w-5 h-5">
            <StarIcon className="w-5 h-5 text-gray-300 absolute" />
            <div className="overflow-hidden w-1/2">
              <StarIconSolid className="w-5 h-5 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(<StarIcon key={i} className="w-5 h-5 text-gray-300" />);
      }
    }
    return stars;
  };

  const ratingCategories = [
    { key: 'games', label: 'Game Library', icon: '🎮', description: 'Quality and variety of games' },
    { key: 'bonuses', label: 'Bonuses', icon: '🎁', description: 'Welcome offers and promotions' },
    { key: 'payments', label: 'Banking', icon: '💳', description: 'Deposit and withdrawal options' },
    { key: 'support', label: 'Support', icon: '💬', description: 'Customer service quality' },
    { key: 'security', label: 'Security', icon: '🛡️', description: 'Safety and licensing' },
    { key: 'mobile', label: 'Mobile', icon: '📱', description: 'Mobile gaming experience' },
    { key: 'usability', label: 'Usability', icon: '🎯', description: 'User interface and navigation' }
  ];

  const quickFacts = [
    { label: 'Established', value: extractedData?.established || '2020', icon: '📅' },
    { label: 'License', value: extractedData?.license?.replace('Gaming License', '').trim() || 'Curacao', icon: '🛡️' },
    { label: 'Owner', value: extractedData?.owner || 'Gaming Corp', icon: '🏢' },
    { label: 'Games', value: extractedData?.total_games || '2000+', icon: '🎮' },
    { label: 'Min Deposit', value: extractedData?.min_deposit || '$10', icon: '💰' },
    { label: 'Support', value: extractedData?.support_hours || '24/7', icon: '💬' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Casino Hero Section */}
        <div className="mb-12">
          <div 
            className="bg-gradient-to-r from-slate-800/50 to-slate-700/50 backdrop-blur-xl border border-white/10 rounded-3xl p-8 lg:p-12 shadow-2xl relative overflow-hidden"
            style={{
              backgroundImage: article.featured_image 
                ? `linear-gradient(rgba(30, 41, 59, 0.8), rgba(51, 65, 85, 0.8)), url(${article.featured_image})`
                : undefined,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >
            <div className="grid lg:grid-cols-3 gap-8 items-center">
              
              {/* Casino Logo & Basic Info */}
              <div className="lg:col-span-2">
                {/* Content Type Badge */}
                <div className="mb-6">
                  <div className="inline-flex items-center space-x-3 px-6 py-3 rounded-full text-base font-semibold bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg">
                    <span className="text-xl">🎰</span>
                    <span>Casino Review</span>
                  </div>
                </div>

                {/* Casino Name */}
                <h1 className="text-4xl lg:text-6xl font-bold text-white mb-4 leading-tight">
                  {article.title?.replace(' Review', '').replace(' Casino Review', '') || 'Casino Name'}
                </h1>

                {/* Meta Info */}
                <div className="flex flex-wrap items-center gap-6 text-slate-300 text-sm mb-6">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="w-4 h-4" />
                    <span>By {article.author || 'Casino Expert'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarIcon className="w-4 h-4" />
                    <span>{formatDate(article.published_at)}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <ClockIcon className="w-4 h-4" />
                    <span>{estimatedReadTime} min read</span>
                  </div>
                </div>

                {/* Main CTA Buttons */}
                <div className="flex flex-wrap gap-4">
                  <button className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 flex items-center space-x-3 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    <PlayIcon className="w-5 h-5" />
                    <span>Visit Casino</span>
                  </button>
                  <button className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                    Claim Bonus
                  </button>
                </div>
              </div>

              {/* Overall Rating Card */}
              <div className="lg:col-span-1">
                <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 text-center">
                  <h3 className="text-xl font-bold text-white mb-4">Expert Rating</h3>
                  <div className="text-6xl font-bold text-yellow-400 mb-3">
                    {extractedData?.ratings?.overall || '4.2'}
                  </div>
                  <div className="flex justify-center space-x-1 mb-4">
                    {renderStars(extractedData?.ratings?.overall || 4.2)}
                  </div>
                  <p className="text-slate-300 text-sm">
                    Based on {ratingCategories.length} categories
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Facts & Ratings Grid */}
        <div className="grid lg:grid-cols-4 gap-6 mb-12">
          
          {/* Quick Facts Card */}
          <div className="lg:col-span-1">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6 h-full">
              <h3 className="text-xl font-bold text-white mb-6 flex items-center">
                <InformationCircleIcon className="w-6 h-6 mr-2 text-blue-400" />
                Quick Facts
              </h3>
              <div className="space-y-4">
                {quickFacts.map((fact, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{fact.icon}</span>
                      <span className="text-slate-300 text-sm">{fact.label}</span>
                    </div>
                    <span className="text-white font-semibold text-sm">{fact.value}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Detailed Ratings */}
          <div className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
              <h3 className="text-xl font-bold text-white mb-6">Detailed Ratings</h3>
              <div className="grid md:grid-cols-2 gap-4">
                {ratingCategories.map((category) => (
                  <div key={category.key} className="bg-white/5 rounded-xl p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{category.icon}</span>
                        <span className="text-white font-medium">{category.label}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex space-x-1">
                          {renderStars(extractedData?.ratings?.[category.key] || 4.0)}
                        </div>
                        <span className="text-yellow-400 font-bold text-lg min-w-[2rem]">
                          {extractedData?.ratings?.[category.key] || '4.0'}
                        </span>
                      </div>
                    </div>
                    <p className="text-slate-400 text-xs">{category.description}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Pros & Cons Section */}
        <div className="grid md:grid-cols-2 gap-6 mb-12">
          {/* Pros */}
          <div className="bg-emerald-500/10 border border-emerald-500/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-emerald-400 mb-6 flex items-center">
              <CheckCircleIcon className="w-6 h-6 mr-2" />
              Pros
            </h3>
            <div className="space-y-3">
              {(extractedData?.pros || []).map((pro, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircleIcon className="w-5 h-5 text-emerald-400 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-200">{pro}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Cons */}
          <div className="bg-red-500/10 border border-red-500/30 rounded-2xl p-6">
            <h3 className="text-xl font-bold text-red-400 mb-6 flex items-center">
              <XCircleIcon className="w-6 h-6 mr-2" />
              Cons
            </h3>
            <div className="space-y-3">
              {(extractedData?.cons || []).map((con, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <XCircleIcon className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                  <span className="text-slate-200">{con}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="grid lg:grid-cols-4 gap-8">
          
          {/* Table of Contents Sidebar */}
          {showTableOfContents && tableOfContents.length > 0 && (
            <aside className="lg:col-span-1">
              <div className="sticky top-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <ListBulletIcon className="w-5 h-5 mr-2" />
                  Contents
                </h3>
                <nav className="space-y-2">
                  {tableOfContents.map((item, index) => (
                    <button
                      key={index}
                      onClick={() => scrollToHeading(item.id)}
                      className={`block w-full text-left text-sm text-slate-300 hover:text-white transition-colors py-2 px-3 rounded-lg hover:bg-white/10 ${
                        item.level > 2 ? 'pl-6' : ''
                      } ${item.level > 3 ? 'pl-10' : ''}`}
                    >
                      {item.text}
                    </button>
                  ))}
                </nav>
              </div>
            </aside>
          )}

          {/* Article Content */}
          <article className="lg:col-span-3">
            <div className="bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8 lg:p-12">
              <div 
                ref={contentRef}
                className="prose prose-xl prose-invert max-w-none"
                dangerouslySetInnerHTML={{ 
                  __html: addHeadingIds(cleanedContent || article.content || '', tableOfContents)
                }}
                style={{
                  '--tw-prose-body': '#cbd5e1',
                  '--tw-prose-headings': '#f1f5f9',
                  '--tw-prose-links': '#60a5fa',
                  '--tw-prose-bold': '#f1f5f9',
                  '--tw-prose-counters': '#94a3b8',
                  '--tw-prose-bullets': '#94a3b8',
                  '--tw-prose-hr': '#475569',
                  '--tw-prose-quotes': '#94a3b8',
                  '--tw-prose-quote-borders': '#475569',
                  '--tw-prose-captions': '#94a3b8',
                  '--tw-prose-code': '#fbbf24',
                  '--tw-prose-pre-code': '#e5e7eb',
                  '--tw-prose-pre-bg': '#1f2937',
                  '--tw-prose-th-borders': '#475569',
                  '--tw-prose-td-borders': '#374151',
                  lineHeight: '1.8'
                }}
              />
            </div>

            {/* Final Verdict Section */}
            <div className="mt-8 bg-gradient-to-r from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-white mb-4 flex items-center">
                <StarIcon className="w-7 h-7 mr-3 text-yellow-400" />
                Expert Verdict
              </h3>
              <div className="flex items-center space-x-6 mb-4">
                <div className="text-4xl font-bold text-yellow-400">
                  {extractedData?.ratings?.overall || '4.2'}/5
                </div>
                <div className="flex space-x-1">
                  {renderStars(extractedData?.ratings?.overall || 4.2)}
                </div>
              </div>
              <p className="text-slate-200 text-lg leading-relaxed mb-6">
                {article.excerpt || 'This casino offers a solid gaming experience with a good selection of games and reliable customer support. While there are areas for improvement, it provides good value for both new and experienced players.'}
              </p>
              <div className="flex flex-wrap gap-4">
                <button className="bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl">
                  Play Now
                </button>
                <button className="bg-white/10 hover:bg-white/20 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 border border-white/20">
                  Read Full Review
                </button>
              </div>
            </div>

            {/* Tags Section */}
            {article.tags && article.tags.length > 0 && (
              <div className="mt-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <TagIcon className="w-6 h-6 text-blue-400" />
                  <span className="text-white font-semibold text-lg">Review Tags</span>
                </div>
                <div className="flex flex-wrap gap-3">
                  {article.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30 hover:border-blue-500/50 transition-colors font-medium"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Screenshots Gallery Section */}
            {extractedData?.screenshots && extractedData.screenshots.length > 0 && (
              <div className="mt-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
                <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <CameraIcon className="w-7 h-7 mr-3 text-green-400" />
                  Casino Screenshots
                </h2>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {extractedData.screenshots.map((screenshot, index) => (
                    <div 
                      key={index}
                      className="aspect-video bg-white/5 rounded-lg overflow-hidden cursor-pointer hover:bg-white/10 transition-all duration-300 border border-white/10 hover:border-white/30"
                      onClick={() => setSelectedImage(screenshot)}
                    >
                      <img
                        src={screenshot.url || screenshot}
                        alt={screenshot.caption || `Casino Screenshot ${index + 1}`}
                        className="w-full h-full object-cover"
                        loading="lazy"
                      />
                    </div>
                  ))}
                </div>
                {selectedImage && (
                  <div 
                    className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
                    onClick={() => setSelectedImage(null)}
                  >
                    <div className="max-w-4xl max-h-full">
                      <img
                        src={selectedImage.url || selectedImage}
                        alt={selectedImage.caption || 'Casino Screenshot'}
                        className="max-w-full max-h-full object-contain rounded-lg"
                      />
                      {selectedImage.caption && (
                        <p className="text-white text-center mt-4 text-lg">
                          {selectedImage.caption}
                        </p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* FAQ Section */}
            {extractedData?.faq && extractedData.faq.length > 0 && (
              <div className="mt-8 bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl p-8">
                <h2 className="text-2xl font-bold text-white mb-6 flex items-center">
                  <QuestionMarkCircleIcon className="w-7 h-7 mr-3 text-purple-400" />
                  Frequently Asked Questions
                </h2>
                <div className="space-y-4">
                  {extractedData.faq.map((item, index) => (
                    <div 
                      key={index}
                      className="bg-white/5 rounded-xl overflow-hidden transition-all duration-300"
                    >
                      <button
                        onClick={() => setExpandedFAQ({...expandedFAQ, [index]: !expandedFAQ[index]})}
                        className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-white/10 transition-colors"
                      >
                        <span className="text-white font-medium text-lg pr-4">{item.question}</span>
                        {expandedFAQ[index] ? (
                          <ChevronUpIcon className="w-5 h-5 text-purple-400 flex-shrink-0" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 text-purple-400 flex-shrink-0" />
                        )}
                      </button>
                      {expandedFAQ[index] && (
                        <div className="px-6 pb-4">
                          <p className="text-slate-300 leading-relaxed">{item.answer}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </article>
        </div>
      </div>
    </div>
  );
};

export default CasinoReviewLayout;