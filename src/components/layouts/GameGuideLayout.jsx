import React from 'react';
import {
  UserIcon,
  CalendarIcon,
  ClockIcon,
  TagIcon,
  ListBulletIcon,
  StarIcon,
  InformationCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate, addHeadingIds } from '../../utils/articleUtils';

/**
 * Game Guide Layout - Safe, simplified version with casino.org styling
 */
const GameGuideLayout = ({ 
  article, 
  tableOfContents, 
  estimatedReadTime, 
  contentRef,
  scrollToHeading,
  showTableOfContents 
}) => {
  // Guard against undefined article
  if (!article) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <p className="text-casino-gold-400">Loading game guide...</p>
        </div>
      </div>
    );
  }

  // Simple star rendering function
  const renderStars = (rating = 4.2) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<StarIconSolid key={i} className="w-5 h-5 text-yellow-400" />);
      } else {
        stars.push(<StarIcon key={i} className="w-5 h-5 text-gray-600" />);
      }
    }
    return stars;
  };

  // Safe table of contents check
  const safeTableOfContents = tableOfContents && Array.isArray(tableOfContents) ? tableOfContents : [];
  const safeTags = article.tags && Array.isArray(article.tags) ? article.tags : [];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header Section */}
      <div className="mb-8">
        {/* Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-casino-gold-400 mb-4">
          <span>Home</span>
          <span>/</span>
          <span>Game Guides</span>
          <span>/</span>
          <span className="text-casino-gold-200 truncate">{article.title || 'Game Guide'}</span>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Title Area */}
          <div className="lg:col-span-2">
            {/* Content Type Badge */}
            <div className="mb-4">
              <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-semibold bg-gradient-to-r ${article.contentTypeInfo?.color || 'from-blue-500 to-cyan-500'} text-white`}>
                <span>{article.contentTypeInfo?.icon || '🎮'}</span>
                <span>{article.contentTypeInfo?.label || 'Game Guide'}</span>
              </div>
            </div>

            {/* Title */}
            <h1 className="text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight">
              {article.title || 'Game Guide'}
            </h1>

            {/* Meta Info */}
            <div className="flex flex-wrap items-center gap-6 text-casino-gold-300 text-sm mb-6">
              <div className="flex items-center space-x-1">
                <UserIcon className="w-4 h-4" />
                <span>By {article.author || 'Writer 777'}</span>
              </div>
              <div className="flex items-center space-x-1">
                <CalendarIcon className="w-4 h-4" />
                <span>{formatDate(article.published_at)}</span>
              </div>
              <div className="flex items-center space-x-1">
                <ClockIcon className="w-4 h-4" />
                <span>{estimatedReadTime || 5} min read</span>
              </div>
            </div>

            {/* Rating */}
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold text-white">4.2</span>
                <div className="flex space-x-1">
                  {renderStars(4.2)}
                </div>
              </div>
              <span className="text-casino-gold-400 text-sm">Overall Rating</span>
            </div>

            {/* Excerpt */}
            {article.excerpt && (
              <div className="bg-casino-dark-700/50 border border-casino-gold-500/30 rounded-lg p-4">
                <p className="text-casino-gold-200 leading-relaxed">
                  {article.excerpt}
                </p>
              </div>
            )}
          </div>

          {/* Game Info Card */}
          <div className="lg:col-span-1">
            <div className="bg-casino-dark-800/80 border border-casino-gold-500/30 rounded-xl p-6 sticky top-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <InformationCircleIcon className="w-5 h-5 mr-2" />
                Game Information
              </h3>

              {/* Key Stats */}
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-casino-gold-400 text-sm">Provider</span>
                  <span className="text-white font-semibold">Pragmatic Play</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-casino-gold-400 text-sm">RTP</span>
                  <span className="text-green-400 font-semibold">96.50%</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-casino-gold-400 text-sm">Volatility</span>
                  <span className="text-red-400 font-semibold">High</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-casino-gold-400 text-sm">Bet Range</span>
                  <span className="text-white font-semibold">$0.20 - $100</span>
                </div>

                <div className="flex justify-between items-center">
                  <span className="text-casino-gold-400 text-sm">Max Win</span>
                  <span className="text-casino-gold-300 font-bold">21,100x</span>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="mt-6 space-y-3">
                <button className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-bold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center space-x-2">
                  <PlayIcon className="w-5 h-5" />
                  <span>Play Demo</span>
                </button>
                <button className="w-full bg-gradient-to-r from-casino-gold-600 to-casino-gold-700 hover:from-casino-gold-700 hover:to-casino-gold-800 text-black font-bold py-3 px-4 rounded-lg transition-all duration-200">
                  Play for Real Money
                </button>
              </div>

              {/* Responsible Gambling Notice */}
              <div className="mt-6 p-3 bg-orange-900/30 border border-orange-500/50 rounded-lg">
                <div className="flex items-start space-x-2">
                  <ExclamationTriangleIcon className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
                  <div className="text-xs text-orange-200">
                    <strong className="block mb-1">Play Responsibly</strong>
                    <p>18+ only. Gambling can be addictive. Please play responsibly.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="grid lg:grid-cols-4 gap-8">
        {/* Article Content */}
        <div className="lg:col-span-3">
          <article className="bg-casino-dark-800/50 border border-casino-gold-500/30 rounded-xl p-8">
            <div 
              ref={contentRef}
              className="prose prose-lg prose-invert max-w-none 
                prose-headings:text-casino-gold-400 
                prose-p:text-casino-gold-200 prose-p:leading-relaxed
                prose-a:text-casino-gold-300 prose-a:hover:text-casino-gold-100
                prose-strong:text-casino-gold-100
                prose-ul:text-casino-gold-200 
                prose-ol:text-casino-gold-200
                prose-li:text-casino-gold-200
                prose-blockquote:text-casino-gold-300 prose-blockquote:border-casino-gold-500
                prose-code:text-casino-gold-100 prose-code:bg-casino-dark-700
                prose-table:text-casino-gold-200"
              dangerouslySetInnerHTML={{ __html: addHeadingIds(article.content || '', safeTableOfContents) }}
            />
          </article>

          {/* Tags */}
          {safeTags.length > 0 && (
            <div className="mt-8 bg-casino-dark-800/50 border border-casino-gold-500/30 rounded-xl p-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <TagIcon className="w-5 h-5 mr-2" />
                Related Topics
              </h3>
              <div className="flex flex-wrap gap-2">
                {safeTags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-casino-gold-500/20 border border-casino-gold-500/40 rounded-full text-sm text-casino-gold-300 hover:bg-casino-gold-500/30 transition-colors cursor-pointer"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Table of Contents Sidebar */}
        {showTableOfContents && safeTableOfContents.length > 0 && (
          <div className="lg:col-span-1">
            <div className="bg-casino-dark-800/80 border border-casino-gold-500/30 rounded-xl p-6 sticky top-6">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <ListBulletIcon className="w-5 h-5 mr-2" />
                Table of Contents
              </h3>
              <nav className="space-y-1">
                {safeTableOfContents.map((item, index) => (
                  <button
                    key={index}
                    onClick={() => scrollToHeading && item.id && scrollToHeading(item.id)}
                    className={`block w-full text-left py-2 px-3 rounded-lg transition-colors text-sm hover:bg-casino-gold-500/20 hover:text-white ${
                      (item.level || 1) === 1 ? 'font-semibold text-casino-gold-200' :
                      (item.level || 1) === 2 ? 'ml-3 text-casino-gold-300' :
                      'ml-6 text-casino-gold-400'
                    }`}
                  >
                    {item.text || 'Section'}
                  </button>
                ))}
              </nav>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GameGuideLayout;