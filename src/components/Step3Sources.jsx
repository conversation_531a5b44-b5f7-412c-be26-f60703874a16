import React, { useState } from 'react';
import { 
  PlusIcon, 
  XMarkIcon, 
  LinkIcon, 
  DocumentTextIcon,
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ChatBubbleLeftIcon,
  FireIcon,
  SparklesIcon,
  ClockIcon,
  ArrowTopRightOnSquareIcon,
  EyeIcon,
  ChevronRightIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';

const Step3Sources = ({ data, updateData }) => {
  const [urlInput, setUrlInput] = useState('');
  const [textInput, setTextInput] = useState('');
  const [isExtracting, setIsExtracting] = useState(false);
  
  // Reddit integration states
  const [showRedditAssistant, setShowRedditAssistant] = useState(true); // 默认展开
  const [subreddit, setSubreddit] = useState('');
  const [keyword, setKeyword] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [isSearching, setIsSearching] = useState(false);
  const [error, setError] = useState(null);
  const [redditAuth, setRedditAuth] = useState({
    connected: false,
    username: null,
    loading: false
  });
  const [selectedPost, setSelectedPost] = useState(null);
  const [postDetails, setPostDetails] = useState(null);
  const [loadingDetails, setLoadingDetails] = useState(false);
  const [expandedComments, setExpandedComments] = useState(new Set());
  const [expandedTexts, setExpandedTexts] = useState(new Set());
  const [subredditSuggestions, setSubredditSuggestions] = useState([]);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // Article-wide sources instead of per-topic
  const sources = data.sources || [];

  // Check Reddit OAuth status on component mount
  React.useEffect(() => {
    checkRedditAuthStatus();
  }, []);

  // Update keyword when selectedKeywords change
  React.useEffect(() => {
    if (data.selectedKeywords && data.selectedKeywords.length > 0) {
      setKeyword(data.selectedKeywords[0]);
    }
  }, [data.selectedKeywords]);

  // Handle URL parameters for OAuth callback
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const redditAuthStatus = urlParams.get('reddit_auth');
    const username = urlParams.get('username');
    const message = urlParams.get('message');

    if (redditAuthStatus === 'success') {
      setRedditAuth({
        connected: true,
        username: decodeURIComponent(username || ''),
        loading: false
      });
      setError(null);
      // Clean up OAuth parameters but preserve other query params
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('reddit_auth');
      newUrl.searchParams.delete('username');
      window.history.replaceState({}, document.title, newUrl.toString());
    } else if (redditAuthStatus === 'error') {
      setError('Reddit authorization failed: ' + decodeURIComponent(message || 'Unknown error'));
      // Clean up OAuth parameters but preserve other query params
      const newUrl = new URL(window.location);
      newUrl.searchParams.delete('reddit_auth');
      newUrl.searchParams.delete('message');
      window.history.replaceState({}, document.title, newUrl.toString());
    }
  }, []);

  // Check Reddit OAuth authorization status
  const checkRedditAuthStatus = async () => {
    setRedditAuth(prev => ({ ...prev, loading: true }));
    try {
      const response = await apiCall('/api/reddit-oauth/status');
      setRedditAuth({
        connected: response.connected,
        username: response.username,
        loading: false
      });
    } catch (err) {
      console.error('Error checking Reddit auth status:', err);
      setRedditAuth({
        connected: false,
        username: null,
        loading: false
      });
    }
  };

  // Initiate Reddit OAuth flow
  const connectReddit = async () => {
    setRedditAuth(prev => ({ ...prev, loading: true }));
    try {
      // Get current page path to return to after OAuth
      const currentPath = window.location.pathname + window.location.search;
      const response = await apiCall(`/api/reddit-oauth/auth?returnUrl=${encodeURIComponent(currentPath)}`);
      
      if (response && response.authUrl) {
        // Redirect to Reddit authorization page
        window.location.href = response.authUrl;
      } else {
        throw new Error('No authorization URL received from server');
      }
    } catch (err) {
      console.error('Error initiating Reddit OAuth:', err);
      let errorMessage = 'Reddit OAuth服务暂时不可用，请稍后再试';
      
      if (err.response?.data?.message) {
        errorMessage = err.response.data.message;
      } else if (err.message) {
        errorMessage = err.message;
      }
      
      setError(`Reddit连接失败: ${errorMessage}`);
      setRedditAuth(prev => ({ ...prev, loading: false }));
    }
  };

  // Disconnect Reddit account
  const disconnectReddit = async () => {
    try {
      await apiCall('/api/reddit-oauth/disconnect', { method: 'DELETE' });
      setRedditAuth({
        connected: false,
        username: null,
        loading: false
      });
      setError(null);
    } catch (err) {
      console.error('Error disconnecting Reddit:', err);
      setError('Failed to disconnect Reddit account. Please try again.');
    }
  };

  // Search subreddits based on input
  const searchSubreddits = async (query) => {
    if (!redditAuth.connected || !query || query.length < 2) {
      setSubredditSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    try {
      const response = await apiCall(`/api/reddit/subreddits/search?q=${encodeURIComponent(query)}`);
      setSubredditSuggestions(response.subreddits || []);
      setShowSuggestions(true);
    } catch (err) {
      console.error('Error searching subreddits:', err);
      setSubredditSuggestions([]);
      setShowSuggestions(false);
    }
  };

  // Handle subreddit input change with debounce
  const handleSubredditChange = (value) => {
    setSubreddit(value);
    
    // Clear suggestions if input is too short
    if (!value || value.length < 2) {
      setShowSuggestions(false);
      setSubredditSuggestions([]);
    }
    
    // Debounce the search
    const timer = setTimeout(() => {
      searchSubreddits(value);
    }, 300);

    return () => clearTimeout(timer);
  };

  // Select a suggested subreddit
  const selectSubreddit = (subredditName) => {
    setSubreddit(subredditName);
    setShowSuggestions(false);
    setSubredditSuggestions([]);
  };

  const searchReddit = async () => {
    if (!redditAuth.connected) {
      setError('Please connect your Reddit account first');
      return;
    }

    if (!subreddit || !keyword) {
      setError('Please enter both subreddit and keyword');
      return;
    }

    setIsSearching(true);
    setError(null);
    
    try {
      const response = await apiCall('/api/reddit/search', {
        method: 'POST',
        body: JSON.stringify({
          subreddit: subreddit.replace(/^r\//, ''), // Remove r/ prefix if present
          keyword,
          limit: 20
        })
      });

      if (response.error) {
        throw new Error(response.error);
      }

      setSearchResults(response.posts || []);
    } catch (err) {
      console.error('Reddit search error:', err);
      if (err.message.includes('401') || err.message.includes('authorization')) {
        setError('Reddit authorization expired. Please reconnect your account.');
        setRedditAuth({ connected: false, username: null, loading: false });
      } else {
        setError('Failed to search Reddit. Please try again.');
      }
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Fetch detailed post with comments
  const fetchPostDetails = async (post) => {
    setLoadingDetails(true);
    setSelectedPost(post);
    try {
      const response = await apiCall(`/api/reddit/post/${post.id}?subreddit=${post.subreddit}`);
      setPostDetails(response.post);
    } catch (err) {
      console.error('Error fetching post details:', err);
      setError('Failed to fetch post details');
    } finally {
      setLoadingDetails(false);
    }
  };

  // Toggle comment expansion
  const toggleComment = (commentId) => {
    const newExpanded = new Set(expandedComments);
    if (newExpanded.has(commentId)) {
      newExpanded.delete(commentId);
    } else {
      newExpanded.add(commentId);
    }
    setExpandedComments(newExpanded);
  };

  // Toggle text expansion
  const toggleText = (textId) => {
    const newExpanded = new Set(expandedTexts);
    if (newExpanded.has(textId)) {
      newExpanded.delete(textId);
    } else {
      newExpanded.add(textId);
    }
    setExpandedTexts(newExpanded);
  };

  // Text display component with expand/collapse
  const ExpandableText = ({ text, maxLength = 400, id }) => {
    const isExpanded = expandedTexts.has(id);
    const shouldTruncate = text.length > maxLength;
    
    return (
      <div>
        <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
          {shouldTruncate && !isExpanded ? text.substring(0, maxLength) + '...' : text}
        </div>
        {shouldTruncate && (
          <button
            onClick={() => toggleText(id)}
            className="mt-2 text-xs text-blue-600 hover:text-blue-800"
          >
            {isExpanded ? 'Show less' : 'Show more'}
          </button>
        )}
      </div>
    );
  };

  const addUrlSource = async () => {
    if (!urlInput.trim()) return;

    setIsExtracting(true);
    try {
      const extractedData = await apiCall(API_CONFIG.ENDPOINTS.SOURCES_EXTRACT, {
        method: 'POST',
        body: JSON.stringify({ url: urlInput.trim() }),
      });

      updateData({
        sources: [...sources, {
          type: 'url',
          url: urlInput.trim(),
          title: extractedData.title,
          content: extractedData.content,
          description: extractedData.description,
          wordCount: extractedData.wordCount
        }]
      });

      setUrlInput('');
    } catch (error) {
      console.error('Error extracting URL content:', error);
      alert('Failed to extract content from URL. Please check the URL and try again.');
    } finally {
      setIsExtracting(false);
    }
  };

  const addTextSource = () => {
    if (!textInput.trim()) return;

    const wordCount = textInput.trim().split(/\s+/).length;

    updateData({
      sources: [...sources, {
        type: 'text',
        title: 'Custom Text Block',
        content: textInput.trim(),
        wordCount: wordCount
      }]
    });

    setTextInput('');
  };

  const removeSource = (index) => {
    updateData({
      sources: sources.filter((_, i) => i !== index)
    });
  };

  const handleUsePost = (post) => {
    console.log('🔥 handleUsePost called with post:', post);
    
    // Extract keywords from title and subreddit
    const titleWords = post.title.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3 && !['this', 'that', 'with', 'from', 'have'].includes(word))
      .slice(0, 5);

    const postSource = {
      type: 'reddit',
      url: post.url,
      title: post.title,
      content: post.selftext || post.body || '',
      description: `Reddit post from r/${post.subreddit} with ${post.upvotes || post.score || 0} upvotes and ${post.num_comments || 0} comments`,
      wordCount: (post.selftext || post.body || '').split(/\s+/).length,
      subreddit: post.subreddit,
      upvotes: post.upvotes || post.score || 0,
      comments: post.num_comments || 0,
      author: post.author,
      created_utc: post.created_utc,
      flair: post.link_flair_text || ''
    };
    
    console.log('🔥 Adding Reddit post as source:', postSource);
    updateData({
      sources: [...sources, postSource]
    });
  };

  // Check if a post is already selected
  const isPostSelected = (post) => {
    return sources.some(source => source.url === post.url);
  };

  const selectedTopics = data.selectedTopics || [];

  if (selectedTopics.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-blue-500/50">
          <span className="text-2xl">📚</span>
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Topics Selected</h3>
        <p className="text-gray-600 mb-4">
          Please go back to Step 2 to select topics before adding sources.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Source Integration</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          Add up to 3 sources to provide context and information for your article. You can add URLs for automatic content extraction, paste text blocks directly, or discover Reddit discussions.
        </p>
      </div>

      {/* Selected Topics Display */}
      {selectedTopics.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3">📝 Selected Topics from Step 2</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {selectedTopics.map((topic, index) => (
              <div key={index} className="bg-white border border-green-300 rounded-md p-3">
                <div className="text-sm font-medium text-gray-900">
                  {index + 1}. {topic.edited}
                </div>
                {topic.original !== topic.edited && (
                  <div className="text-xs text-gray-500 mt-1">
                    Original: {topic.original}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900">📊 Sources Summary</h3>
            <p className="text-sm text-gray-600">
              {sources.length}/3 sources added for the entire article
            </p>
          </div>
          <div className="text-sm text-gray-500">
            Covers all selected topics
          </div>
        </div>
      </div>

      {/* URL Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <LinkIcon className="w-4 h-4 inline mr-1" />
          Add URL Source
        </label>
        <div className="flex gap-2 mb-4">
          <input
            type="url"
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example.com/article"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            disabled={sources.length >= 3}
          />
          <button
            onClick={addUrlSource}
            disabled={!urlInput.trim() || sources.length >= 3 || isExtracting}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            {isExtracting ? 'Extracting...' : <PlusIcon className="w-5 h-5" />}
          </button>
        </div>
      </div>

      {/* Text Input Section */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          <DocumentTextIcon className="w-4 h-4 inline mr-1" />
          Add Text Block
        </label>
        <textarea
          value={textInput}
          onChange={(e) => setTextInput(e.target.value)}
          placeholder="Paste your research notes, product descriptions, or any relevant text here..."
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          disabled={sources.length >= 3}
        />
        <div className="flex justify-between items-center mt-2">
          <span className="text-sm text-gray-500">
            {textInput.trim() ? `${textInput.trim().split(/\s+/).length} words` : ''}
          </span>
          <button
            onClick={addTextSource}
            disabled={!textInput.trim() || sources.length >= 3}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          >
            Add Text Block
          </button>
        </div>
      </div>

      {/* Reddit Assistant Section */}
      <div className="border-t pt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
            <span className="text-2xl">📱</span>
            Reddit Insight Assistant
          </h3>
          <button
            onClick={() => setShowRedditAssistant(!showRedditAssistant)}
            className="px-3 py-1 text-sm bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:bg-gray-400"
            disabled={sources.length >= 3}
          >
            {showRedditAssistant ? 'Hide Reddit' : 'Find Reddit Posts'}
          </button>
        </div>
        
        {showRedditAssistant && (
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            {/* OAuth Information Panel */}
            {!redditAuth.connected && (
              <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                  <LinkIcon className="w-5 h-5 text-blue-600" />
                  Reddit Account Required
                </h4>
                <p className="text-sm text-gray-700 mb-3">
                  Connect your Reddit account to search subreddits and discover trending topics. 
                  This uses Reddit's official OAuth2 authentication for secure access.
                </p>
                <div className="text-xs text-gray-600 bg-white p-3 rounded border">
                  <p className="font-medium mb-1">✅ Secure & Private:</p>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Uses Reddit's official OAuth2 authentication</li>
                    <li>No passwords or API keys stored</li>
                    <li>Read-only access to public posts</li>
                    <li>You can disconnect anytime</li>
                  </ul>
                </div>
              </div>
            )}

            {/* Reddit Connection Status */}
            <div className="flex items-center justify-between mb-4">
              <div>
                <p className="text-sm text-gray-600">
                  Discover trending topics and real user discussions from Reddit communities
                </p>
              </div>
              <div className="flex items-center gap-2">
                {redditAuth.connected ? (
                  <div className="flex items-center gap-2">
                    <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                      Connected as u/{redditAuth.username}
                    </span>
                    <button
                      onClick={disconnectReddit}
                      className="flex items-center gap-1 text-sm text-red-600 hover:text-red-800 px-2 py-1 rounded"
                    >
                      <XMarkIcon className="w-4 h-4" />
                      Disconnect
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={connectReddit}
                    disabled={redditAuth.loading}
                    className="flex items-center gap-1 text-sm bg-orange-600 text-white px-3 py-1 rounded hover:bg-orange-700 disabled:bg-gray-400"
                  >
                    <LinkIcon className="w-4 h-4" />
                    {redditAuth.loading ? 'Connecting...' : 'Connect Reddit'}
                  </button>
                )}
              </div>
            </div>

            {/* Search Form */}
            {redditAuth.connected && (
              <div className="space-y-4 mb-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="relative">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Subreddit
                    </label>
                    <input
                      type="text"
                      value={subreddit}
                      onChange={(e) => handleSubredditChange(e.target.value)}
                      onFocus={() => {
                        if (subredditSuggestions.length > 0) setShowSuggestions(true);
                      }}
                      onBlur={() => {
                        // Delay hiding to allow clicking on suggestions
                        setTimeout(() => setShowSuggestions(false), 200);
                      }}
                      placeholder="e.g., productivity, technology, fitness"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    
                    {/* Subreddit Suggestions Dropdown */}
                    {showSuggestions && subredditSuggestions.length > 0 && (
                      <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        {subredditSuggestions.map((sub) => (
                          <div
                            key={sub.name}
                            onClick={() => selectSubreddit(sub.name)}
                            className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                          >
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <div className="font-medium text-gray-900">r/{sub.name}</div>
                                <div className="text-sm text-gray-600 truncate">{sub.title}</div>
                                {sub.description && (
                                  <div className="text-xs text-gray-500 truncate mt-1">
                                    {sub.description.length > 80 ? sub.description.substring(0, 80) + '...' : sub.description}
                                  </div>
                                )}
                              </div>
                              {sub.subscribers && (
                                <div className="text-xs text-gray-400 ml-2">
                                  {sub.subscribers > 1000000 
                                    ? `${(sub.subscribers / 1000000).toFixed(1)}M` 
                                    : sub.subscribers > 1000 
                                    ? `${(sub.subscribers / 1000).toFixed(0)}k` 
                                    : sub.subscribers} members
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Core Keyword
                    </label>
                    <input
                      type="text"
                      value={keyword}
                      onChange={(e) => setKeyword(e.target.value)}
                      placeholder="e.g., pomodoro"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <button
                    onClick={searchReddit}
                    disabled={isSearching || sources.length >= 3}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 flex items-center gap-2"
                  >
                    <MagnifyingGlassIcon className="w-4 h-4" />
                    {isSearching ? 'Searching...' : 'Search All Posts (Hot, Top & New)'}
                  </button>
                  
                  <div className="text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <FireIcon className="w-4 h-4 text-orange-500" />
                      <SparklesIcon className="w-4 h-4 text-yellow-500" />
                      <ClockIcon className="w-4 h-4 text-blue-500" />
                      All sort types included
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md text-red-700 text-sm">
                {error}
              </div>
            )}

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">
                  Found {searchResults.length} relevant posts
                </h4>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {searchResults.map((post, index) => (
                    <div
                      key={post.id || index}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex justify-between items-start gap-4">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900 line-clamp-2 mb-2">
                            {post.title}
                          </h5>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <ArrowUpIcon className="w-4 h-4 text-orange-500" />
                              {post.upvotes || post.score || 0}
                            </span>
                            <span className="flex items-center gap-1">
                              <ChatBubbleLeftIcon className="w-4 h-4 text-blue-500" />
                              {post.num_comments || 0} comments
                            </span>
                            <a
                              href={post.url}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-blue-600 hover:text-blue-800"
                            >
                              View
                              <ArrowTopRightOnSquareIcon className="w-3 h-3" />
                            </a>
                          </div>
                        </div>
                        <div className="flex flex-col gap-2">
                          <button
                            onClick={() => fetchPostDetails(post)}
                            className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 whitespace-nowrap flex items-center gap-1"
                          >
                            <EyeIcon className="w-3 h-3" />
                            View Details
                          </button>
                          <button
                            onClick={() => handleUsePost(post)}
                            className={`px-3 py-1 text-white text-sm rounded-md whitespace-nowrap flex items-center gap-1 ${
                              isPostSelected(post) 
                                ? 'bg-red-600 hover:bg-red-700' 
                                : sources.length >= 3
                                ? 'bg-gray-400 cursor-not-allowed'
                                : 'bg-green-600 hover:bg-green-700'
                            }`}
                            disabled={!isPostSelected(post) && sources.length >= 3}
                          >
                            {isPostSelected(post) ? (
                              <>
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                </svg>
                                Remove
                              </>
                            ) : sources.length >= 3 ? (
                              'Limit Reached (3/3)'
                            ) : (
                              'Use this Source'
                            )}
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Post Details Modal */}
            {selectedPost && (
              <div className="fixed inset-0 bg-black bg-opacity-50 z-[9999] overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4">
                  <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                  <div className="flex justify-between items-center p-4 border-b">
                    <h3 className="text-lg font-semibold text-gray-900">Reddit Post Details</h3>
                    <button
                      onClick={() => {
                        setSelectedPost(null);
                        setPostDetails(null);
                        setExpandedComments(new Set());
                        setExpandedTexts(new Set());
                      }}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      <XMarkIcon className="w-5 h-5" />
                    </button>
                  </div>
                  
                  <div className="p-4 overflow-y-auto" style={{maxHeight: 'calc(90vh - 120px)'}}>
                    {loadingDetails ? (
                      <div className="text-center py-8">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                        <p className="mt-2 text-sm text-gray-600">Loading post details...</p>
                      </div>
                    ) : postDetails ? (
                      <div className="space-y-6">
                        {/* Post Header */}
                        <div className="border-b pb-4">
                          <h4 className="text-xl font-semibold text-gray-900 mb-2">{postDetails.title}</h4>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                            <span>r/{postDetails.subreddit}</span>
                            <span>by u/{postDetails.author}</span>
                            <span className="flex items-center gap-1">
                              <ArrowUpIcon className="w-4 h-4 text-orange-500" />
                              {postDetails.score} ({Math.round(postDetails.upvote_ratio * 100)}% upvoted)
                            </span>
                            <span>{postDetails.num_comments} comments</span>
                          </div>
                          {postDetails.selftext && (
                            <div className="bg-gray-50 p-4 rounded-lg">
                              <h6 className="font-medium text-gray-700 mb-2">Post Content:</h6>
                              <ExpandableText 
                                text={postDetails.selftext} 
                                maxLength={600} 
                                id={`post-${postDetails.id}`} 
                              />
                            </div>
                          )}
                        </div>

                        {/* Comments */}
                        <div>
                          <h5 className="font-medium text-gray-900 mb-3 flex items-center gap-2">
                            <ChatBubbleLeftIcon className="w-4 h-4" />
                            Top Comments ({postDetails.comments?.length || 0})
                          </h5>
                          <div className="space-y-3">
                            {postDetails.comments?.slice(0, 10).map((comment) => (
                              <div key={comment.id} className="border rounded p-3" style={{ marginLeft: comment.depth * 16 }}>
                                <div className="flex justify-between items-start mb-2">
                                  <span className="text-sm font-medium text-gray-700">u/{comment.author}</span>
                                  <div className="flex items-center gap-2 text-xs text-gray-500">
                                    <span>{comment.score} points</span>
                                    {comment.replies?.length > 0 && (
                                      <button
                                        onClick={() => toggleComment(comment.id)}
                                        className="flex items-center gap-1 hover:text-gray-700"
                                      >
                                        {expandedComments.has(comment.id) ? (
                                          <ChevronDownIcon className="w-3 h-3" />
                                        ) : (
                                          <ChevronRightIcon className="w-3 h-3" />
                                        )}
                                        {comment.replies.length} replies
                                      </button>
                                    )}
                                  </div>
                                </div>
                                <ExpandableText 
                                  text={comment.body} 
                                  maxLength={400} 
                                  id={`comment-${comment.id}`} 
                                />
                                
                                {/* Nested replies */}
                                {expandedComments.has(comment.id) && comment.replies?.map((reply) => (
                                  <div key={reply.id} className="mt-2 ml-4 border-l-2 border-gray-200 pl-3">
                                    <div className="flex justify-between items-start mb-1">
                                      <span className="text-xs font-medium text-gray-600">u/{reply.author}</span>
                                      <span className="text-xs text-gray-400">{reply.score} points</span>
                                    </div>
                                    <div className="text-xs text-gray-700">
                                      <ExpandableText 
                                        text={reply.body} 
                                        maxLength={250} 
                                        id={`reply-${reply.id}`} 
                                      />
                                    </div>
                                  </div>
                                ))}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <p>Failed to load post details</p>
                      </div>
                    )}
                  </div>
                  </div>
                </div>
              </div>
            )}

            {/* Empty State */}
            {!isSearching && searchResults.length === 0 && !error && redditAuth.connected && (
              <div className="text-center py-8 text-gray-500">
                <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p className="text-sm">
                  Search Reddit to discover trending topics and discussions
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Sources Limit Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <p className="text-sm text-blue-700">
          <strong>Sources: {sources.length}/3</strong>
          {sources.length >= 3 && ' (Maximum reached)'}
        </p>
      </div>

      {/* Added Sources Display */}
      {sources.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-800 mb-3">Added Sources</h4>
          <div className="space-y-3">
            {sources.map((source, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1">
                    <h5 className="font-medium text-gray-900">{source.title}</h5>
                    {source.type === 'url' && (
                      <p className="text-sm text-blue-600 break-all">{source.url}</p>
                    )}
                    {source.type === 'reddit' && (
                      <div>
                        <p className="text-sm text-orange-600 break-all">{source.url}</p>
                        <p className="text-xs text-gray-500">Reddit: r/{source.subreddit} • {source.upvotes} upvotes • {source.comments} comments</p>
                      </div>
                    )}
                    {source.description && (
                      <p className="text-sm text-gray-600 mt-1">{source.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      {source.wordCount} words • {source.type === 'url' ? 'URL Source' : source.type === 'reddit' ? 'Reddit Post' : 'Text Block'}
                    </p>
                  </div>
                  <button
                    onClick={() => removeSource(index)}
                    className="ml-2 text-red-600 hover:text-red-800"
                  >
                    <XMarkIcon className="w-5 h-5" />
                  </button>
                </div>

                {/* Content Preview */}
                <div className="bg-gray-50 rounded p-3 mt-2">
                  <p className="text-sm text-gray-700 line-clamp-3">
                    {source.content.substring(0, 200)}
                    {source.content.length > 200 && '...'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

    </div>
  );
};

export default Step3Sources;
