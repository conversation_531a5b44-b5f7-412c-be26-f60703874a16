import React, { useState, useRef, useEffect } from 'react';

/**
 * VideoBackground Component - Optimized video background with fallbacks
 * Features:
 * - Lazy loading and preload control
 * - Mobile optimization (shows static image instead)
 * - User preference respect (reduced motion)
 * - Error handling with static image fallback
 * - Performance optimizations
 */
const VideoBackground = ({ 
  videoSrc = "/777-hd.mp4", // Default to optimized HD version
  posterImage = "/777-poster.jpg", 
  fallbackImage,
  className = "",
  overlay = true,
  overlayOpacity = 0.7
}) => {
  const videoRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const [loadTime, setLoadTime] = useState(null);
  const [optimalVideoSrc, setOptimalVideoSrc] = useState(videoSrc);
  const loadStartTime = useRef(null);

  // Select optimal video based on device and network conditions
  const selectOptimalVideo = () => {
    const connection = navigator.connection;
    const screenWidth = window.innerWidth;
    const deviceMemory = navigator.deviceMemory;
    
    // For tablets and large screens with good conditions
    if (screenWidth >= 1200 && (!connection || connection.effectiveType === '4g') && (!deviceMemory || deviceMemory >= 4)) {
      return "/777-hd.mp4"; // 2MB - High quality
    }
    
    // For medium screens or moderate conditions  
    if (screenWidth >= 768 && (!connection || ['4g', '3g'].includes(connection.effectiveType))) {
      return "/777-medium.mp4"; // 1.3MB - Medium quality
    }
    
    // For mobile or poor conditions (though video likely won't be shown)
    return "/777-mobile.mp4"; // 630KB - Mobile quality
  };

  // Check if we should show video based on user preferences and device
  useEffect(() => {
    const checkShouldShowVideo = () => {
      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
      
      // Check if mobile device (basic check for performance)
      const isMobile = window.innerWidth < 768 || 
                      /Android|webOS|iPhone|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
                      // Removed iPad and touch check to allow tablets to show video
      
      // Check for slow connection (only exclude very slow connections)
      const connection = navigator.connection;
      const isSlowConnection = connection && (
        connection.effectiveType === 'slow-2g' || 
        connection.effectiveType === '2g'
        // Allow 3G and 4G since we have optimized small video files
      );
      
      // Check for data saver mode
      const isDataSaver = connection && connection.saveData;
      
      // Check for low-end device (basic heuristic)
      const isLowEndDevice = navigator.deviceMemory && navigator.deviceMemory < 4;
      
      // Show video only if conditions are favorable
      const shouldShow = !prefersReducedMotion && 
                        !isMobile && 
                        !isSlowConnection && 
                        !isDataSaver && 
                        !isLowEndDevice;
      
      setShowVideo(shouldShow);
      
      console.log('Video conditions check:', {
        screenWidth: window.innerWidth,
        userAgent: navigator.userAgent,
        prefersReducedMotion,
        isMobile,
        isSlowConnection,
        isDataSaver,
        isLowEndDevice,
        connectionType: connection?.effectiveType,
        deviceMemory: navigator.deviceMemory,
        shouldShow
      });

      if (shouldShow) {
        // Select optimal video quality
        const optimalSrc = selectOptimalVideo();
        setOptimalVideoSrc(optimalSrc);
        console.log('✅ Video enabled! Selected quality:', optimalSrc);
      } else {
        setIsLoading(false);
        console.warn('❌ Video disabled due to conditions above');
      }
    };

    checkShouldShowVideo();
    
    // Listen for resize to re-evaluate mobile status
    window.addEventListener('resize', checkShouldShowVideo);
    return () => window.removeEventListener('resize', checkShouldShowVideo);
  }, []);

  // Handle video load events and lazy loading
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !showVideo) return;

    const handleLoadStart = () => {
      setIsLoading(true);
      loadStartTime.current = performance.now();
    };
    
    const handleCanPlay = () => {
      setIsLoading(false);
      if (loadStartTime.current) {
        const loadDuration = performance.now() - loadStartTime.current;
        setLoadTime(loadDuration);
        console.log(`✅ Video loaded in ${Math.round(loadDuration)}ms`);
        
        // Try to play the video
        const video = videoRef.current;
        if (video) {
          video.play().then(() => {
            console.log('✅ Video started playing successfully');
          }).catch(error => {
            console.warn('❌ Video autoplay failed:', error.message);
            console.log('This is usually due to browser autoplay policies. User interaction required.');
          });
        }
        
        // Log performance metrics for optimization
        if (loadDuration > 3000) {
          console.warn('Video took longer than 3s to load. Consider optimizing the file.');
        }
      }
    };
    
    const handleError = () => {
      setHasError(true);
      setIsLoading(false);
      console.warn('Video failed to load, falling back to static image');
    };

    // Add event listeners
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('canplay', handleCanPlay);
    video.addEventListener('error', handleError);

    // Implement lazy loading with Intersection Observer
    const lazyLoadVideo = () => {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting && entry.target === video) {
            // Load video when it comes into view
            if (!video.src) {
              video.src = optimalVideoSrc;
              video.load(); // Trigger loading
            }
            observer.unobserve(video);
          }
        });
      }, { threshold: 0.1 });

      observer.observe(video);
      
      return () => observer.disconnect();
    };

    const cleanup = lazyLoadVideo();

    return () => {
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('canplay', handleCanPlay);
      video.removeEventListener('error', handleError);
      cleanup();
    };
  }, [showVideo, optimalVideoSrc]);

  // Show static image if video should not be displayed or has error
  if (!showVideo || hasError) {
    return (
      <div 
        className={`absolute inset-0 ${className}`}
        style={{
          backgroundImage: `url(${fallbackImage || posterImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        {overlay && (
          <div 
            className="absolute inset-0 bg-gradient-to-br from-black/90 via-black/80 to-purple-900/70"
            style={{ opacity: overlayOpacity }}
          />
        )}
      </div>
    );
  }

  return (
    <div className={`absolute inset-0 ${className}`}>
      {/* Loading placeholder */}
      {isLoading && (
        <div 
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${posterImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-2 border-white border-t-transparent"></div>
          </div>
        </div>
      )}

      {/* Video element */}
      <video
        ref={videoRef}
        autoPlay
        muted
        loop
        playsInline
        preload="none" // Changed to 'none' for better performance with large files
        poster={posterImage}
        className={`w-full h-full object-cover ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-500`}
        style={{ 
          filter: isLoading ? 'blur(5px)' : 'none',
          transition: 'filter 0.5s ease-in-out'
        }}
      >
        {/* Source will be added dynamically for better control */}
        <source data-src={optimalVideoSrc} type="video/mp4" />
        {/* Fallback for browsers that don't support video */}
        <img 
          src={fallbackImage || posterImage} 
          alt="Background" 
          className="w-full h-full object-cover"
        />
      </video>

      {/* Overlay */}
      {overlay && (
        <div 
          className="absolute inset-0 bg-gradient-to-br from-black/80 via-black/60 to-purple-900/50"
          style={{ opacity: overlayOpacity }}
        />
      )}
    </div>
  );
};

export default VideoBackground;