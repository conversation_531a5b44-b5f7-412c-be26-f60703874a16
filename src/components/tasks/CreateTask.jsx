import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAuth } from '../../contexts/AuthContext';
import { SparklesIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';

const CreateTask = () => {
  const { t } = useTranslation();
  const { authenticatedFetch } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert(t('createTask.nameRequired'));
      return;
    }

    setLoading(true);

    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.CREATE, {
        method: 'POST',
        body: JSON.stringify({
          name: formData.name.trim(),
          initialData: {
            description: formData.description.trim()
          }
        })
      });

      if (response.ok) {
        try {
          const data = await response.json();
          if (data && data.task && data.task.id) {
            // Redirect to the task editor
            navigate(`/tasks/${data.task.id}/edit`);
          } else {
            console.error('Invalid response data:', data);
            alert('Invalid response from server. Please try again.');
          }
        } catch (jsonError) {
          console.error('Failed to parse success response as JSON:', jsonError);
          alert('Invalid response format from server. Please try again.');
        }
      } else {
        try {
          const errorData = await response.json();
          alert(errorData.message || 'Failed to create task');
        } catch (jsonError) {
          console.error('Failed to parse error response as JSON:', jsonError);
          alert(`Failed to create task (${response.status}). Server returned invalid response.`);
        }
      }
    } catch (error) {
      console.error('Error creating task:', error);
      alert('Error creating task. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const generateTaskName = () => {
    const topics = [
      'AI and Machine Learning',
      'Digital Marketing Strategies',
      'Sustainable Technology',
      'Remote Work Best Practices',
      'Cybersecurity Trends',
      'E-commerce Growth',
      'Content Marketing',
      'Social Media Strategy',
      'Data Analytics',
      'Cloud Computing'
    ];
    
    const formats = [
      'Complete Guide',
      'Best Practices',
      'Trends and Insights',
      'Step-by-Step Tutorial',
      'Expert Analysis',
      'Industry Report',
      'Comprehensive Review'
    ];

    const randomTopic = topics[Math.floor(Math.random() * topics.length)];
    const randomFormat = formats[Math.floor(Math.random() * formats.length)];
    
    setFormData({
      ...formData,
      name: `${randomFormat}: ${randomTopic}`
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/tasks')}
              className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white/50 rounded-lg transition-colors"
            >
              <ArrowLeftIcon className="w-5 h-5" />
            </button>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-lg flex items-center justify-center">
                <SparklesIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">{t('createTask.title')}</h1>
                <p className="text-gray-600">{t('dashboard.quickActions.startNewDesc')}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">{t('createTask.title')}</h2>
            <p className="text-gray-600 mt-1">{t('createTask.taskNamePlaceholder')}</p>
          </div>

          <form onSubmit={handleSubmit} className="p-8 space-y-6">
            {/* Task Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                {t('createTask.taskName')} *
              </label>
              <div className="flex space-x-3">
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder={t('createTask.taskNamePlaceholder')}
                  className="flex-1 px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  maxLength={255}
                />
                <button
                  type="button"
                  onClick={generateTaskName}
                  className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors whitespace-nowrap"
                >
                  Generate Idea
                </button>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Choose a clear, descriptive name that reflects your article's topic and purpose
              </p>
            </div>

            {/* Description (Optional) */}
            <div>
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                {t('createTask.description')} ({t('common.optional')})
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={4}
                placeholder={t('createTask.descriptionPlaceholder')}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none"
                maxLength={1000}
              />
              <p className="text-sm text-gray-500 mt-1">
                Optional: Add notes about your target audience, goals, or specific requirements
              </p>
            </div>

            {/* Info Box */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <SparklesIcon className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h3 className="text-sm font-medium text-blue-900">What happens next?</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    After creating your task, you'll be guided through our 7-step article generation process:
                  </p>
                  <ul className="text-sm text-blue-700 mt-2 space-y-1">
                    <li>• Keyword research and discovery</li>
                    <li>• Topic selection and refinement</li>
                    <li>• Source gathering and integration</li>
                    <li>• Product information (if applicable)</li>
                    <li>• Authority profile and E-E-A-T setup</li>
                    <li>• Style and format configuration</li>
                    <li>• AI-powered article generation</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-between pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={() => navigate('/tasks')}
                className="px-6 py-3 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={loading || !formData.name.trim()}
                className="px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating Task...
                  </div>
                ) : (
                  'Create Task & Start'
                )}
              </button>
            </div>
          </form>
        </div>

        {/* Quick Start Templates */}
        <div className="mt-8 bg-white rounded-xl shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Start Templates</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { name: 'How-to Guide', desc: 'Step-by-step instructional content' },
              { name: 'Product Review', desc: 'Detailed product analysis and comparison' },
              { name: 'Industry Analysis', desc: 'Market trends and insights' },
              { name: 'Best Practices', desc: 'Expert recommendations and tips' },
              { name: 'Case Study', desc: 'Real-world examples and results' },
              { name: 'Beginner\'s Guide', desc: 'Introduction to complex topics' }
            ].map((template, index) => (
              <button
                key={index}
                onClick={() => setFormData({ ...formData, name: template.name })}
                className="text-left p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors"
              >
                <h4 className="font-medium text-gray-900">{template.name}</h4>
                <p className="text-sm text-gray-600 mt-1">{template.desc}</p>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateTask;
