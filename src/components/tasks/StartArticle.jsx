import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { SparklesIcon, ArrowPathIcon } from '@heroicons/react/24/outline';
import { API_CONFIG } from '../../config/api';

const StartArticle = () => {
  const { authenticatedFetch, user, token } = useAuth();
  const navigate = useNavigate();
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Check if user is authenticated before creating task
    if (!user || !token) {
      setError('Please log in to create an article task');
      return;
    }

    // Auto-create task when component mounts
    createTaskAndRedirect();
  }, [user, token]);

  const createTaskAndRedirect = async () => {
    setCreating(true);
    setError(null);

    try {
      console.log('Creating task...');
      console.log('User:', user);
      console.log('Token exists:', !!token);

      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.AUTO_CREATE, {
        method: 'POST'
      });

      if (response.ok) {
        try {
          const data = await response.json();
          if (data && data.task && data.task.id) {
            // Redirect to the task editor starting at step 1 (keyword research)
            navigate(`/tasks/${data.task.id}/edit`);
          } else {
            console.error('Invalid response data:', data);
            setError('Invalid response from server. Please try again.');
          }
        } catch (jsonError) {
          console.error('Failed to parse success response as JSON:', jsonError);
          setError('Invalid response format from server. Please try again.');
        }
      } else {
        try {
          const errorData = await response.json();
          console.error('API Error:', response.status, errorData);
          setError(errorData.message || `Failed to create article task (${response.status})`);
        } catch (jsonError) {
          console.error('Failed to parse error response as JSON:', jsonError);
          setError(`Failed to create article task (${response.status}). Server returned invalid response.`);
        }
      }
    } catch (error) {
      console.error('Error creating task:', error);
      setError(`Error creating article task: ${error.message}`);
    } finally {
      setCreating(false);
    }
  };

  const handleRetry = () => {
    createTaskAndRedirect();
  };

  const handleGoBack = () => {
    navigate('/dashboard');
  };

  const handleLogin = () => {
    navigate('/login');
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Failed to Create Article Task
            </h2>
            <p className="text-gray-600 mb-6">
              {error}
            </p>
            <div className="flex space-x-3">
              {error.includes('log in') ? (
                <>
                  <button
                    onClick={handleLogin}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Login
                  </button>
                  <button
                    onClick={handleGoBack}
                    className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Go Back
                  </button>
                </>
              ) : (
                <>
                  <button
                    onClick={handleRetry}
                    className="flex-1 inline-flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <ArrowPathIcon className="w-4 h-4 mr-2" />
                    Try Again
                  </button>
                  <button
                    onClick={handleGoBack}
                    className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                  >
                    Go Back
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4">
        <div className="text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <SparklesIcon className="w-8 h-8 text-blue-600 animate-pulse" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Creating Your Article Task
          </h2>
          <p className="text-gray-600 mb-6">
            Setting up your workspace for article creation...
          </p>
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StartArticle;
