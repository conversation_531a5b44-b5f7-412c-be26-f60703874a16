import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { ChevronRightIcon, ChevronLeftIcon, SparklesIcon, ArrowLeftIcon, CheckIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import StepIndicator from '../StepIndicator';
import Step0KeywordResearch from '../Step0KeywordResearch';
import Step1TopicSelection from '../Step1TopicSelection';
import Step3Sources from '../Step3Sources';
import Step3Product from '../Step3Product';
import Step4EEAT from '../Step4EEAT';
import Step4Parameters from '../Step4Parameters';
import ComplianceSettings from '../ComplianceSettings';
import Step5Generation from '../Step5Generation';
import { API_CONFIG } from '../../config/api';

const STEPS = [
  { id: 1, title: 'Keyword Research', subtitle: 'Discover content opportunities', component: Step0KeywordResearch, icon: '🔍' },
  { id: 2, title: 'Choose Topics', subtitle: 'Define your article focus', component: Step1TopicSelection, icon: '📝' },
  { id: 3, title: 'Gather Sources', subtitle: 'Add supporting references', component: Step3Sources, icon: '📚' },
  { id: 4, title: 'Product Integration', subtitle: 'Optional product mentions', component: Step3Product, icon: '🏷️' },
  { id: 5, title: 'Authority Profile', subtitle: 'Establish your expertise', component: Step4EEAT, icon: '👤' },
  { id: 6, title: 'Style & Format', subtitle: 'Customize writing style and content type', component: Step4Parameters, icon: '⚙️' },
  { id: 7, title: 'Compliance Settings', subtitle: 'Configure regulatory requirements', component: ComplianceSettings, icon: '⚖️' },
  { id: 8, title: 'Generate Article', subtitle: 'Create your content', component: Step5Generation, icon: '✨' },
];

const TaskEditor = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const { authenticatedFetch } = useAuth();

  const [task, setTask] = useState({
    keywords: [],
    selectedTopics: [],
    topicSuggestions: [],
    keywordResearchData: null,
    keywordResearchSelections: [],
    sources: [],
    topicSources: {},
    productInfo: {},
    eeatProfile: {},
    outputParameters: {},
    generatedArticle: '',
    secondaryKeywords: [] // Ensure secondaryKeywords is initialized
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);
  const [lastSaved, setLastSaved] = useState(null);
  const [isFinishing, setIsFinishing] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  
  // 自动保存防抖
  const autoSaveTimeoutRef = useRef(null);

  useEffect(() => {
    if (taskId) {
      fetchTask();
    }
    
    // 清理定时器
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [taskId]);

  const fetchTask = async () => {
    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.GET(taskId));

      if (response.ok) {
        const data = await response.json();
        
        const newCurrentStep = data.task.generated_article
          ? STEPS.length - 1
          : (data.task.current_step || 0);
        setCurrentStep(newCurrentStep);

        console.log('[TaskEditor] fetchTask - Server data selectedKeywords:', data.task.selectedKeywords);
        console.log('[TaskEditor] fetchTask - Server data secondaryKeywords:', data.task.secondaryKeywords);

        // 直接使用服务器数据，不进行"智能合并"，避免状态冲突
        setTask({
          ...data.task,
          // 确保关键词数组字段正确初始化
          selectedKeywords: data.task.selectedKeywords || [],
          secondaryKeywords: data.task.secondaryKeywords || [],
          keywords: data.task.keywords || [],
          // 确保语言和国家字段正确映射（数据库字段名到前端字段名）
          selectedLanguage: data.task.targetLanguage || data.task.target_language || 'en',
          selectedCountry: data.task.targetCountry || data.task.target_country || 'us',
          targetLanguage: data.task.targetLanguage || data.task.target_language || 'en',
          targetCountry: data.task.targetCountry || data.task.target_country || 'us'
        });
      } else {
        console.error('Failed to fetch task');
        navigate('/tasks');
      }
    } catch (error) {
      console.error('Error fetching task:', error);
      navigate('/tasks');
    } finally {
      setLoading(false);
    }
  };

  const saveTask = async (taskToSave) => {
    if (!taskToSave) return;

    console.log('[TaskEditor] saveTask called with full task data:', taskToSave);
    console.log('[TaskEditor] saveTask - taskToSave.selectedKeywords:', taskToSave.selectedKeywords);
    console.log('[TaskEditor] saveTask - taskToSave.secondaryKeywords:', taskToSave.secondaryKeywords);

    setSaving(true);
    try {
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.UPDATE(taskId), {
        method: 'PUT',
        body: JSON.stringify(taskToSave)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('[TaskEditor] Save response data.task:', data.task);
        console.log('[TaskEditor] Save response selectedKeywords:', data.task.selectedKeywords);
        console.log('[TaskEditor] Save response secondaryKeywords:', data.task.secondaryKeywords);
        
        // 确保服务器返回的数据包含所有必要字段，特别是关键词选择
        const updatedTask = {
          ...data.task,
          // 确保关键词数组字段存在
          selectedKeywords: data.task.selectedKeywords || [],
          secondaryKeywords: data.task.secondaryKeywords || [],
          keywords: data.task.keywords || []
        };
        
        setTask(updatedTask);
        setLastSaved(new Date());
        console.log('Save successful');
      } else {
        const errorData = await response.text();
        console.error('Save failed with status:', response.status, 'Error:', errorData);
      }
    } catch (error) {
      console.error('Error saving task:', error);
    } finally {
      setSaving(false);
    }
  };

  const getTaskStatus = () => {
    if (!task) return 'Draft - Step 1';

    // Only show "Completed" if the task status is actually "Completed" (after user clicks Finish)
    if (task.status === 'Completed') {
      return 'Completed';
    } else if (task.generated_article && currentStep === STEPS.length - 1) {
      return 'Ready to Finish';
    } else if (currentStep === STEPS.length - 1) {
      return 'Ready to Generate';
    } else {
      return `Draft - Step ${currentStep + 1}`;
    }
  };

  const getTaskProgress = () => {
    if (!task) return 0;

    console.log('getTaskProgress - task.status:', task.status, 'task.generated_article:', !!task.generated_article);

    // Only show 100% if task is actually completed (after user clicks Finish)
    if (task.status === 'Completed') {
      console.log('Task is completed, returning 100%');
      return 100;
    }

    // If article is generated but not finished, show 95%
    if (task.generated_article) {
      console.log('Article generated but not finished, returning 95%');
      return 95;
    }

    // Otherwise, calculate based on current step
    const progress = ((currentStep + 1) / STEPS.length) * 90; // Max 90% until finished
    console.log('Step-based progress:', progress);
    return progress;
  };

  const updateArticleData = (updates) => {
    if (!task) return;

    console.log('[TaskEditor] updateArticleData called with updates:', updates);

    const updatedTask = {
      ...task,
      ...updates
    };

    setTask(updatedTask);
    console.log('[TaskEditor] After setTask - updatedTask.selectedKeywords:', updatedTask.selectedKeywords);
    console.log('[TaskEditor] After setTask - updatedTask.secondaryKeywords:', updatedTask.secondaryKeywords);

    // If article is generated, automatically go to final step
    if (updates.generated_article && currentStep !== STEPS.length - 1) {
      console.log('Article generated, moving to final step');
      setCurrentStep(STEPS.length - 1);
      updates.currentStep = STEPS.length - 1;
    }

    // 立即保存关键词选择和语言/国家设置，其他更新使用防抖保存
    const isKeywordUpdate = updates.selectedKeywords !== undefined || updates.secondaryKeywords !== undefined;
    const isLanguageUpdate = updates.selectedLanguage !== undefined || updates.selectedCountry !== undefined || 
                           updates.targetLanguage !== undefined || updates.targetCountry !== undefined;
    
    if (isKeywordUpdate || isLanguageUpdate) {
      console.log('[TaskEditor] Keyword or language update detected, saving immediately');
      saveTask(updatedTask);
    } else {
      // 防抖自动保存 - 避免频繁保存导致状态冲突
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
      
      console.log('Scheduling debounced auto-save in 1.5 seconds with updates:', updates);
      autoSaveTimeoutRef.current = setTimeout(() => {
        saveTask(updatedTask);
      }, 1500);
    }
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      const newStep = currentStep + 1;
      // Save current task state before moving to next step
      saveTask({ ...task, current_step: newStep, status: getTaskStatus() });
      setCurrentStep(newStep);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      const newStep = currentStep - 1;
      // Save current task state before moving to previous step
      saveTask({ ...task, current_step: newStep, status: getTaskStatus() });
      setCurrentStep(newStep);
    }
  };

  const finishTask = async () => {
    if (isFinishing) return; // Prevent multiple clicks

    setIsFinishing(true);
    try {
      console.log('Finishing task:', taskId);
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.FINISH(taskId), {
        method: 'POST'
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Task finish failed:', response.status, errorData);
        throw new Error(`Failed to finish task: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      console.log('Task finished successfully:', result);

      // Update with the complete finished task data
      setTask(result.task);

      // Force a re-render to update progress and status
      setTimeout(() => {
        setTask(prevTask => ({ ...prevTask, ...result.task }));
      }, 100);

      // Show success message
      alert('🎉 Task completed successfully! The article has been finalized and automatically published to your homepage.');

    } catch (error) {
      console.error('Error finishing task:', error);
      alert('Failed to finish task. Please try again.');
    } finally {
      setIsFinishing(false);
    }
  };

  const publishArticle = async () => {
    if (isPublishing) return; // Prevent multiple clicks

    setIsPublishing(true);
    try {
      console.log('Publishing article for task:', taskId);
      const response = await authenticatedFetch(API_CONFIG.ENDPOINTS.TASKS.PUBLISH(taskId), {
        method: 'POST'
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('Article publish failed:', response.status, errorData);
        throw new Error(`Failed to publish article: ${response.status} ${errorData}`);
      }

      const result = await response.json();
      console.log('Article published successfully:', result);

      // Show success message with article URL
      if (result.article && result.article.slug) {
        alert(`🎉 Article published successfully!\n\nYou can view it at: /articles/${result.article.slug}\n\nIt will now appear on your public homepage.`);
      } else {
        alert('🎉 Article published successfully! It will now appear on your public homepage.');
      }

    } catch (error) {
      console.error('Error publishing article:', error);
      if (error.message.includes('already published')) {
        alert('ℹ️ This article is already published on your homepage.');
      } else {
        alert('❌ Failed to publish article. Please try again.');
      }
    } finally {
      setIsPublishing(false);
    }
  };

  const goToStep = (stepIndex) => {
    // Save current task state before jumping to a different step
    saveTask({ ...task, current_step: stepIndex, status: getTaskStatus() });
    setCurrentStep(stepIndex);
  };

  const canProceedToNext = () => {
    if (!task) return false;

    switch (currentStep) {
      case 0:
        return true; // Keyword research is optional
      case 1:
        return task.selectedTopics && task.selectedTopics.length > 0;
      case 2:
        return true; // Sources are optional
      case 3:
        return true; // Product info is optional
      case 4:
        return true; // E-E-A-T is optional
      case 5:
        return true; // Parameters have defaults (content type will be set in this step)
      case 6:
        return true; // Compliance settings are optional but recommended
      case 7:
        return true; // Final step
      default:
        return false;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading task...</p>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Task Not Found</h2>
          <p className="text-gray-600 mb-4">The requested task could not be found.</p>
          <button
            onClick={() => navigate('/tasks')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Back to Tasks
          </button>
        </div>
      </div>
    );
  }

  const CurrentStepComponent = STEPS[currentStep].component;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg shadow-blue-500/5">
        <div className="w-full px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/tasks')}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-white/50 rounded-lg transition-colors"
              >
                <ArrowLeftIcon className="w-5 h-5" />
              </button>
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg">
                  <SparklesIcon className="w-7 h-7 text-white" />
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                  {task.name}
                </h1>
                <p className="text-sm text-gray-600 font-medium">
                  {getTaskStatus()} • Step {currentStep + 1} of {STEPS.length}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {saving && (
                <div className="flex items-center space-x-2 text-sm text-gray-600">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-gray-600"></div>
                  <span>Saving...</span>
                </div>
              )}
              {lastSaved && !saving && (
                <div className="text-sm text-gray-500">
                  Saved {lastSaved.toLocaleTimeString()}
                </div>
              )}
              <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/60 rounded-full">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">AI Ready</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="w-full py-8">
        <div className="flex min-h-screen">
          {/* Left Sidebar - Step Indicator */}
          <div className="w-64 flex-shrink-0 px-4">
            <div className="sticky top-32">
              <StepIndicator
                steps={STEPS}
                currentStep={currentStep}
                onStepClick={goToStep}
                isTaskCompleted={task?.status === 'Completed'}
              />
            </div>
          </div>

          {/* Main Content Area - Full Width */}
          <div className="flex-1 px-6">
            <div className="bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl shadow-blue-500/10 border border-white/30 overflow-hidden">
              {/* Step Header */}
              <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-8 py-6 border-b border-white/30">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">
                      {STEPS[currentStep].title}
                    </h2>
                    <p className="text-gray-600 font-medium">
                      {STEPS[currentStep].subtitle}
                    </p>
                  </div>
                  <div className="text-4xl opacity-80">
                    {STEPS[currentStep].icon}
                  </div>
                </div>
              </div>

              {/* Step Content */}
              <div className="relative p-8">
                <CurrentStepComponent
                  data={task}
                  updateData={updateArticleData}
                  onNext={nextStep}
                  onPrev={prevStep}
                  isFirstStep={currentStep === 0}
                  isLastStep={currentStep === STEPS.length - 1}
                  isGeneratingTopics={isGeneratingTopics}
                  setIsGeneratingTopics={setIsGeneratingTopics}
                  taskId={taskId}
                />
              </div>

              {/* Navigation */}
              <div className="bg-gradient-to-r from-gray-50 via-blue-50 to-indigo-50 px-8 py-6 border-t border-white/30">
                <div className="flex justify-between items-center">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="flex items-center space-x-2 px-6 py-3 bg-white/80 text-gray-700 rounded-xl font-medium hover:bg-white hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 border border-gray-200/50"
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600 font-medium">
                      Step {currentStep + 1} of {STEPS.length}
                    </span>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getTaskProgress()}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Show Finish button on last step with generated article, otherwise show Next */}
                  {currentStep === STEPS.length - 1 && task?.generated_article ? (
                    <div className="flex items-center space-x-3">
                      {/* Publish Article Button - Show for completed tasks */}
                      {task?.status === 'Completed' && (
                        <button
                          onClick={publishArticle}
                          disabled={isPublishing}
                          className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg ${
                            isPublishing
                              ? 'bg-blue-400 cursor-not-allowed'
                              : 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg'
                          }`}
                        >
                          {isPublishing ? (
                            <>
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                              <span>Publishing...</span>
                            </>
                          ) : (
                            <>
                              <GlobeAltIcon className="w-4 h-4" />
                              <span>Publish Article</span>
                            </>
                          )}
                        </button>
                      )}

                      {/* Finish/Completed Button */}
                      <button
                        onClick={finishTask}
                        disabled={isFinishing || task?.status === 'Completed'}
                        className={`flex items-center space-x-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg ${
                          isFinishing || task?.status === 'Completed'
                            ? 'bg-gray-400 cursor-not-allowed'
                            : 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700 hover:shadow-lg'
                        }`}
                      >
                        {isFinishing ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            <span>Finishing...</span>
                          </>
                        ) : task?.status === 'Completed' ? (
                          <>
                            <CheckIcon className="w-4 h-4" />
                            <span>Completed</span>
                          </>
                        ) : (
                          <>
                            <span>Finish</span>
                            <CheckIcon className="w-4 h-4" />
                          </>
                        )}
                      </button>
                    </div>
                  ) : (
                    <button
                      onClick={nextStep}
                      disabled={currentStep === STEPS.length - 1 || !canProceedToNext()}
                      className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                    >
                      <span>Next</span>
                      <ChevronRightIcon className="w-4 h-4" />
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default TaskEditor;
