import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import language resources
import en from './locales/en.json';
import zh from './locales/zh.json';
import pt from './locales/pt.json';
import es from './locales/es.json';
import de from './locales/de.json';
import fr from './locales/fr.json';
import it from './locales/it.json';
import ja from './locales/ja.json';

const resources = {
  en: {
    translation: en
  },
  zh: {
    translation: zh
  },
  pt: {
    translation: pt
  },
  es: {
    translation: es
  },
  de: {
    translation: de
  },
  fr: {
    translation: fr
  },
  it: {
    translation: it
  },
  ja: {
    translation: ja
  }
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    lng: 'pt', // default language (Portuguese first priority)
    fallbackLng: 'pt',
    
    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage']
    },

    interpolation: {
      escapeValue: false // React already escapes values
    },

    react: {
      useSuspense: false
    }
  });

export default i18n;