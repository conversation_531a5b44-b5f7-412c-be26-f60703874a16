const { Pool } = require('pg');

class Database {
  constructor() {
    this.pool = null;
    this.isClosing = false;
    this.isServerMode = false; // Flag to indicate if running as server
    
    // Require PostgreSQL connection
    if (!process.env.DATABASE_URL || !process.env.DATABASE_URL.startsWith('postgresql://')) {
      throw new Error('PostgreSQL DATABASE_URL is required. SQLite is no longer supported.');
    }
  }

  async connect() {
    try {
      // Only create new pool if we don't have one or it's ended
      if (!this.pool || this.pool.ended) {
        this.pool = new Pool({
          connectionString: process.env.DATABASE_URL,
          ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
          // Connection pool configuration
          max: 20, // Maximum number of clients in the pool
          idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
          connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection cannot be established
          // Query timeout
          query_timeout: 10000, // 10 seconds query timeout
          statement_timeout: 10000 // 10 seconds statement timeout
        });
      }

      // Test the connection
      const client = await this.pool.connect();
      client.release();
      console.log('Connected to PostgreSQL database');

      await this.initializeTables();
      return Promise.resolve();
    } catch (error) {
      console.error('Database connection error:', error);
      throw error;
    }
  }

  async initializeTables() {
    const tables = this.getPostgresTables();

    for (const table of tables) {
      await this.run(table);
    }

    // Create indexes for better performance
    const indexes = this.getPostgresIndexes();

    for (const index of indexes) {
      await this.run(index);
    }

    console.log('Database tables initialized successfully');
  }

  getPostgresTables() {
    return [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id SERIAL PRIMARY KEY,
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        full_name VARCHAR(255),
        plan_type VARCHAR(50) DEFAULT 'V1_DEFAULT_ACCESS',
        role VARCHAR(50) DEFAULT 'user',
        email_verified BOOLEAN DEFAULT FALSE,
        verification_token VARCHAR(255),
        reset_token VARCHAR(255),
        reset_token_expires TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // System settings table
      `CREATE TABLE IF NOT EXISTS system_settings (
        id SERIAL PRIMARY KEY,
        setting_key VARCHAR(255) UNIQUE NOT NULL,
        setting_value TEXT,
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Tasks table
      `CREATE TABLE IF NOT EXISTS tasks (
        id VARCHAR(255) PRIMARY KEY,
        user_id INTEGER NOT NULL,
        name VARCHAR(255) NOT NULL,
        status VARCHAR(100) DEFAULT 'Draft - Step 1',
        current_step INTEGER DEFAULT 0,
        content_type VARCHAR(100),
        jurisdiction VARCHAR(50) DEFAULT 'international',
        target_language VARCHAR(10) DEFAULT 'en',
        target_country VARCHAR(10) DEFAULT 'US',
        content_type_config TEXT, -- JSON object
        compliance_template TEXT, -- JSON object
        custom_disclaimers TEXT, -- JSON array
        review_settings TEXT, -- JSON object
        additional_requirements TEXT,
        keywords TEXT, -- JSON array
        selected_keywords TEXT, -- JSON array - 主要关键词选择
        secondary_keywords TEXT, -- JSON array - 次要关键词选择
        selected_topics TEXT, -- JSON array
        topic_suggestions TEXT, -- JSON array
        keyword_research_data TEXT, -- JSON object
        keyword_research_selections TEXT, -- JSON array
        sources TEXT, -- JSON array (legacy)
        topic_sources TEXT, -- JSON object
        product_info TEXT, -- JSON object
        eeat_profile TEXT, -- JSON object
        output_parameters TEXT, -- JSON object
        generated_article TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // User presets table
      `CREATE TABLE IF NOT EXISTS user_presets (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        preset_type VARCHAR(50) NOT NULL, -- 'author' or 'product'
        preset_name VARCHAR(255) NOT NULL,
        preset_data TEXT NOT NULL, -- JSON object
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Blog posts table
      `CREATE TABLE IF NOT EXISTS blog_posts (
        id SERIAL PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        content TEXT NOT NULL,
        excerpt TEXT,
        featured_image VARCHAR(500),
        author VARCHAR(255) DEFAULT 'J-Writer Team',
        status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'published', 'unpublished'
        content_type VARCHAR(100), -- Content type for categorization
        categories TEXT, -- JSON array
        tags TEXT, -- JSON array
        language VARCHAR(10) DEFAULT 'en', -- Language code (en, pt, es, etc.)
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        published_at TIMESTAMP
      )`,

      // User sessions table (for JWT blacklisting)
      `CREATE TABLE IF NOT EXISTS user_sessions (
        id SERIAL PRIMARY KEY,
        user_id INTEGER NOT NULL,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
      )`,

      // Prompt templates table
      `CREATE TABLE IF NOT EXISTS prompt_templates (
        id SERIAL PRIMARY KEY,
        content_type VARCHAR(100) NOT NULL,
        template_name VARCHAR(255) NOT NULL,
        template_version VARCHAR(50) DEFAULT '1.0',
        prompt_content TEXT NOT NULL,
        persona_description TEXT,
        specialized_instructions TEXT,
        compliance_rules TEXT, -- JSON array
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`,

      // Affiliate links table
      `CREATE TABLE IF NOT EXISTS affiliate_links (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        affiliate_url VARCHAR(1000) NOT NULL,
        display_text VARCHAR(255) NOT NULL,
        content_type VARCHAR(100), -- Which content type this link is for
        category VARCHAR(100), -- e.g., 'casino', 'sportsbook', 'software'
        commission_rate DECIMAL(5,2), -- Percentage
        tracking_code VARCHAR(100),
        click_count INTEGER DEFAULT 0,
        conversion_count INTEGER DEFAULT 0,
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )`
    ];
  }

  getPostgresIndexes() {
    return [
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_user_id ON tasks(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_language ON tasks(target_language)',
      'CREATE INDEX IF NOT EXISTS idx_tasks_country ON tasks(target_country)',
      'CREATE INDEX IF NOT EXISTS idx_presets_user_id ON user_presets(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_presets_type ON user_presets(preset_type)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_status ON blog_posts(status)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_slug ON blog_posts(slug)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_language ON blog_posts(language)',
      'CREATE INDEX IF NOT EXISTS idx_blog_posts_status_language ON blog_posts(status, language)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_content_type ON prompt_templates(content_type)',
      'CREATE INDEX IF NOT EXISTS idx_prompt_templates_active ON prompt_templates(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_content_type ON affiliate_links(content_type)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_category ON affiliate_links(category)',
      'CREATE INDEX IF NOT EXISTS idx_affiliate_links_active ON affiliate_links(is_active)'
    ];
  }

  // Convert SQLite-style ? placeholders to PostgreSQL-style $1, $2, etc.
  convertSqlForPostgres(sql) {
    let paramCount = 1;
    return sql.replace(/\?/g, () => `$${paramCount++}`);
  }

  async run(sql, params = []) {
    try {
      // Check if pool is ended and try to reconnect
      if (this.pool && this.pool.ended) {
        console.log('🔄 PostgreSQL pool was ended, reconnecting...');
        await this.connect();
      }

      let pgSql = this.convertSqlForPostgres(sql);

      // For INSERT statements, add RETURNING id to get the inserted ID
      if (pgSql.trim().toUpperCase().startsWith('INSERT') && !pgSql.toUpperCase().includes('RETURNING')) {
        pgSql += ' RETURNING id';
      }

      const result = await this.pool.query(pgSql, params);
      return {
        id: result.rows[0]?.id || null,
        changes: result.rowCount || 0,
        rows: result.rows
      };
    } catch (error) {
      console.error('Database run error:', error.message);
      throw error;
    }
  }

  async get(sql, params = []) {
    try {
      // Check if pool is ended and try to reconnect
      if (this.pool && this.pool.ended) {
        console.log('🔄 PostgreSQL pool was ended, reconnecting...');
        await this.connect();
      }

      const pgSql = this.convertSqlForPostgres(sql);
      const result = await this.pool.query(pgSql, params);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Database get error:', error.message);
      throw error;
    }
  }

  async all(sql, params = []) {
    try {
      // Check if pool is ended and try to reconnect
      if (this.pool && this.pool.ended) {
        console.log('🔄 PostgreSQL pool was ended, reconnecting...');
        await this.connect();
      }

      const pgSql = this.convertSqlForPostgres(sql);
      const result = await this.pool.query(pgSql, params);
      return result.rows;
    } catch (error) {
      console.error('Database all error:', error.message);
      throw error;
    }
  }

  setServerMode(isServer = true) {
    this.isServerMode = isServer;
  }

  async close() {
    // Prevent closing if in server mode unless explicitly forced
    if (this.isServerMode && !this.isClosing) {
      console.log('⚠️ Database close() called in server mode - ignoring to prevent pool closure');
      return;
    }

    try {
      if (this.pool && !this.pool.ended) {
        await this.pool.end();
        console.log('PostgreSQL connection pool closed');
      }
    } catch (error) {
      console.error('Error closing PostgreSQL pool:', error);
      throw error;
    }
  }

  async forceClose() {
    this.isClosing = true;
    await this.close();
  }
}

module.exports = new Database();