const express = require('express');
const { v4: uuidv4 } = require('uuid');
const { authenticateToken, checkResourceOwnership } = require('../middleware/auth');
const database = require('../config/database');
const geminiService = require('../services/geminiService');
const { marked } = require('marked');

const router = express.Router();

// Configure marked for HTML conversion
marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false
});

// Helper function to convert markdown to HTML
function markdownToHtml(markdown) {
  if (!markdown) return '';

  try {
    return marked(markdown);
  } catch (error) {
    console.error('Error converting markdown to HTML:', error);
    return markdown; // Return original markdown if conversion fails
  }
}

// GET /api/tasks/stats - Get task statistics for the user
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await database.all(
      `SELECT
        status,
        COUNT(*) as count
      FROM tasks
      WHERE user_id = ?
      GROUP BY status`,
      [req.user.id]
    );

    const totalTasks = await database.get(
      'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?',
      [req.user.id]
    );

    res.json({
      totalTasks: totalTasks.total,
      statusBreakdown: stats
    });

  } catch (error) {
    console.error('Get task stats error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task statistics',
      message: 'An error occurred while retrieving task statistics'
    });
  }
});

// All routes require authentication
router.use(authenticateToken);

// GET /api/tasks - Get all tasks for the authenticated user
router.get('/', async (req, res) => {
  try {
    const { status, contentType, sortBy = 'updated_at', sortOrder = 'DESC', page = 1, limit = 20 } = req.query;

    let query = 'SELECT * FROM tasks WHERE user_id = ?';
    const params = [req.user.id];

    // Add status filter if provided
    if (status) {
      query += ' AND status = ?';
      params.push(status);
    }

    // Add content type filter if provided
    if (contentType) {
      query += ' AND content_type = ?';
      params.push(contentType);
    }

    // Add sorting
    const validSortFields = ['name', 'status', 'created_at', 'updated_at'];
    const validSortOrders = ['ASC', 'DESC'];

    if (validSortFields.includes(sortBy) && validSortOrders.includes(sortOrder.toUpperCase())) {
      query += ` ORDER BY ${sortBy} ${sortOrder.toUpperCase()}`;
    } else {
      query += ' ORDER BY updated_at DESC';
    }

    // Add pagination
    const offset = (parseInt(page) - 1) * parseInt(limit);
    query += ' LIMIT ? OFFSET ?';
    params.push(parseInt(limit), offset);

    const tasks = await database.all(query, params);

    // Get total count for pagination
    let countQuery = 'SELECT COUNT(*) as total FROM tasks WHERE user_id = ?';
    const countParams = [req.user.id];

    if (status) {
      countQuery += ' AND status = ?';
      countParams.push(status);
    }

    if (contentType) {
      countQuery += ' AND content_type = ?';
      countParams.push(contentType);
    }

    const { total } = await database.get(countQuery, countParams);

    // Parse JSON fields for response
    const formattedTasks = tasks.map(task => ({
      ...task,
      contentTypeConfig: task.content_type_config ? JSON.parse(task.content_type_config) : null,
      complianceTemplate: task.compliance_template ? JSON.parse(task.compliance_template) : null,
      customDisclaimers: task.custom_disclaimers ? JSON.parse(task.custom_disclaimers) : [],
      reviewSettings: task.review_settings ? JSON.parse(task.review_settings) : {},
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedKeywords: task.selected_keywords ? JSON.parse(task.selected_keywords) : [],
      secondaryKeywords: task.secondary_keywords ? JSON.parse(task.secondary_keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      topicSources: task.topic_sources ? JSON.parse(task.topic_sources) : {},
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {},
      // 确保语言和国家字段返回
      targetLanguage: task.target_language || 'en',
      targetCountry: task.target_country || 'us',
      selectedLanguage: task.target_language || 'en', // 别名，供前端使用
      selectedCountry: task.target_country || 'us'     // 别名，供前端使用
    }));

    res.json({
      tasks: formattedTasks,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get tasks error:', error);
    res.status(500).json({
      error: 'Failed to retrieve tasks',
      message: 'An error occurred while retrieving your tasks'
    });
  }
});

// GET /api/tasks/:id - Get a specific task
router.get('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse JSON fields
    const formattedTask = {
      ...task,
      contentTypeConfig: task.content_type_config ? JSON.parse(task.content_type_config) : null,
      complianceTemplate: task.compliance_template ? JSON.parse(task.compliance_template) : null,
      customDisclaimers: task.custom_disclaimers ? JSON.parse(task.custom_disclaimers) : [],
      reviewSettings: task.review_settings ? JSON.parse(task.review_settings) : {},
      keywords: task.keywords ? JSON.parse(task.keywords) : [],
      selectedKeywords: task.selected_keywords ? JSON.parse(task.selected_keywords) : [],
      secondaryKeywords: task.secondary_keywords ? JSON.parse(task.secondary_keywords) : [],
      selectedTopics: task.selected_topics ? JSON.parse(task.selected_topics) : [],
      topicSuggestions: task.topic_suggestions ? JSON.parse(task.topic_suggestions) : [],
      keywordResearchData: task.keyword_research_data ? JSON.parse(task.keyword_research_data) : null,
      keywordResearchSelections: task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [],
      sources: task.sources ? JSON.parse(task.sources) : [],
      topicSources: task.topic_sources ? JSON.parse(task.topic_sources) : {},
      productInfo: task.product_info ? JSON.parse(task.product_info) : {},
      eeatProfile: task.eeat_profile ? JSON.parse(task.eeat_profile) : {},
      outputParameters: task.output_parameters ? JSON.parse(task.output_parameters) : {},
      // 确保语言和国家字段返回
      targetLanguage: task.target_language || 'en',
      targetCountry: task.target_country || 'us',
      selectedLanguage: task.target_language || 'en', // 别名，供前端使用
      selectedCountry: task.target_country || 'us'     // 别名，供前端使用
    };

    res.json({ task: formattedTask });

  } catch (error) {
    console.error('Get task error:', error);
    res.status(500).json({
      error: 'Failed to retrieve task',
      message: 'An error occurred while retrieving the task'
    });
  }
});

// POST /api/tasks/auto-create - Auto-create a new task with timestamp
router.post('/auto-create', async (req, res) => {
  try {
    const { targetLanguage, targetCountry } = req.body;
    const taskId = uuidv4();
    const timestamp = new Date().toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
    const taskName = `Article Task - ${timestamp}`;

    // Prepare initial data with defaults
    const defaultData = {
      contentType: '',
      jurisdiction: 'international',
      targetLanguage: targetLanguage || 'en',
      targetCountry: targetCountry || 'US',
      contentTypeConfig: null,
      complianceTemplate: null,
      customDisclaimers: [],
      reviewSettings: {
        mandatory_review: { title: 'Mandatory Human Review', enabled: true, locked: true },
        legal_review: { title: 'Legal Compliance Review', enabled: false, locked: false },
        fact_checking: { title: 'Fact Checking', enabled: true, locked: false },
        responsible_gambling: { title: 'Responsible Gambling Review', enabled: true, locked: true }
      },
      additionalRequirements: '',
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      sources: [],
      topicSources: {},
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        contentType: 'generic',
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        content_type, jurisdiction, target_language, target_country,
        content_type_config, compliance_template, custom_disclaimers, review_settings,
        additional_requirements, keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        sources, topic_sources, product_info,
        eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        defaultData.contentType,
        defaultData.jurisdiction,
        defaultData.targetLanguage,
        defaultData.targetCountry,
        JSON.stringify(defaultData.contentTypeConfig),
        JSON.stringify(defaultData.complianceTemplate),
        JSON.stringify(defaultData.customDisclaimers),
        JSON.stringify(defaultData.reviewSettings),
        defaultData.additionalRequirements,
        JSON.stringify(defaultData.keywords),
        JSON.stringify(defaultData.selectedTopics),
        JSON.stringify(defaultData.topicSuggestions),
        JSON.stringify(defaultData.keywordResearchData),
        JSON.stringify(defaultData.keywordResearchSelections),
        JSON.stringify(defaultData.sources),
        JSON.stringify(defaultData.topicSources),
        JSON.stringify(defaultData.productInfo),
        JSON.stringify(defaultData.eeatProfile),
        JSON.stringify(defaultData.outputParameters)
      ]
    );

    // Get the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      contentTypeConfig: createdTask.content_type_config ? JSON.parse(createdTask.content_type_config) : null,
      complianceTemplate: createdTask.compliance_template ? JSON.parse(createdTask.compliance_template) : null,
      customDisclaimers: JSON.parse(createdTask.custom_disclaimers),
      reviewSettings: JSON.parse(createdTask.review_settings),
      keywords: JSON.parse(createdTask.keywords),
      selectedKeywords: createdTask.selected_keywords ? JSON.parse(createdTask.selected_keywords) : [],
      secondaryKeywords: createdTask.secondary_keywords ? JSON.parse(createdTask.secondary_keywords) : [],
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      sources: JSON.parse(createdTask.sources),
      topicSources: JSON.parse(createdTask.topic_sources),
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Auto-create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// POST /api/tasks - Create a new task (legacy endpoint)
router.post('/', async (req, res) => {
  try {
    const { name, initialData = {}, targetLanguage, targetCountry } = req.body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return res.status(400).json({
        error: 'Invalid task name',
        message: 'Task name is required and must be a non-empty string'
      });
    }

    const taskId = uuidv4();
    const taskName = name.trim().slice(0, 255); // Limit name length

    // Prepare initial data with defaults
    const defaultData = {
      contentType: '',
      jurisdiction: 'international',
      targetLanguage: targetLanguage || initialData.targetLanguage || 'en',
      targetCountry: targetCountry || initialData.targetCountry || 'US',
      contentTypeConfig: null,
      complianceTemplate: null,
      customDisclaimers: [],
      reviewSettings: {
        mandatory_review: { title: 'Mandatory Human Review', enabled: true, locked: true },
        legal_review: { title: 'Legal Compliance Review', enabled: false, locked: false },
        fact_checking: { title: 'Fact Checking', enabled: true, locked: false },
        responsible_gambling: { title: 'Responsible Gambling Review', enabled: true, locked: true }
      },
      additionalRequirements: '',
      keywords: [],
      selectedTopics: [],
      topicSuggestions: [],
      keywordResearchData: null,
      keywordResearchSelections: [],
      sources: [],
      topicSources: {},
      productInfo: {
        name: '',
        link: '',
        description: '',
        features: []
      },
      eeatProfile: {
        authorName: '',
        authorBio: '',
        targetAudience: '',
        articleGoal: ''
      },
      outputParameters: {
        contentType: 'generic',
        tonality: 'informative',
        length: 'medium_article',
        format: 'markdown'
      }
    };

    const mergedData = { ...defaultData, ...initialData };

    // Create task
    await database.run(
      `INSERT INTO tasks (
        id, user_id, name, status, current_step,
        target_language, target_country,
        keywords, selected_topics, topic_suggestions,
        keyword_research_data, keyword_research_selections,
        sources, topic_sources, product_info,
        eeat_profile, output_parameters
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        taskId,
        req.user.id,
        taskName,
        'Draft - Step 1',
        0,
        mergedData.targetLanguage,
        mergedData.targetCountry,
        JSON.stringify(mergedData.keywords),
        JSON.stringify(mergedData.selectedTopics),
        JSON.stringify(mergedData.topicSuggestions),
        JSON.stringify(mergedData.keywordResearchData),
        JSON.stringify(mergedData.keywordResearchSelections),
        JSON.stringify(mergedData.sources),
        JSON.stringify(mergedData.topicSources),
        JSON.stringify(mergedData.productInfo),
        JSON.stringify(mergedData.eeatProfile),
        JSON.stringify(mergedData.outputParameters)
      ]
    );

    // Return the created task
    const createdTask = await database.get(
      'SELECT * FROM tasks WHERE id = ?',
      [taskId]
    );

    const formattedTask = {
      ...createdTask,
      contentTypeConfig: createdTask.content_type_config ? JSON.parse(createdTask.content_type_config) : null,
      complianceTemplate: createdTask.compliance_template ? JSON.parse(createdTask.compliance_template) : null,
      customDisclaimers: JSON.parse(createdTask.custom_disclaimers),
      reviewSettings: JSON.parse(createdTask.review_settings),
      keywords: JSON.parse(createdTask.keywords),
      selectedKeywords: createdTask.selected_keywords ? JSON.parse(createdTask.selected_keywords) : [],
      secondaryKeywords: createdTask.secondary_keywords ? JSON.parse(createdTask.secondary_keywords) : [],
      selectedTopics: JSON.parse(createdTask.selected_topics),
      topicSuggestions: JSON.parse(createdTask.topic_suggestions),
      keywordResearchData: JSON.parse(createdTask.keyword_research_data),
      keywordResearchSelections: JSON.parse(createdTask.keyword_research_selections),
      sources: JSON.parse(createdTask.sources),
      topicSources: JSON.parse(createdTask.topic_sources),
      productInfo: JSON.parse(createdTask.product_info),
      eeatProfile: JSON.parse(createdTask.eeat_profile),
      outputParameters: JSON.parse(createdTask.output_parameters)
    };

    res.status(201).json({
      message: 'Task created successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Create task error:', error);
    res.status(500).json({
      error: 'Failed to create task',
      message: 'An error occurred while creating the task'
    });
  }
});

// PUT /api/tasks/:id - Update a task
router.put('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const {
      name,
      status,
      currentStep,
      targetLanguage,
      targetCountry,
      keywords,
      selectedTopics,
      topicSuggestions,
      keywordResearchData,
      keywordResearchSelections,
      sources,
      topicSources,
      productInfo,
      eeatProfile,
      outputParameters,
      generatedArticle,
      generated_article,  // Accept both camelCase and snake_case
      selectedKeywords,   // 主要关键词选择
      secondaryKeywords   // 次要关键词选择
    } = req.body;

    // Use either field name for generated article
    const articleContent = generatedArticle || generated_article;

    // Debug article content updates
    if (articleContent !== undefined) {
      console.log('=== ARTICLE UPDATE DEBUG ===');
      console.log('Received article content length:', articleContent ? articleContent.length : 'null/undefined');
      console.log('Article content preview:', articleContent ? articleContent.substring(0, 100) + '...' : 'null');
      console.log('============================');
    }

    // Build update query dynamically based on provided fields
    const updates = [];
    const params = [];

    if (name !== undefined) {
      updates.push('name = ?');
      params.push(name.toString().trim().slice(0, 255));
    }

    if (status !== undefined) {
      updates.push('status = ?');
      params.push(status.toString());
    }

    if (currentStep !== undefined) {
      updates.push('current_step = ?');
      params.push(parseInt(currentStep));
    }

    if (targetLanguage !== undefined) {
      updates.push('target_language = ?');
      params.push(targetLanguage.toString());
    }

    if (targetCountry !== undefined) {
      updates.push('target_country = ?');
      params.push(targetCountry.toString());
    }

    if (keywords !== undefined) {
      updates.push('keywords = ?');
      params.push(JSON.stringify(keywords));
    }

    if (selectedTopics !== undefined) {
      updates.push('selected_topics = ?');
      params.push(JSON.stringify(selectedTopics));
    }

    if (topicSuggestions !== undefined) {
      updates.push('topic_suggestions = ?');
      params.push(JSON.stringify(topicSuggestions));
    }

    if (keywordResearchData !== undefined) {
      updates.push('keyword_research_data = ?');
      params.push(JSON.stringify(keywordResearchData));
    }

    if (keywordResearchSelections !== undefined) {
      updates.push('keyword_research_selections = ?');
      params.push(JSON.stringify(keywordResearchSelections));
    }

    if (sources !== undefined) {
      updates.push('sources = ?');
      params.push(JSON.stringify(sources));
    }

    if (topicSources !== undefined) {
      updates.push('topic_sources = ?');
      params.push(JSON.stringify(topicSources));
    }

    if (productInfo !== undefined) {
      updates.push('product_info = ?');
      params.push(JSON.stringify(productInfo));
    }

    if (eeatProfile !== undefined) {
      updates.push('eeat_profile = ?');
      params.push(JSON.stringify(eeatProfile));
    }

    if (outputParameters !== undefined) {
      updates.push('output_parameters = ?');
      params.push(JSON.stringify(outputParameters));
    }

    if (selectedKeywords !== undefined) {
      updates.push('selected_keywords = ?');
      params.push(JSON.stringify(selectedKeywords));
    }

    if (secondaryKeywords !== undefined) {
      updates.push('secondary_keywords = ?');
      params.push(JSON.stringify(secondaryKeywords));
    }

    if (articleContent !== undefined) {
      updates.push('generated_article = ?');
      params.push(articleContent);

      // Auto-update task name to article title when article is completed
      if (articleContent && typeof articleContent === 'string') {
        try {
          // First try to extract SEO title from structured format
          const seoTitleMatch = articleContent.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                               articleContent.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

          let articleTitle = null;

          if (seoTitleMatch && seoTitleMatch[1]) {
            articleTitle = seoTitleMatch[1].trim();
            // Remove any markdown formatting and brackets
            articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
            // Remove any trailing periods or punctuation that might be part of the format
            articleTitle = articleTitle.replace(/[.]*$/, '').trim();
            // Stop at any meta description content that might have been captured
            articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          } else {
            // Fallback: try to extract from markdown header or first line
            const titleMatch = articleContent.match(/^#\s*(.+)$/m) ||
                             articleContent.match(/^(.+)$/m);

            if (titleMatch && titleMatch[1]) {
              articleTitle = titleMatch[1].trim();
              // Remove any markdown formatting
              articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            }
          }

          if (articleTitle) {
            // Limit title length
            articleTitle = articleTitle.slice(0, 200);
            updates.push('name = ?');
            params.push(articleTitle); // Add to end of params array
          }
        } catch (error) {
          console.warn('Failed to extract article title:', error);
          // Continue without updating the name
        }
      }
    }

    if (updates.length === 0) {
      return res.status(400).json({
        error: 'No updates provided',
        message: 'At least one field must be provided for update'
      });
    }

    // Always update the updated_at timestamp
    updates.push('updated_at = CURRENT_TIMESTAMP');

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    const query = `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`;

    // Debug query and params
    console.log('=== UPDATE QUERY DEBUG ===');
    console.log('Query:', query);
    console.log('Params:', params);
    console.log('Updates:', updates);
    console.log('========================');

    const result = await database.run(query, params);

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found or updated'
      });
    }

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedKeywords: updatedTask.selected_keywords ? JSON.parse(updatedTask.selected_keywords) : [],
      secondaryKeywords: updatedTask.secondary_keywords ? JSON.parse(updatedTask.secondary_keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      topicSources: updatedTask.topic_sources ? JSON.parse(updatedTask.topic_sources) : {},
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {},
      // 确保语言和国家字段返回
      targetLanguage: updatedTask.target_language || 'en',
      targetCountry: updatedTask.target_country || 'us',
      selectedLanguage: updatedTask.target_language || 'en', // 别名，供前端使用
      selectedCountry: updatedTask.target_country || 'us'     // 别名，供前端使用
    };

    res.json({
      message: 'Task updated successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Update task error:', error);
    res.status(500).json({
      error: 'Failed to update task',
      message: 'An error occurred while updating the task'
    });
  }
});

// POST /api/tasks/:id/generate-article - Generate article for a task
router.post('/:id/generate-article', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract data
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    // Parse task data
    const selectedTopics = task.selected_topics ? JSON.parse(task.selected_topics) : [];
    const topicSources = task.topic_sources ? JSON.parse(task.topic_sources) : {};
    const sources = task.sources ? JSON.parse(task.sources) : [];
    const productInfo = task.product_info ? JSON.parse(task.product_info) : {};
    const eeatProfile = task.eeat_profile ? JSON.parse(task.eeat_profile) : {};
    const outputParameters = task.output_parameters ? JSON.parse(task.output_parameters) : {};
    const keywordResearchData = task.keyword_research_data ? JSON.parse(task.keyword_research_data) : {};
    const keywordResearchSelections = task.keyword_research_selections ? JSON.parse(task.keyword_research_selections) : [];
    // Extract keyword selections from Step0
    const selectedKeywords = task.selected_keywords ? JSON.parse(task.selected_keywords) : [];
    const secondaryKeywords = task.secondary_keywords ? JSON.parse(task.secondary_keywords) : [];
    // Extract language and country information directly from task fields
    const selectedLanguage = task.target_language || 'en';
    const selectedCountry = task.target_country || 'US';

    // Validate that we have topics to work with
    if (!selectedTopics || selectedTopics.length === 0) {
      return res.status(400).json({
        error: 'No topics selected',
        message: 'Please select at least one topic before generating an article'
      });
    }

    // Extract topics
    const allTopics = selectedTopics.map(t => t.edited || t.original || t);

    // Use article-level sources (new architecture)
    // All sources are now stored at article level instead of per-topic
    const allSources = sources || [];
    
    // Legacy support: Also check topicSources in case of old data
    if (topicSources && Object.keys(topicSources).length > 0) {
      Object.values(topicSources).forEach(topicSourceList => {
        allSources.push(...topicSourceList);
      });
    }

    // Debug source information
    console.log('=== SOURCES DEBUG ===');
    console.log('Article-level sources count:', sources.length);
    console.log('Sources by type:', sources.reduce((acc, source) => {
      acc[source.type] = (acc[source.type] || 0) + 1;
      return acc;
    }, {}));
    if (sources.find(s => s.type === 'reddit')) {
      console.log('Reddit sources found:', sources.filter(s => s.type === 'reddit').map(s => ({
        title: s.title,
        subreddit: s.subreddit,
        upvotes: s.upvotes,
        comments: s.comments
      })));
    }
    console.log('====================');

    // Extract keywords - Use new selectedKeywords and secondaryKeywords fields
    console.log('=== KEYWORD DATA DEBUG ===');
    console.log('selectedKeywords:', selectedKeywords);
    console.log('secondaryKeywords:', secondaryKeywords);
    console.log('keywordResearchData:', keywordResearchData);
    console.log('===========================');

    // Primary keywords from Step0 selections
    const primaryKeywords = [...selectedKeywords];
    
    // Add the original research keyword if available
    if (keywordResearchData && keywordResearchData.keyword) {
      if (!primaryKeywords.includes(keywordResearchData.keyword)) {
        primaryKeywords.push(keywordResearchData.keyword);
        console.log('Added research keyword:', keywordResearchData.keyword);
      }
    }

    // Secondary keywords from Step0 selections
    const finalSecondaryKeywords = [...secondaryKeywords];
    
    console.log('Final primaryKeywords:', primaryKeywords);
    console.log('Final secondaryKeywords:', finalSecondaryKeywords);

    // Prepare article data
    const articleData = {
      topics: allTopics,
      sources: allSources,
      productInfo: productInfo,
      tonality: outputParameters.tonality || 'informative',
      length: outputParameters.length || 'medium_article',
      format: outputParameters.format || 'markdown',
      authorName: eeatProfile.authorName || '',
      authorBio: eeatProfile.authorBio || '',
      targetAudience: eeatProfile.targetAudience || '',
      articleGoal: eeatProfile.articleGoal || '',
      primaryKeywords: [...new Set(primaryKeywords)],
      secondaryKeywords: [...new Set(finalSecondaryKeywords)],
      languageCode: outputParameters.languageCode || selectedLanguage, // Use selected language from Step 0
      contentType: outputParameters.contentType || 'general',
      jurisdiction: outputParameters.jurisdiction || 'international',
      targetLanguage: selectedLanguage, // Add target language for prompt context
      targetCountry: selectedCountry    // Add target country for market context
    };

    console.log('Generating article for task:', req.params.id);
    console.log('Primary keywords extracted:', primaryKeywords);
    console.log('Secondary keywords extracted:', secondaryKeywords);
    console.log('Keyword research selections:', keywordResearchSelections);
    console.log('Full article data:', articleData);

    // Generate article
    const generatedArticle = await geminiService.generateArticle(articleData);

    // Update task with generated article only (don't auto-complete yet)
    await database.run(
      'UPDATE tasks SET generated_article = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ? AND user_id = ?',
      [generatedArticle, req.params.id, req.user.id]
    );

    res.json({
      article: generatedArticle,
      message: 'Article generated successfully'
    });

  } catch (error) {
    console.error('Generate article error:', error);
    res.status(500).json({
      error: 'Failed to generate article',
      message: error.message || 'An error occurred while generating the article'
    });
  }
});

// Helper function to extract game info from content
function extractGameInfoFromContent(content) {
  if (!content || typeof content !== 'string') {
    return null;
  }

  try {
    // Method 1: Try to extract from HTML comments (preferred format)
    const startMarker = '<!-- GAME_INFO_START -->';
    const endMarker = '<!-- GAME_INFO_END -->';
    
    const startIndex = content.indexOf(startMarker);
    const endIndex = content.indexOf(endMarker);
    
    if (startIndex !== -1 && endIndex !== -1 && startIndex < endIndex) {
      const jsonStart = startIndex + startMarker.length;
      const jsonContent = content.substring(jsonStart, endIndex).trim();
      
      const gameInfo = JSON.parse(jsonContent);
      console.log('✓ Extracted game info from HTML comments:', gameInfo);
      return gameInfo;
    }

    // Method 2: Try to extract standalone JSON block (fallback)
    console.log('HTML comments not found, trying to extract standalone JSON...');
    
    // Look for JSON block that starts with { and contains typical game info fields
    const jsonRegex = /\{[\s\S]*?"provider"[\s\S]*?"rtp"[\s\S]*?\}/;
    const jsonMatch = content.match(jsonRegex);
    
    if (jsonMatch) {
      const jsonContent = jsonMatch[0];
      const gameInfo = JSON.parse(jsonContent);
      console.log('✓ Extracted game info from standalone JSON:', gameInfo);
      return gameInfo;
    }

    console.log('No game info JSON found in content');
    return null;
    
  } catch (error) {
    console.error('Error extracting game info:', error);
    return null;
  }
}

// Helper function to remove game info from content
function removeGameInfoFromContent(content) {
  if (!content || typeof content !== 'string') {
    return content;
  }
  
  // Method 1: Remove HTML comment wrapped JSON (preferred format)
  const startMarker = '<!-- GAME_INFO_START -->';
  const endMarker = '<!-- GAME_INFO_END -->';
  
  const startIndex = content.indexOf(startMarker);
  const endIndex = content.indexOf(endMarker);
  
  if (startIndex !== -1 && endIndex !== -1) {
    const beforeJson = content.substring(0, startIndex);
    const afterJson = content.substring(endIndex + endMarker.length);
    console.log('✓ Removed game info from HTML comments');
    return (beforeJson + afterJson).trim();
  }
  
  // Method 2: Remove standalone JSON block (fallback)
  console.log('HTML comments not found, trying to remove standalone JSON...');
  
  // Look for JSON block that starts with { and contains typical game info fields
  const jsonRegex = /\{[\s\S]*?"provider"[\s\S]*?"rtp"[\s\S]*?\}/;
  const cleanContent = content.replace(jsonRegex, '').trim();
  
  if (cleanContent !== content) {
    console.log('✓ Removed standalone JSON block');
    return cleanContent;
  }
  
  console.log('No game info JSON found to remove');
  return content;
}

// Helper function to generate slug from title
function generateSlug(title) {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim('-'); // Remove leading/trailing hyphens
}

// Helper function to extract title from article content
function extractTitle(content, fallbackTitle) {
  if (!content) return fallbackTitle || 'Untitled Article';

  // Try to find H1 heading
  const h1Match = content.match(/^#\s+(.+)$/m);
  if (h1Match) {
    return h1Match[1].trim();
  }

  // Try to find any heading
  const headingMatch = content.match(/^#+\s+(.+)$/m);
  if (headingMatch) {
    return headingMatch[1].trim();
  }

  // Try to find title in first line
  const lines = content.split('\n').filter(line => line.trim());
  if (lines.length > 0) {
    const firstLine = lines[0].replace(/^#+\s*/, '').trim();
    if (firstLine.length > 0 && firstLine.length < 200) {
      return firstLine;
    }
  }

  return fallbackTitle || 'Untitled Article';
}

// Helper function to extract excerpt from article content
function extractExcerpt(content, maxLength = 200) {
  if (!content) return '';

  // Remove markdown formatting
  let text = content
    .replace(/^#+\s+/gm, '') // Remove headings
    .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
    .replace(/\*(.*?)\*/g, '$1') // Remove italic
    .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`(.*?)`/g, '$1') // Remove inline code
    .replace(/\n+/g, ' ') // Replace newlines with spaces
    .trim();

  // Get first meaningful paragraph
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
  if (sentences.length > 0) {
    let excerpt = sentences[0].trim();
    if (excerpt.length > maxLength) {
      excerpt = excerpt.substring(0, maxLength).trim();
      // Try to end at a word boundary
      const lastSpace = excerpt.lastIndexOf(' ');
      if (lastSpace > maxLength * 0.8) {
        excerpt = excerpt.substring(0, lastSpace);
      }
      excerpt += '...';
    }
    return excerpt;
  }

  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// Helper function to ensure unique slug
async function ensureUniqueSlug(baseSlug) {
  let slug = baseSlug;
  let counter = 1;

  while (true) {
    const existing = await database.get(
      'SELECT id FROM blog_posts WHERE slug = ?',
      [slug]
    );

    if (!existing) {
      return slug;
    }

    slug = `${baseSlug}-${counter}`;
    counter++;
  }
}

// POST /api/tasks/:id/finish - Mark task as completed and update title
router.post('/:id/finish', checkResourceOwnership('task'), async (req, res) => {
  try {
    // Get the task first to extract the article
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    if (!task.generated_article) {
      return res.status(400).json({
        error: 'No article to finish',
        message: 'Please generate an article before finishing the task'
      });
    }

    // Prepare update query with title extraction
    const updates = ['status = ?', 'current_step = ?', 'updated_at = CURRENT_TIMESTAMP'];
    const params = ['Completed', 6]; // Set to final step (0-indexed)

    // Extract SEO title from the generated article
    if (task.generated_article && typeof task.generated_article === 'string') {
      try {
        // Look for the SEO Title in the structured output format with more precise regex
        // This regex ensures we only capture the title line and stop at the next line or meta description
        const seoTitleMatch = task.generated_article.match(/\*\*SEO Title:\*\*\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*\[?([^\]\n]+?)\]?(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/\*\*SEO Title:\*\*\s*([^\n]+?)(?:\s*\n|\s*$)/i) ||
                             task.generated_article.match(/SEO Title:\s*([^\n]+?)(?:\s*\n|\s*$)/i);

        if (seoTitleMatch && seoTitleMatch[1]) {
          let articleTitle = seoTitleMatch[1].trim();
          // Remove any markdown formatting and brackets
          articleTitle = articleTitle.replace(/[#*_`\[\]]/g, '').trim();
          // Remove any trailing periods or punctuation that might be part of the format
          articleTitle = articleTitle.replace(/[.]*$/, '').trim();
          // Stop at any meta description content that might have been captured
          articleTitle = articleTitle.split(/meta\s*description/i)[0].trim();
          // Limit title length
          articleTitle = articleTitle.slice(0, 200);

          if (articleTitle) {
            updates.push('name = ?');
            params.push(articleTitle);
            console.log('Finishing task and updating name to SEO title:', articleTitle);
          }
        } else {
          // Fallback: try to extract from markdown header or first line
          const fallbackMatch = task.generated_article.match(/^#\s*(.+)$/m) ||
                               task.generated_article.match(/^(.+)$/m);

          if (fallbackMatch && fallbackMatch[1]) {
            let articleTitle = fallbackMatch[1].trim();
            articleTitle = articleTitle.replace(/[#*_`]/g, '').trim();
            articleTitle = articleTitle.slice(0, 200);

            if (articleTitle) {
              updates.push('name = ?');
              params.push(articleTitle);
              console.log('Finishing task and updating name to fallback title:', articleTitle);
            }
          }
        }
      } catch (error) {
        console.warn('Failed to extract SEO title:', error);
        // Continue without updating the name
      }
    }

    // Add task ID and user ID to params
    params.push(req.params.id, req.user.id);

    // Update task to completed status with new title
    await database.run(
      `UPDATE tasks SET ${updates.join(', ')} WHERE id = ? AND user_id = ?`,
      params
    );

    // Auto-publish article to blog_posts table
    try {
      // Extract title from article content or use task name
      const title = extractTitle(task.generated_article, task.name || task.title);

      // Generate unique slug
      const baseSlug = generateSlug(title);
      const uniqueSlug = await ensureUniqueSlug(baseSlug);

      // Extract excerpt
      const excerpt = extractExcerpt(task.generated_article);

      // Get correct content type from output parameters
      const outputParameters = task.output_parameters ? JSON.parse(task.output_parameters) : {};
      const contentType = outputParameters.contentType || task.content_type || 'general';

      // Extract game info if it's a game guide
      let gameInfo = null;
      let cleanContent = task.generated_article;
      
      if (contentType === 'game_guide' && task.generated_article) {
        // Extract GAME_INFO JSON from content
        gameInfo = extractGameInfoFromContent(task.generated_article);
        // Remove GAME_INFO from content for clean display
        cleanContent = removeGameInfoFromContent(task.generated_article);
      }

      // Check if already published (avoid duplicates)
      // First check by slug, then also check if this task has already been published
      const existingPost = await database.get(
        'SELECT id FROM blog_posts WHERE slug = ?',
        [uniqueSlug]
      );

      // Also check if this specific task has already been auto-published
      // Use a combination of title, author, and created_at to identify duplicates
      const taskAlreadyPublished = await database.get(
        'SELECT id FROM blog_posts WHERE title = ? AND author = ? AND created_at = ?',
        [title, 'Writer 777 AI', task.created_at]
      );

      if (!existingPost && !taskAlreadyPublished) {
        // Convert markdown to HTML for proper display
        const htmlContent = markdownToHtml(cleanContent);

        // Insert into blog_posts table
        await database.run(`
          INSERT INTO blog_posts (
            title,
            slug,
            content,
            excerpt,
            content_type,
            game_info,
            author,
            status,
            language,
            created_at,
            updated_at,
            published_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          title,
          uniqueSlug,
          htmlContent,
          excerpt,
          contentType,
          gameInfo ? JSON.stringify(gameInfo) : null,
          'Writer 777 AI',
          'published',
          task.target_language || 'en', // 添加语言字段
          task.created_at,
          new Date().toISOString(),
          new Date().toISOString()
        ]);

        console.log(`✅ Auto-published article: ${title} (${uniqueSlug})`);
      } else {
        if (existingPost) {
          console.log(`⚠️ Article already published with slug: ${uniqueSlug}`);
        }
        if (taskAlreadyPublished) {
          console.log(`⚠️ Task already auto-published: ${title}`);
        }
      }
    } catch (publishError) {
      console.error('Error auto-publishing article:', publishError);
      // Don't fail the task completion if publishing fails
    }

    // Return updated task
    const updatedTask = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    const formattedTask = {
      ...updatedTask,
      keywords: updatedTask.keywords ? JSON.parse(updatedTask.keywords) : [],
      selectedKeywords: updatedTask.selected_keywords ? JSON.parse(updatedTask.selected_keywords) : [],
      secondaryKeywords: updatedTask.secondary_keywords ? JSON.parse(updatedTask.secondary_keywords) : [],
      selectedTopics: updatedTask.selected_topics ? JSON.parse(updatedTask.selected_topics) : [],
      topicSuggestions: updatedTask.topic_suggestions ? JSON.parse(updatedTask.topic_suggestions) : [],
      keywordResearchData: updatedTask.keyword_research_data ? JSON.parse(updatedTask.keyword_research_data) : null,
      keywordResearchSelections: updatedTask.keyword_research_selections ? JSON.parse(updatedTask.keyword_research_selections) : [],
      sources: updatedTask.sources ? JSON.parse(updatedTask.sources) : [],
      topicSources: updatedTask.topic_sources ? JSON.parse(updatedTask.topic_sources) : {},
      productInfo: updatedTask.product_info ? JSON.parse(updatedTask.product_info) : {},
      eeatProfile: updatedTask.eeat_profile ? JSON.parse(updatedTask.eeat_profile) : {},
      outputParameters: updatedTask.output_parameters ? JSON.parse(updatedTask.output_parameters) : {}
    };

    res.json({
      message: 'Task completed successfully',
      task: formattedTask
    });

  } catch (error) {
    console.error('Finish task error:', error);
    res.status(500).json({
      error: 'Failed to finish task',
      message: error.message || 'An error occurred while finishing the task'
    });
  }
});

// DELETE /api/tasks/:id - Delete a task
router.delete('/:id', checkResourceOwnership('task'), async (req, res) => {
  try {
    const result = await database.run(
      'DELETE FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    if (result.changes === 0) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    res.json({
      message: 'Task deleted successfully'
    });

  } catch (error) {
    console.error('Delete task error:', error);
    res.status(500).json({
      error: 'Failed to delete task',
      message: 'An error occurred while deleting the task'
    });
  }
});


// POST /api/tasks/:id/publish - Manually publish article to blog_posts
router.post('/:id/publish', checkResourceOwnership('task'), async (req, res) => {
  try {
    console.log(`📤 Publishing article for task: ${req.params.id}`);

    // Get the task first to extract the article
    const task = await database.get(
      'SELECT * FROM tasks WHERE id = ? AND user_id = ?',
      [req.params.id, req.user.id]
    );

    console.log(`📋 Task found: ${task ? 'Yes' : 'No'}, Has article: ${task?.generated_article ? 'Yes' : 'No'}`);

    if (!task) {
      return res.status(404).json({
        error: 'Task not found',
        message: 'The requested task could not be found'
      });
    }

    if (!task.generated_article) {
      return res.status(400).json({
        error: 'No article to publish',
        message: 'This task does not have a generated article to publish'
      });
    }

    // Extract title from article content or use task name
    console.log(`🏷️ Extracting title from article...`);
    const title = extractTitle(task.generated_article, task.name || task.title);
    console.log(`📝 Extracted title: ${title}`);

    // Generate unique slug
    console.log(`🔗 Generating slug...`);
    const baseSlug = generateSlug(title);
    const uniqueSlug = await ensureUniqueSlug(baseSlug);
    console.log(`🔗 Generated unique slug: ${uniqueSlug}`);

    // Extract excerpt
    console.log(`📄 Extracting excerpt...`);
    const excerpt = extractExcerpt(task.generated_article);
    console.log(`📄 Extracted excerpt length: ${excerpt.length}`);

    // Check if already published
    console.log(`🔍 Checking if article already published...`);
    const existingPost = await database.get(
      'SELECT id, slug FROM blog_posts WHERE slug = ?',
      [uniqueSlug]
    );

    // Also check if this specific task has already been published (by title, author, and created_at)
    const taskAlreadyPublished = await database.get(
      'SELECT id, slug FROM blog_posts WHERE title = ? AND author = ? AND created_at = ?',
      [title, 'Writer 777 AI', task.created_at]
    );

    if (existingPost || taskAlreadyPublished) {
      const conflictPost = existingPost || taskAlreadyPublished;
      console.log(`⚠️ Article already published with slug: ${conflictPost.slug}`);
      return res.status(409).json({
        error: 'Article already published',
        message: 'This article has already been published to your homepage',
        article: {
          id: conflictPost.id,
          slug: conflictPost.slug,
          title: title
        }
      });
    }

    // Get correct content type from output parameters
    console.log(`🏷️ Determining content type...`);
    const outputParameters = task.output_parameters ? JSON.parse(task.output_parameters) : {};
    const contentType = outputParameters.contentType || task.content_type || 'general';
    console.log(`📝 Content type: ${contentType}`);

    // Extract game info if it's a game guide
    let gameInfo = null;
    let cleanContent = task.generated_article;
    
    if (contentType === 'game_guide' && task.generated_article) {
      console.log(`🎮 Extracting game info for game guide...`);
      gameInfo = extractGameInfoFromContent(task.generated_article);
      cleanContent = removeGameInfoFromContent(task.generated_article);
      console.log(`🎮 Game info extracted: ${gameInfo ? 'Yes' : 'No'}`);
    }

    // Convert markdown to HTML for proper display
    console.log(`🔄 Converting markdown to HTML...`);
    const htmlContent = markdownToHtml(cleanContent);
    console.log(`✅ Converted to HTML, length: ${htmlContent.length}`);

    // Insert into blog_posts table
    console.log(`💾 Inserting article into blog_posts table...`);
    const result = await database.run(`
      INSERT INTO blog_posts (
        title,
        slug,
        content,
        excerpt,
        content_type,
        game_info,
        author,
        status,
        language,
        created_at,
        updated_at,
        published_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      title,
      uniqueSlug,
      htmlContent,
      excerpt,
      contentType,
      gameInfo ? JSON.stringify(gameInfo) : null,
      'Writer 777 AI',
      'published',
      task.target_language || 'en', // 添加语言字段
      task.created_at,
      new Date().toISOString(),
      new Date().toISOString()
    ]);

    console.log(`💾 Insert result:`, result);

    console.log(`✅ Manually published article: ${title} (${uniqueSlug})`);

    res.json({
      message: 'Article published successfully',
      article: {
        id: result.id || result.lastInsertRowid, // Handle both PostgreSQL and SQLite
        title: title,
        slug: uniqueSlug,
        content_type: contentType,
        game_info: gameInfo,
        published_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error publishing article:', error);
    res.status(500).json({
      error: 'Failed to publish article',
      message: error.message
    });
  }
});

module.exports = router;
