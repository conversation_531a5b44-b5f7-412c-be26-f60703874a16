#!/usr/bin/env node

/**
 * Test script to verify the publish fix
 * This script tests that:
 * 1. Language field is correctly set when publishing articles
 * 2. No duplicate articles are created
 * 3. Both auto-publish and manual publish work correctly
 */

// Load environment variables
require('dotenv').config();

const database = require('../config/database');

async function testPublishFix() {
  console.log('🧪 Testing publish fix...\n');

  try {
    // Connect to database first
    await database.connect();
    console.log('✅ Connected to database\n');
    // Test 1: Check if language field is being set correctly
    console.log('📋 Test 1: Checking language field in published articles');
    
    const recentArticles = await database.all(`
      SELECT id, title, slug, language, author, created_at 
      FROM blog_posts 
      WHERE author = 'Writer 777 AI' 
      ORDER BY created_at DESC 
      LIMIT 10
    `);

    console.log(`Found ${recentArticles.length} recent articles by Writer 777 AI:`);
    recentArticles.forEach(article => {
      console.log(`  - ${article.title} (${article.slug})`);
      console.log(`    Language: ${article.language || 'NULL'}`);
      console.log(`    Created: ${article.created_at}`);
      console.log('');
    });

    // Test 2: Check for potential duplicates
    console.log('📋 Test 2: Checking for duplicate articles');
    
    const duplicates = await database.all(`
      SELECT title, COUNT(*) as count, GROUP_CONCAT(slug) as slugs
      FROM blog_posts 
      WHERE author = 'Writer 777 AI'
      GROUP BY title 
      HAVING COUNT(*) > 1
    `);

    if (duplicates.length > 0) {
      console.log(`⚠️  Found ${duplicates.length} potential duplicates:`);
      duplicates.forEach(dup => {
        console.log(`  - "${dup.title}" appears ${dup.count} times`);
        console.log(`    Slugs: ${dup.slugs}`);
      });
    } else {
      console.log('✅ No duplicates found');
    }

    // Test 3: Check language distribution
    console.log('\n📋 Test 3: Language distribution of published articles');
    
    const languageStats = await database.all(`
      SELECT language, COUNT(*) as count
      FROM blog_posts 
      WHERE author = 'Writer 777 AI'
      GROUP BY language
      ORDER BY count DESC
    `);

    console.log('Language distribution:');
    languageStats.forEach(stat => {
      console.log(`  - ${stat.language || 'NULL'}: ${stat.count} articles`);
    });

    // Test 4: Check recent tasks and their publication status
    console.log('\n📋 Test 4: Recent tasks and publication status');
    
    const recentTasks = await database.all(`
      SELECT id, name, status, target_language, target_country, 
             generated_article IS NOT NULL as has_article,
             created_at
      FROM tasks 
      WHERE generated_article IS NOT NULL
      ORDER BY created_at DESC 
      LIMIT 5
    `);

    console.log(`Found ${recentTasks.length} recent tasks with articles:`);
    for (const task of recentTasks) {
      console.log(`\n  Task: ${task.name}`);
      console.log(`    ID: ${task.id}`);
      console.log(`    Status: ${task.status}`);
      console.log(`    Language: ${task.target_language}`);
      console.log(`    Country: ${task.target_country}`);
      console.log(`    Has Article: ${task.has_article ? 'Yes' : 'No'}`);
      
      // Check if this task has been published
      const publishedArticle = await database.get(`
        SELECT id, title, slug, language, created_at as published_at
        FROM blog_posts 
        WHERE author = 'Writer 777 AI' 
        AND created_at = ?
      `, [task.created_at]);

      if (publishedArticle) {
        console.log(`    Published: Yes (${publishedArticle.slug})`);
        console.log(`    Published Language: ${publishedArticle.language}`);
        console.log(`    Language Match: ${publishedArticle.language === task.target_language ? 'Yes' : 'No'}`);
      } else {
        console.log(`    Published: No`);
      }
    }

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    // Close database connection
    if (database.pool) {
      await database.close();
    }
  }
}

// Run the test
if (require.main === module) {
  testPublishFix()
    .then(() => {
      console.log('\n🎉 All tests completed!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testPublishFix };
