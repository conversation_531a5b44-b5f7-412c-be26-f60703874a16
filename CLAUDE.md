# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

Writer J (Writer 777) 是一个复杂的AI文章生成工具，专门为iGaming和在线娱乐行业设计。项目实现了7步战略工作流程，生成高质量、SEO友好的文章，并集成E-E-A-T（Experience, Expertise, Authoritativeness, Trustworthiness）标准。采用React前端 + Node.js/Express后端架构，使用Google Gemini AI进行内容生成。

**项目目标**: 为在线赌场、体育博彩、游戏指南等iGaming内容提供专业化、合规的内容生成解决方案。

## 开发命令

### 前端开发 (根目录)
```bash
npm run dev          # 启动Vite开发服务器 (localhost:5173)
npm run build        # 构建生产版本
npm run preview      # 预览生产构建
npm start            # 启动后端生产服务器
npm run backend:install # 安装后端生产依赖
```

### 后端开发 (backend目录)
```bash
npm run dev          # 启动nodemon开发服务器 (localhost:3001) 
npm start            # 生产环境启动
npm run init-db      # 初始化数据库
npm run test-postgres # 测试PostgreSQL连接
npm run health       # 健康检查
npm run add-role-column # 添加用户角色列（一次性迁移）
npm run railway:deploy # Railway部署
npm run railway:logs # 查看Railway日志（提示）
```

### 数据库管理脚本
```bash
cd backend
npm run init-db                    # 初始化数据库表结构
node scripts/create-super-admin.js # 创建超级管理员账户
node scripts/check-data.js         # 检查数据库数据
node scripts/add-sample-articles.js # 添加示例文章
node scripts/check-published-articles.js # 检查已发布文章
node scripts/test-postgres-connection.js # 测试PostgreSQL连接
```

### 单个功能测试脚本
```bash
cd backend
node test_api.js                   # 测试主要API功能
node test-category-api.js          # 测试分类API
node scripts/test-prompt-system.js # 测试prompt模板系统
node scripts/extract-prompts-from-db.js # 从数据库提取prompt（一次性操作）
```

### API健康检查和状态监控
```bash
npm run health                      # 后端健康检查
curl http://localhost:3001/health   # 直接检查API状态

# Railway部署状态检查
railway logs                        # 查看部署日志
railway status                      # 查看服务状态
```

## 本地开发环境设置

### PostgreSQL本地环境配置
1. **创建本地数据库和用户**:
   ```bash
   # 使用提供的SQL脚本创建数据库
   sudo -u postgres psql -f create-local-db.sql
   
   # 或者手动创建
   sudo -u postgres psql -c "CREATE USER writer777 WITH PASSWORD 'writer777_dev';"
   sudo -u postgres psql -c "CREATE DATABASE writer777_dev OWNER writer777;"
   ```

2. **配置环境变量**:
   ```bash
   # 复制环境变量模板
   cp backend/.env.example backend/.env
   
   # 编辑 backend/.env 文件，设置以下变量:
   DATABASE_URL=postgresql://writer777:writer777_dev@localhost:5432/writer777_dev
   NODE_ENV=development
   GEMINI_API_KEY=your_gemini_api_key_here
   JWT_SECRET=writer777_local_development_secret_key_32_chars_minimum
   ```

3. **初始化数据库**:
   ```bash
   cd backend
   npm run init-db
   node scripts/create-super-admin.js
   ```

4. **启动开发服务器**:
   ```bash
   # 后端 (在 backend 目录)
   npm run dev
   
   # 前端 (在根目录)
   npm run dev
   ```

## 核心架构

### 前端架构 (/src)
**核心技术栈**:
- **React 19.1** + **Vite 5.4** - 现代化开发栈
- **Tailwind CSS 3.4** - 实用优先的CSS框架
- **Headless UI 2.2** - 无障碍的UI组件
- **React Router DOM 6.28** - 前端路由管理
- **React i18next 15.5** - 国际化支持

**功能特性**:
- **7步工作流组件**: Step0-Step5 组件实现完整的文章生成流程
- **专业布局模板**: 9种专业化布局适配iGaming内容
- **认证系统**: JWT token认证，角色权限管理
- **任务管理**: 文章生成任务的全生命周期管理
- **多语言风险分析**: 内置关键词风险检测和合规性检查
- **实时编辑**: 流式生成，自动保存，实时验证

### 后端架构 (/backend)
**核心技术栈**:
- **Node.js + Express 4.21** - 服务器运行时和网络框架
- **PostgreSQL 8.16** - 主数据库（必需）
- **Google Gemini API 1.1** - AI内容生成
- **Cheerio 1.0** - 服务器端网页抓取
- **JWT + bcryptjs** - 认证和密码加密

**服务层架构**:
- **AI集成层**: Google Gemini API服务 (`services/geminiService.js`)
- **内容提取**: 网页内容抓取和处理 (`services/contentExtractor.js`)
- **风险分析**: 多语言关键词风险检测 (`services/riskKeywordService.js`)
- **Prompt管理**: 基于代码的模板系统 (`services/promptTemplateService.js`)
- **数据库层**: PostgreSQL连接池管理和事务处理
- **安全中间件**: 认证、CORS、速率限制、Helmet安全头、管理员权限控制

### 数据库设计 (PostgreSQL)
**核心数据表**:
- **users**: 用户管理，支持角色系统 (user/admin/super_admin)
- **articles**: 文章系统，草稿/发布状态管理，支持HTML/Markdown
- **article_tasks**: 文章生成任务的全生命周期管理
- **prompt_templates**: 历史模板数据（现以代码管理为主）
- **settings**: 系统设置和API密钥管理
- **categories**: 文章分类管理
- **affiliate_links**: 联盟链接管理
- **user_sessions**: JWT令牌会话管理

**数据特性**:
- 事务安全和连接池管理
- 完整的外键约束和数据一致性
- 自动时间戳和元数据跟踪
- 支持JSON字段存储复杂数据结构

## 部署配置

项目支持双平台部署：
- **前端**: Vercel部署 (`vercel.json`配置)
- **后端**: Railway专用部署 (`railway.toml`, `backend/railway.json`)
- 自动PostgreSQL数据库配置
- NIXPACKS构建器支持
- 健康检查和自动重启配置

## 环境变量要求

### 后端必需变量
```
GEMINI_API_KEY=your_gemini_api_key    # Google Gemini AI API密钥
SERPER_API_KEY=your_serper_api_key    # Serper搜索API密钥（可选）
JWT_SECRET=your_jwt_secret            # JWT认证密钥
DATABASE_URL=postgresql://...         # PostgreSQL连接字符串
PORT=3001                            # 后端端口
NODE_ENV=production|development       # 环境标识
FRONTEND_URL=http://localhost:5173    # 前端URL（开发环境）
```

### 前端环境变量（可选）
```
VITE_API_BASE_URL=your_api_url        # 覆盖默认API URL
```

## 主要API端点

### 文章生成流程
```
POST /api/ideation/suggestions      # 从关键词生成主题建议
POST /api/sources/extract           # 单个URL内容提取
POST /api/sources/extract-multiple  # 多URL并发内容提取
POST /api/generate/article          # 根据所有输入生成完整文章
```

### 用户管理和认证
```
POST /api/auth/register             # 用户注册
POST /api/auth/login                # 用户登录
GET  /api/auth/profile              # 获取用户信息
POST /api/auth/logout               # 用户登出
```

### 文章和任务管理
```
GET  /api/articles                  # 获取文章列表
POST /api/articles                  # 创建新文章
PUT  /api/articles/:id              # 更新文章
DELETE /api/articles/:id            # 删除文章
GET  /api/article-tasks             # 获取任务列表
POST /api/article-tasks             # 创建新任务
```

### 文件上传和静态资源
```
POST /api/upload                    # 文件上传（需要认证）
GET  /uploads/*                     # 访问上传的文件
GET  /api/debug/uploads             # 调试：列出所有上传文件
```

### 管理员功能
```
GET  /api/admin/prompt-templates    # 获取所有prompt模板
GET  /api/admin/settings            # 获取系统设置
PUT  /api/admin/settings            # 更新系统设置
GET  /api/health                    # 系统健康检查
GET  /admin.html                    # 管理面板界面
```

## 重要文件位置

- **认证逻辑**: `backend/middleware/auth.js`, `src/contexts/AuthContext.jsx`
- **AI提示模板**: `backend/config/promptTemplates.js` (新的基于代码的配置)
- **风险检测**: `backend/services/riskKeywordService.js`
- **数据库配置**: `backend/config/database.js`
- **API路由**: `backend/routes/` 目录下的所有文件
- **组件库**: `src/components/` 下的分类组件
- **文件上传**: `backend/middleware/upload.js`, `backend/routes/upload.js`
- **静态文件服务**: `backend/server.js:148-153` (public和uploads目录)

## 开发注意事项

### 7步内容生成工作流
1. **关键词研究与主题发现 (Step0)** - 综合关键词研究，自动完成建议，相关关键词分析
2. **主题选择 (Step1)** - AI生成的主题建议，基于关键词研究的战略主题聚类
3. **来源集成 (Step2)** - URL内容提取，自定义文本块集成，Reddit讨论分析
4. **产品集成 (Step3)** - 产品/服务信息输入，自然产品植入策略
5. **E-E-A-T权威配置 (Step4)** - 作者凭证，专业简历，专业知识展示
6. **样式与格式参数 (Step5)** - 自定义语调，文章长度选项，目标受众规范
7. **文章生成与优化 (Step6)** - AI驱动的文章生成，实时编辑功能，自动保存

### 风险管理
- 所有用户输入都经过风险关键词检测
- 支持多语言风险分析 (中文、英文等)
- 内置合规性检查清单

### 数据库操作
- 使用 `scripts/init-database.js` 初始化数据库结构
- 完全基于PostgreSQL，本地和生产环境使用相同数据库技术
- 所有数据库操作都有事务保护和连接池管理

### API集成
- **Google Gemini AI**: 主要的AI内容生成服务
- **Serper API**: 搜索引擎结果和关键词研究
- **Mock模式**: 无API密钥时的开发/测试模式
- **内容提取**: 支持多URL并发处理，Cheerio网页抓取
- **错误处理**: 所有API调用都有优雅的错误处理和重试机制
- **缓存系统**: 使用node-cache提高API响应性能

## 测试和验证命令

### 数据库连接测试
```bash
cd backend
npm run test-postgres    # 测试PostgreSQL连接
node scripts/check-data.js # 检查数据库数据完整性
```

### API健康检查
```bash
curl http://localhost:3001/health  # 本地健康检查
npm run health                     # 运行健康检查命令
```

### 单个功能测试
```bash
# 测试API端点
node test_api.js                   # 测试主要API功能
node test-category-api.js          # 测试分类API

# 数据库维护脚本
cd backend/scripts
node create-super-admin.js         # 创建超级管理员
node add-sample-articles.js        # 添加示例文章
node check-published-articles.js   # 检查已发布文章
```

## 开发特性和工具

### 代码质量管理
本项目目前没有配置ESLint、Prettier或其他代码质量工具。如需添加：
1. 安装相应的开发依赖
2. 配置lint规则文件
3. 在package.json中添加对应的npm scripts

### Vite开发配置
- 开发服务器配置在 `vite.config.js` 中
- 开发环境代理：所有 `/api/*` 请求自动代理到 `http://localhost:3001`
- 热重载和快速刷新已启用

### Git推送和部署
修改完成后，会自动推送到GitHub。后端部署在Railway，前端可部署在Vercel。

### 开发模式特性
- **Mock模式**: 后端在无API密钥时可以正常开发/测试
- **错误处理**: 优雅的API调用失败处理和降级方案
- **响应式设计**: 支持桌面和移动设备
- **实时验证**: 输入验证和用户反馈
- **进度跟踪**: 可视化步骤指示器和完成状态
- **自动保存**: 文章生成过程中的自动保存功能
- **文件上传**: 支持图片和文档上传，存储在`backend/data/uploads`目录
- **调试工具**: 内置调试路由用于开发时的数据检查

### 测试策略
项目包含多个测试脚本用于验证功能：
- `backend/test_api.js` - 主要API功能测试
- `backend/test-category-api.js` - 分类API测试
- `backend/scripts/test-prompt-system.js` - Prompt系统测试
- `backend/scripts/test-postgres-connection.js` - 数据库连接测试
- 手动集成测试和健康检查

未来可考虑添加Jest或Vitest来完善自动化测试覆盖。

## 多语言支持

项目支持8种语言的国际化：
- **前端i18n配置**：`src/i18n/index.js`
- **支持语言**：en, zh, pt, es, de, fr, it, ja
- **语言文件位置**：`src/i18n/locales/`
- **语言组件**：`LanguageSwitcher.jsx`, `LanguageSelector.jsx`
- **风险关键词检测**：支持8种语言的风险词库（`backend/services/riskKeywordService.js`）
- **中文特色**：完整的中文风险关键词库，包含6个风险类别和安全替代词汇
- **自动语言检测**：浏览器语言适配和本地存储

## API配置和环境

### 前端API配置
- API配置文件：`src/config/api.js`
- 默认生产URL：`https://writer777-production.up.railway.app`
- 开发环境代理：通过Vite代理到localhost:3001
- 支持环境变量覆盖：`VITE_API_BASE_URL`

### 后端API密钥管理
- 主要API服务：Google Gemini AI (`GEMINI_API_KEY`)
- 搜索服务：Serper API (`SERPER_API_KEY`)
- 认证密钥：JWT (`JWT_SECRET`)
- 可通过管理面板配置API密钥，或使用环境变量

## 项目特殊注意事项

### 安全和合规
- 内置多语言风险关键词检测系统
- 所有用户输入都会进行风险分析
- 支持赌博、加密货币等敏感内容的合规性检查
- 管理员角色系统：user/admin/super_admin
- 默认超级管理员：<EMAIL>

### 专业内容布局系统
项目专门针对iGaming和在线娱乐内容优化，支持多种专业化文章布局：
- **赌场评论布局** (`CasinoReviewLayout.jsx`) - 完整的赌场分析框架
- **游戏指南布局** (`GameGuideLayout.jsx`, `SimpleGameGuideLayout.jsx`) - 游戏规则和策略指导
- **体育博彩布局** (`SportsBettingLayout.jsx`) - 体育投注分析和建议
- **策略文章布局** (`StrategyArticleLayout.jsx`) - 游戏策略和技巧文章
- **行业新闻布局** (`IndustryNewsLayout.jsx`) - iGaming行业动态报道
- **奖金分析布局** - 赌场奖金和促销活动分析

### 主题化样式系统
Tailwind配置包含专业的赌场主题：
- **Casino颜色系统**: gold, red, green专业配色
- **自定义字体**: casino和elegant字体家族
- **特效动画**: pulse-gold, glow, chip-flip等赌场风格动画

### 部署特殊要求
- **前端**: Vercel部署 (`vercel.json`配置)
- **后端**: Railway专用部署 (`railway.toml`, `backend/railway.json`)
- 必须使用PostgreSQL数据库，不再支持SQLite
- 自动健康检查和重启配置

## Prompt模板系统重构 (重要更新)

### 新的基于代码的Prompt管理
项目已从数据库驱动的prompt模板系统重构为基于代码的管理方式：

**核心配置文件**:
- `backend/config/promptTemplates.js` - 所有prompt模板的中央配置
- `backend/services/promptTemplateService.js` - 重构后的服务层

**支持的内容类型**:
- `casino_review` - 赌场评论模板
- `game_guide` - 游戏指南模板  
- `strategy_article` - 策略文章模板
- `brand_copy` - 品牌文案模板
- `industry_news` - 行业新闻模板
- `sports_betting` - 体育博彩模板
- `bonus_analysis` - 奖金分析模板
- `regulatory_update` - 监管更新模板
- `generic` - 通用内容模板

**重要脚本**:
```bash
# 从数据库提取prompt模板到代码中
node backend/scripts/extract-prompts-from-db.js

# 测试新的prompt系统
node backend/scripts/test-prompt-system.js
```

**向后兼容性**:
- 所有现有API继续工作
- `initializeDefaultTemplates()` 现在为空操作
- `createTemplate()` 和 `updateTemplate()` 抛出指导性错误

**版本控制优势**:
- Prompt模板现在可以通过Git进行版本控制
- 支持代码审查和变更跟踪
- 部署更简单，无需数据库同步
- 所有模板使用统一的BASE_TEMPLATE_STRUCTURE结构
- 基于game_guide模板的完整prompt框架

## 重要开发流程注意事项

### 自动化工作流程
- 完成任务后会自动push到GitHub
- 不需要每次都要求用户确认，直接执行最有效的解决方案
- 修改完成后进行全盘检查，确保代码修改有效且不会带来新问题
- 优先一次性修复问题，避免多次迭代

### 项目维护脚本
所有维护脚本都位于`backend/scripts/`目录下，包括数据库迁移、管理员创建、数据检查等功能。执行前确保在正确的工作目录中。

### TaskEditor组件重要信息
TaskEditor组件 (`src/components/tasks/TaskEditor.jsx`) 是核心的8步工作流界面：
1. 关键词研究 (Step0KeywordResearch)
2. 主题选择 (Step1TopicSelection) 
3. 来源收集 (Step3Sources)
4. 产品集成 (Step3Product)
5. E-E-A-T权威配置 (Step4EEAT)
6. 样式与格式 (Step4Parameters)
7. 合规设置 (ComplianceSettings)
8. 文章生成 (Step5Generation)

该组件管理所有步骤的状态，包括关键词、主题建议、来源、产品信息等，确保数据在步骤间正确传递和保存。