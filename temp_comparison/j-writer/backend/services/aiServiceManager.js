const geminiService = require('./geminiService');
const deepseekService = require('./deepseekService');
const database = require('../config/database');

class AIServiceManager {
  constructor() {
    this.services = {
      'gemini': geminiService,
      'deepseek': deepseekService
    };
    this.defaultModel = 'gemini';
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;

    try {
      // Initialize default models and prompts if they don't exist
      await this.initializeDefaultModels();
      await this.initializeDefaultPrompts();
      this.initialized = true;
      console.log('AI Service Manager initialized successfully');
    } catch (error) {
      console.error('Failed to initialize AI Service Manager:', error);
      throw error;
    }
  }

  async initializeDefaultModels() {
    try {
      // Check if models already exist
      const existingModels = await database.all('SELECT * FROM ai_models');
      
      if (existingModels.length === 0) {
        console.log('Initializing default AI models...');
        
        // Insert Gemini model (with empty API key - to be configured via admin)
        await database.run(
          'INSERT INTO ai_models (model_name, provider, api_key, model_version, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?)',
          ['gemini', 'google', process.env.GEMINI_API_KEY || '', 'gemini-2.5-pro-preview-05-06', true, true]
        );

        // Insert DeepSeek model (with empty API key - to be configured via admin)
        await database.run(
          'INSERT INTO ai_models (model_name, provider, api_key, model_version, is_active, is_default) VALUES (?, ?, ?, ?, ?, ?)',
          ['deepseek', 'deepseek', process.env.DEEPSEEK_API_KEY || '', 'deepseek-reasoner', true, false]
        );

        console.log('Default AI models initialized');
      }
    } catch (error) {
      console.error('Error initializing default models:', error);
      throw error;
    }
  }

  async initializeDefaultPrompts() {
    try {
      // Check if prompts already exist
      const existingPrompts = await database.all('SELECT * FROM prompt_templates');
      
      if (existingPrompts.length === 0) {
        console.log('Initializing default prompts...');
        
        // Only 2 prompt types needed: clustered_topics and article_generation

        // Strategic clustered topic generation prompt (used in Step 1 for organized clusters)
        const clusteredTopicPrompt = `You are an expert AI Content Strategist and Ideation Assistant. Your mission is to transform a list of user-selected keywords into a rich array of compelling, relevant, and actionable article topics, exploring all sensible possibilities and organizing them into strategic clusters.

A user has completed a detailed keyword research phase, analyzing Google Autocomplete, "People Also Ask" (PAA), Related Keywords, and Key Terms extracted from top search results. They have carefully selected the following keywords as highly relevant and representative of their content goals and audience's interests.

User-Selected Keywords: {keywords}

Your task is to generate a comprehensive and diverse list of distinct article topics based *only* on these keywords. Please follow these guidelines meticulously for maximum impact:

1. **Goal & Quality:** Generate a *wide variety* of unique and high-quality article topics. Aim for a substantial list, but *never* sacrifice relevance, clarity, or value for sheer quantity.

2. **Strict Relevance:** Every topic *must* directly stem from one or more of the provided keywords. Do *not* introduce external concepts.

3. **Strategic Combination (High Priority):** **Actively seek and prioritize opportunities to intelligently combine multiple keywords** from the list. This is crucial for creating richer, more specific, in-depth, and targeted ideas.

4. **Strategic Grouping:** Your primary goal is to group the generated topics into logical, strategic clusters based on user intent and content format. This helps the user see the relationships between ideas and build a coherent content plan.

5. **User Intent Focus (Inspiration):** Consider the *likely user intent* behind these keywords to spark ideas for your clusters:
    * **Answering Questions:** Addressing PAA or implied queries.
    * **Providing Instructions:** How-To guides, tutorials.
    * **Solving Problems:** Addressing pain points.
    * **Offering Comparisons:** "X vs Y" or "Best X".
    * **Informing & Explaining:** Deep dives, "What is," "Why X matters."
    * **Curating Lists:** Listicles, resource roundups.

6. **Diverse Angles & Creativity (Explore Freely):** **Think expansively!** Explore different formats and perspectives. Ensure some topics allow an author to showcase **Experience, Expertise, and build Trust/Authority (E-E-A-T).** Be creative and suggest unique angles that will capture attention. Examples include:
    * **Trend Analyses & Predictions**
    * **Opinion Pieces & Thought Leadership**
    * **Myth-Busting & Debunking Articles**
    * **Historical Deep Dives & Evolution Pieces**
    * **Case Studies & Success Stories**

7. **Clarity & Actionability:** Topics should be phrased as compelling, clear titles that immediately suggest the article's value.

8. **Output Format:** **CRITICAL: You MUST use this exact Markdown format.** Use H3 headings (###) followed by numbered lists. Do not add any introductory text, explanations, or concluding remarks. Start directly with the first cluster.

### Pillar Content & Broad Guides
1. [Your comprehensive topic idea here]
2. [Your comprehensive topic idea here]
3. [Your comprehensive topic idea here]

### Question-Based Topics
1. [Your question-answering topic here]
2. [Your question-answering topic here]
3. [Your question-answering topic here]

### Niche Angles & Thought Leadership
1. [Your unique/opinionated topic here]
2. [Your unique/opinionated topic here]
3. [Your unique/opinionated topic here]

### How-To's & Actionable Guides
1. [Your tutorial/guide topic here]
2. [Your tutorial/guide topic here]
3. [Your tutorial/guide topic here]

### Comparison & List-Based Topics
1. [Your comparison/list topic here]
2. [Your comparison/list topic here]
3. [Your comparison/list topic here]

**IMPORTANT:** Follow this format exactly. Each cluster must start with "### " followed by the cluster name, then numbered list items starting with "1. ". Generate 3-5 topics per cluster.

Think like an innovative and seasoned content manager aiming to build a robust, diverse, and audience-focused content plan. Generate the clustered list now.`;

        // Enhanced article generation prompt (comprehensive version from services)
        const defaultArticlePrompt = `You are an Elite AI Thought Leader and Columnist, with a voice similar to top writers for Harvard Business Review or The Atlantic. Your expertise is in analyzing complex topics and presenting them with a unique, insightful angle. Your mission is to write **ONE SINGLE, COHESIVE, and insightful article** that goes beyond mere synthesis. You must **ANALYZE and INTERPRET** the provided inputs to develop a **UNIQUE CENTRAL THESIS** that provides fresh value to the reader.

**CRITICAL PRIORITY RULES (Follow in exact order):**

1. **ACCURACY FIRST:** Never fabricate studies, journals, research data, or specific citations. Use general attribution like "studies suggest" or "research indicates" unless citing provided sources.
2. **THESIS-DRIVEN:** Develop one clear, non-obvious central argument that connects all provided concepts. Don't just synthesize—interpret and challenge assumptions.
3. **ZERO DUPLICATION:** Every paragraph must introduce new value. If topics overlap, find distinct angles or deeper levels of analysis.
4. **NATURAL INTEGRATION:** Product mentions must serve as organic examples that prove points, not sales pitches.
5. **AUTHORIAL VOICE:** Fully embody the specified author's expertise and tone throughout.

**CRITICAL OUTPUT REQUIREMENTS (Strict Order - Use these exact labels):**

1.  **SEO Title:** [Generate one 55-65 character title that includes primary keywords and is engaging]
2.  **Meta Description:** [Generate one 150-160 character description summarizing the article's core value proposition.]
3.  **Focus Keywords:** [List up to 5 *most important* keywords/phrases for this specific article.]
4.  **Tags:** [List up to 5 relevant tags (broader themes) for categorization, **formatted as a single comma-separated string.**]
**--------------------------------------------------------------------------** (Separator)
5.  **Full Article:** [Generate the complete article starting *directly with the Introduction*. **DO NOT include an H1 tag (#) within the article body.**]
6.  **About the Author Section:** [At the end of the article, generate a final H2 section titled "About the Author" and write a brief paragraph based on the author bio provided in the inputs. If no bio is provided, output "N/A".]
7.  **Visual Aid Suggestions:** [After the "About the Author Section", generate a final H2 section titled "Visual Aid Suggestions". Identify 1-2 key concepts in the article that would be best explained with a visual diagram, flowchart, or infographic, and briefly describe each suggested visual in 1-2 sentences.]

**ENHANCED WRITING INSTRUCTIONS:**

**STYLISTIC GUIDELINES & WORD PREFERENCES (CRITICAL):**
Your writing must be direct, confident, and precise.
* **Prioritize Clarity and Reader Understanding:** While maintaining an authoritative tone, ensure your explanations are clear and accessible.
* **Vary Sentence Structure:** Mix shorter, direct sentences with longer, analytical ones.
* **Avoid Filler Phrases & Meta-Commentary:** Get straight to the point.

**CITATION & ACCURACY REQUIREMENTS (CRITICAL):**
- NEVER fabricate studies, journals, or specific research data.
- Only cite specific sources if they are provided. Use conservative language like "studies suggest" for general claims.

**STRUCTURAL REQUIREMENTS:**
- Introduction: Start with a provocative hook. NO cliché openings.
- Body: 3-6 H2 sections, each advancing the central argument with smooth transitions.
- Conclusion: Answer the "So what?" question with a forward-looking insight.
- **DO NOT** state the article's structure (e.g., "In this article, we will cover...").

**HYPERLINK REQUIREMENTS (CRITICAL):**
- **Product Links:** Create a markdown hyperlink for the **FIRST OCCURRENCE ONLY** of the product name: [Product Name](product-link).
- **Source Links:** Create **ONE EXTERNAL LINK** per source that has a URL, using relevant anchor text: [relevant text](source-url).

**FINAL INSTRUCTION:** Generate the SEO Title, Meta Description, Focus Keywords, Tags, and complete article now. Follow ALL instructions with precision, prioritizing accuracy and thesis-driven analysis.

**ARTICLE INPUTS & DETAILS:**
{article_inputs}

Follow all provided requirements for tone, length, keywords, and structure.`;

        // Insert prompts for both models
        // Clustered topic generation prompts (used in Step0KeywordResearch)
        await database.run(
          'INSERT INTO prompt_templates (template_name, template_type, model_name, prompt_content, is_active) VALUES (?, ?, ?, ?, ?)',
          ['Clustered Topic Generation', 'clustered_topics', 'gemini', clusteredTopicPrompt, true]
        );

        await database.run(
          'INSERT INTO prompt_templates (template_name, template_type, model_name, prompt_content, is_active) VALUES (?, ?, ?, ?, ?)',
          ['Clustered Topic Generation', 'clustered_topics', 'deepseek', clusteredTopicPrompt, true]
        );

        // Article generation prompts
        await database.run(
          'INSERT INTO prompt_templates (template_name, template_type, model_name, prompt_content, is_active) VALUES (?, ?, ?, ?, ?)',
          ['Enhanced Article Generation', 'article_generation', 'gemini', defaultArticlePrompt, true]
        );

        await database.run(
          'INSERT INTO prompt_templates (template_name, template_type, model_name, prompt_content, is_active) VALUES (?, ?, ?, ?, ?)',
          ['Enhanced Article Generation', 'article_generation', 'deepseek', defaultArticlePrompt, true]
        );

        console.log('Default prompts initialized');
      }
    } catch (error) {
      console.error('Error initializing default prompts:', error);
      throw error;
    }
  }

  async getActiveModel() {
    await this.initialize();
    
    try {
      const activeModel = await database.get(
        'SELECT * FROM ai_models WHERE is_active = ? AND is_default = ? ORDER BY updated_at DESC LIMIT 1',
        [true, true]
      );
      
      return activeModel ? activeModel.model_name : this.defaultModel;
    } catch (error) {
      console.error('Error getting active model:', error);
      return this.defaultModel;
    }
  }

  async getModelConfig(modelName) {
    await this.initialize();
    
    try {
      const model = await database.get(
        'SELECT * FROM ai_models WHERE model_name = ? AND is_active = ?',
        [modelName, true]
      );
      
      return model;
    } catch (error) {
      console.error('Error getting model config:', error);
      return null;
    }
  }

  async getPromptTemplate(modelName, templateType) {
    await this.initialize();

    try {
      const template = await database.get(
        'SELECT * FROM prompt_templates WHERE model_name = ? AND template_type = ? AND is_active = ? ORDER BY updated_at DESC LIMIT 1',
        [modelName, templateType, true]
      );

      return template ? template.prompt_content : null;
    } catch (error) {
      console.error('Error getting prompt template:', error);
      return null;
    }
  }

  async ensureServiceInitialized(modelName) {
    try {
      const modelConfig = await this.getModelConfig(modelName);
      const service = this.services[modelName];

      if (!modelConfig || !service) {
        throw new Error(`Model configuration or service not found for: ${modelName}`);
      }

      if (!modelConfig.api_key) {
        throw new Error(`API key not configured for model: ${modelName}`);
      }

      // Initialize service with the API key from database
      await service.initializeAI(modelConfig.api_key);

      if (!service.initialized) {
        throw new Error(`Failed to initialize ${modelName} service`);
      }
    } catch (error) {
      console.error(`Error ensuring service initialization for ${modelName}:`, error);
      throw error;
    }
  }

  async generateTopicSuggestions(keywords, customPrompt = null, promptType = 'topic_generation', redditInsights = null) {
    await this.initialize();

    const activeModelName = await this.getActiveModel();
    const service = this.services[activeModelName];

    if (!service) {
      throw new Error(`AI service not found for model: ${activeModelName}`);
    }

    // Ensure service is initialized with latest API key
    await this.ensureServiceInitialized(activeModelName);

    try {
      if (customPrompt) {
        // Use custom prompt
        return await service.generateCustomTopics(customPrompt, keywords, redditInsights);
      } else {
        // Use stored prompt template based on prompt type
        const promptTemplate = await this.getPromptTemplate(activeModelName, promptType);
        if (promptTemplate) {
          let prompt = promptTemplate.replace('{keywords}', keywords.join(', '));
          
          // Enhance prompt with Reddit insights if provided
          if (redditInsights) {
            const RedditAnalyzer = require('../utils/redditAnalyzer');
            const redditInsightsText = RedditAnalyzer.prepareInsightsForTopicGeneration(redditInsights);
            if (redditInsightsText) {
              prompt = prompt + '\n\n**REDDIT USER INSIGHTS:**\n' + redditInsightsText + '\n\n**ENHANCED INSTRUCTION:** Prioritize topics that address the real user pain points and trending discussions identified from Reddit communities. Use these insights to generate more relevant and engaging topic suggestions.';
            }
          }
          
          return await service.generateCustomTopics(prompt, keywords, redditInsights);
        } else {
          // No prompt template found - this shouldn't happen with proper database setup
          throw new Error(`No prompt template found for type '${promptType}' and model '${activeModelName}'. Please check your database configuration.`);
        }
      }
    } catch (error) {
      console.error(`Error generating topics with ${activeModelName}:`, error);
      throw error;
    }
  }

  async generateClusteredTopics(keywords, redditInsights = null) {
    return this.generateTopicSuggestions(keywords, null, 'clustered_topics', redditInsights);
  }

  async generateArticle(articleData) {
    await this.initialize();

    const activeModelName = await this.getActiveModel();
    const service = this.services[activeModelName];

    if (!service) {
      throw new Error(`AI service not found for model: ${activeModelName}`);
    }

    // Ensure service is initialized with latest API key
    await this.ensureServiceInitialized(activeModelName);

    try {
      // Pass Reddit sources directly to AI for intelligent analysis
      let enhancedArticleData = { ...articleData };
      if (articleData.redditSources && Array.isArray(articleData.redditSources) && articleData.redditSources.length > 0) {
        console.log(`🔗 Passing ${articleData.redditSources.length} raw Reddit sources to AI for intelligent analysis`);
        // AI will analyze the raw Reddit content instead of using pre-processed insights
        enhancedArticleData.rawRedditData = articleData.redditSources;
      }

      // Get custom prompt template if available
      const promptTemplate = await this.getPromptTemplate(activeModelName, 'article_generation');

      if (promptTemplate && service.generateArticleWithCustomPrompt) {
        // Use database-stored prompt template
        console.log(`Using database prompt for ${activeModelName} article generation`);
        return await service.generateArticleWithCustomPrompt(enhancedArticleData, promptTemplate);
      } else {
        // No prompt template found or service doesn't support custom prompts
        throw new Error(`No article generation prompt template found for model '${activeModelName}' or service doesn't support custom prompts. Please check your database configuration.`);
      }
    } catch (error) {
      console.error(`Error generating article with ${activeModelName}:`, error);
      throw error;
    }
  }

  // Admin methods
  async getAllModels() {
    await this.initialize();
    
    try {
      return await database.all('SELECT * FROM ai_models ORDER BY created_at DESC');
    } catch (error) {
      console.error('Error getting all models:', error);
      throw error;
    }
  }

  async updateModel(modelName, updates) {
    await this.initialize();

    try {
      const { api_key, model_version, is_active, is_default } = updates;

      // If setting as default, unset other defaults first
      if (is_default) {
        await database.run('UPDATE ai_models SET is_default = ? WHERE model_name != ?', [false, modelName]);
      }

      // Build dynamic update query to only update provided fields
      const updateFields = [];
      const updateValues = [];

      if (api_key !== undefined && api_key !== null && api_key.trim() !== '') {
        updateFields.push('api_key = ?');
        updateValues.push(api_key);
      }

      if (model_version !== undefined) {
        updateFields.push('model_version = ?');
        updateValues.push(model_version);
      }

      if (is_active !== undefined) {
        updateFields.push('is_active = ?');
        updateValues.push(is_active);
      }

      if (is_default !== undefined) {
        updateFields.push('is_default = ?');
        updateValues.push(is_default);
      }

      // Always update timestamp
      updateFields.push('updated_at = CURRENT_TIMESTAMP');

      // Add model name for WHERE clause
      updateValues.push(modelName);

      const query = `UPDATE ai_models SET ${updateFields.join(', ')} WHERE model_name = ?`;
      await database.run(query, updateValues);

      return await this.getModelConfig(modelName);
    } catch (error) {
      console.error('Error updating model:', error);
      throw error;
    }
  }

  async getAllPromptTemplates() {
    await this.initialize();
    
    try {
      return await database.all('SELECT * FROM prompt_templates ORDER BY model_name, template_type, created_at DESC');
    } catch (error) {
      console.error('Error getting all prompt templates:', error);
      throw error;
    }
  }

  async updatePromptTemplate(id, updates) {
    await this.initialize();
    
    try {
      const { template_name, prompt_content, is_active } = updates;
      
      await database.run(
        'UPDATE prompt_templates SET template_name = ?, prompt_content = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [template_name, prompt_content, is_active, id]
      );
      
      return await database.get('SELECT * FROM prompt_templates WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error updating prompt template:', error);
      throw error;
    }
  }
}

module.exports = new AIServiceManager();
