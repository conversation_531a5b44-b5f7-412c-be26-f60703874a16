const aiServiceManager = require('../services/aiServiceManager');
const database = require('../config/database');

async function testAIServices() {
  console.log('🧪 Testing AI Services...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Initialize AI service manager
    await aiServiceManager.initialize();
    
    // Test keywords
    const testKeywords = ['productivity', 'time management', 'workflow'];
    
    console.log('\n📊 Testing topic generation...');
    console.log('Keywords:', testKeywords.join(', '));
    
    // Get current active model
    const activeModel = await aiServiceManager.getActiveModel();
    console.log('🎯 Active model:', activeModel);
    
    // Test topic generation
    try {
      console.log('\n🔄 Generating topics...');
      const topics = await aiServiceManager.generateTopicSuggestions(testKeywords);
      
      console.log('✅ Topic generation successful!');
      console.log('📝 Generated topics count:', topics.totalCount || topics.suggestions?.length || 0);
      
      if (topics.suggestions && topics.suggestions.length > 0) {
        console.log('📋 Sample topics:');
        topics.suggestions.slice(0, 3).forEach((topic, index) => {
          console.log(`  ${index + 1}. ${topic}`);
        });
      }
      
      if (topics.clustered && Object.keys(topics.clustered).length > 0) {
        console.log('🗂️ Clusters found:', Object.keys(topics.clustered).join(', '));
      }
      
    } catch (topicError) {
      console.error('❌ Topic generation failed:', topicError.message);
    }
    
    // Test article generation with minimal data
    console.log('\n📝 Testing article generation...');
    
    const testArticleData = {
      topics: ['How to improve productivity with time management'],
      sources: [],
      productInfo: {},
      tonality: 'informative',
      length: 'short_post',
      format: 'markdown',
      authorName: 'Test Author',
      authorBio: 'Test bio',
      targetAudience: 'Professionals',
      articleGoal: 'Educate readers',
      primaryKeywords: ['productivity', 'time management'],
      secondaryKeywords: ['workflow', 'efficiency']
    };
    
    try {
      console.log('🔄 Generating article...');
      const article = await aiServiceManager.generateArticle(testArticleData);
      
      console.log('✅ Article generation successful!');
      console.log('📄 Article length:', article.length, 'characters');
      console.log('📋 Article preview (first 200 chars):');
      console.log(article.substring(0, 200) + '...');
      
    } catch (articleError) {
      console.error('❌ Article generation failed:', articleError.message);
    }
    
    // Test model switching
    console.log('\n🔄 Testing model switching...');
    
    try {
      const allModels = await aiServiceManager.getAllModels();
      console.log('📊 Available models:', allModels.map(m => `${m.model_name} (${m.is_active ? 'active' : 'inactive'})`).join(', '));
      
      // Find an alternative model to test switching
      const alternativeModel = allModels.find(m => m.model_name !== activeModel && m.is_active);
      
      if (alternativeModel) {
        console.log(`🔄 Switching to ${alternativeModel.model_name}...`);
        
        // Temporarily switch models
        await aiServiceManager.updateModel(alternativeModel.model_name, {
          ...alternativeModel,
          is_default: true
        });
        
        // Test with new model
        const newActiveModel = await aiServiceManager.getActiveModel();
        console.log('🎯 New active model:', newActiveModel);
        
        // Switch back to original
        await aiServiceManager.updateModel(activeModel, {
          is_default: true
        });
        
        console.log('✅ Model switching test successful!');
      } else {
        console.log('⚠️ No alternative active model found for switching test');
      }
      
    } catch (switchError) {
      console.error('❌ Model switching test failed:', switchError.message);
    }
    
    // Close database connection
    await database.close();
    console.log('\n🎉 AI Services test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
testAIServices();
