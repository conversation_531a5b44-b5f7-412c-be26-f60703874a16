const database = require('../config/database');
const { hashPassword } = require('../utils/auth');

async function createAdminUser() {
  console.log('🔧 Creating admin user...');
  
  try {
    // Connect to database
    await database.connect();
    
    // Check if admin user already exists
    const existingAdmin = await database.get(
      'SELECT id FROM users WHERE email = ? OR role = ?',
      ['<EMAIL>', 'admin']
    );
    
    if (existingAdmin) {
      console.log('⚠️ Admin user already exists. Updating role...');
      
      // Update existing user to admin role
      await database.run(
        'UPDATE users SET role = ? WHERE id = ?',
        ['admin', existingAdmin.id]
      );
      
      console.log('✅ Existing user updated to admin role');
    } else {
      console.log('👤 Creating new admin user...');
      
      // Create new admin user
      const adminEmail = '<EMAIL>';
      const adminPassword = 'Admin123!'; // Change this in production
      const passwordHash = await hashPassword(adminPassword);
      
      const result = await database.run(
        'INSERT INTO users (email, password_hash, full_name, plan_type, email_verified, role) VALUES (?, ?, ?, ?, ?, ?)',
        [adminEmail, passwordHash, 'Admin User', 'V1_DEFAULT_ACCESS', true, 'admin']
      );
      
      console.log('✅ Admin user created successfully!');
      console.log('📧 Email:', adminEmail);
      console.log('🔑 Password:', adminPassword);
      console.log('⚠️ Please change the password after first login!');
    }
    
    // Close database connection
    await database.close();
    console.log('🎉 Admin user setup completed!');
    
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  }
}

// Run the script
createAdminUser();
