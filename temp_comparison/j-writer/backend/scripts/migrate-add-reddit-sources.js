const database = require('../config/database');

async function addRedditSourcesColumn() {
  try {
    console.log('🔄 Starting Reddit sources column migration...');
    
    await database.connect();
    
    // Check if the column already exists
    console.log('📋 Checking if reddit_sources column exists...');
    
    if (database.isPostgres) {
      // PostgreSQL: Check if column exists
      const columnExists = await database.get(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'tasks' AND column_name = 'reddit_sources'
      `);
      
      if (columnExists) {
        console.log('✅ reddit_sources column already exists in PostgreSQL');
        return;
      }
      
      console.log('🔧 Adding reddit_sources column to PostgreSQL tasks table...');
      await database.run('ALTER TABLE tasks ADD COLUMN reddit_sources TEXT');
      console.log('✅ Successfully added reddit_sources column to PostgreSQL');
      
    } else {
      // SQLite: Check if column exists
      const tableInfo = await database.all('PRAGMA table_info(tasks)');
      const columnExists = tableInfo.some(column => column.name === 'reddit_sources');
      
      if (columnExists) {
        console.log('✅ reddit_sources column already exists in SQLite');
        return;
      }
      
      console.log('🔧 Adding reddit_sources column to SQLite tasks table...');
      await database.run('ALTER TABLE tasks ADD COLUMN reddit_sources TEXT');
      console.log('✅ Successfully added reddit_sources column to SQLite');
    }
    
    console.log('🎉 Migration completed successfully!');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await database.close();
  }
}

// Run migration if called directly
if (require.main === module) {
  addRedditSourcesColumn()
    .then(() => {
      console.log('✅ Reddit sources migration completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Reddit sources migration failed:', error);
      process.exit(1);
    });
}

module.exports = { addRedditSourcesColumn };