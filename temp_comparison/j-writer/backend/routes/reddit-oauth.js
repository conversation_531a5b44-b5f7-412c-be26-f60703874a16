const express = require('express');
const axios = require('axios');
const crypto = require('crypto');
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

// Reddit OAuth configuration
const REDDIT_CLIENT_ID = process.env.REDDIT_CLIENT_ID;
const REDDIT_CLIENT_SECRET = process.env.REDDIT_CLIENT_SECRET;
const REDDIT_REDIRECT_URI = process.env.REDDIT_REDIRECT_URI;
const REDDIT_AUTH_URL = 'https://www.reddit.com/api/v1/authorize';
const REDDIT_TOKEN_URL = 'https://www.reddit.com/api/v1/access_token';
const REDDIT_API_BASE = 'https://oauth.reddit.com';

// OAuth state storage using database instead of memory for production reliability

// GET /api/reddit-oauth/auth - Initiate Reddit OAuth flow
router.get('/auth', authenticateToken, async (req, res) => {
  try {
    if (!REDDIT_CLIENT_ID || !REDDIT_CLIENT_SECRET || !REDDIT_REDIRECT_URI) {
      return res.status(500).json({
        error: 'Reddit OAuth not configured',
        message: 'Server missing Reddit OAuth configuration'
      });
    }

    // Get return URL from query parameter (frontend should provide current task URL)
    const returnUrl = req.query.returnUrl || '/dashboard';
    
    // Generate random state for CSRF protection that includes user ID and return URL
    const stateData = {
      userId: req.user.id,
      returnUrl: returnUrl,
      timestamp: Date.now()
    };
    const state = Buffer.from(JSON.stringify(stateData)).toString('base64');
    console.log('Generated OAuth state for user:', req.user.id, 'returnUrl:', returnUrl);

    // Build Reddit authorization URL
    const authUrl = new URL(REDDIT_AUTH_URL);
    authUrl.searchParams.set('client_id', REDDIT_CLIENT_ID);
    authUrl.searchParams.set('response_type', 'code');
    authUrl.searchParams.set('state', state);
    authUrl.searchParams.set('redirect_uri', REDDIT_REDIRECT_URI);
    authUrl.searchParams.set('duration', 'temporary'); // or 'permanent' for refresh tokens
    authUrl.searchParams.set('scope', 'identity read');

    res.json({
      authUrl: authUrl.toString(),
      state
    });

  } catch (error) {
    console.error('Reddit OAuth init error:', error);
    res.status(500).json({
      error: 'Failed to initiate Reddit OAuth',
      message: error.message
    });
  }
});

// GET /api/reddit-oauth/callback - Handle Reddit OAuth callback
router.get('/callback', async (req, res) => {
  try {
    const { code, state, error: oauthError } = req.query;
    console.log('Reddit OAuth callback received:', { code: !!code, state: !!state, error: oauthError });

    if (oauthError) {
      console.error('Reddit OAuth error:', oauthError);
      return res.redirect(`${process.env.FRONTEND_URL}?reddit_auth=error&message=${encodeURIComponent(oauthError)}`);
    }

    if (!code || !state) {
      console.error('Missing OAuth parameters:', { code: !!code, state: !!state });
      return res.redirect(`${process.env.FRONTEND_URL}?reddit_auth=error&message=Missing authorization code or state`);
    }

    // Verify state to prevent CSRF attacks - decode state data
    let stateData;
    try {
      stateData = JSON.parse(Buffer.from(state, 'base64').toString());
      console.log('OAuth state verification:', stateData);
      
      // Check if state is not too old (10 minutes max)
      if (Date.now() - stateData.timestamp > 10 * 60 * 1000) {
        throw new Error('State expired');
      }
    } catch (err) {
      console.error('Invalid OAuth state:', err);
      return res.redirect(`${process.env.FRONTEND_URL}/dashboard?reddit_auth=error&message=Invalid or expired state`);
    }

    // Exchange code for access token
    const auth = Buffer.from(`${REDDIT_CLIENT_ID}:${REDDIT_CLIENT_SECRET}`).toString('base64');
    
    console.log('Exchanging Reddit OAuth code for token:', {
      redirectUri: REDDIT_REDIRECT_URI,
      clientIdLength: REDDIT_CLIENT_ID?.length,
      hasSecret: !!REDDIT_CLIENT_SECRET
    });
    
    const tokenResponse = await axios.post(REDDIT_TOKEN_URL, 
      new URLSearchParams({
        grant_type: 'authorization_code',
        code,
        redirect_uri: REDDIT_REDIRECT_URI
      }), {
        headers: {
          'Authorization': `Basic ${auth}`,
          'User-Agent': 'WriterJ:1.0.0 (by /u/WriterJApp)',
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    const tokenData = tokenResponse.data;
    console.log('Reddit token response:', { 
      hasAccessToken: !!tokenData.access_token, 
      scope: tokenData.scope,
      tokenType: tokenData.token_type,
      tokenPrefix: tokenData.access_token?.substring(0, 20) + '...'
    });
    
    if (!tokenData.access_token) {
      throw new Error('No access token received from Reddit');
    }

    // Get user info from Reddit
    console.log('Getting Reddit user info with token type:', tokenData.token_type);
    const userResponse = await axios.get(`${REDDIT_API_BASE}/api/v1/me`, {
      headers: {
        'Authorization': `${tokenData.token_type || 'bearer'} ${tokenData.access_token}`,
        'User-Agent': 'WriterJ:1.0.0 (by /u/WriterJApp)'
      }
    });

    const redditUser = userResponse.data;

    // Calculate token expiration
    const expiresAt = tokenData.expires_in 
      ? new Date(Date.now() + tokenData.expires_in * 1000)
      : new Date(Date.now() + 3600 * 1000); // Default 1 hour

    // Store token in database
    console.log('Storing Reddit OAuth token for user:', stateData.userId);
    
    // Delete existing token first, then insert new one (works for both SQLite and PostgreSQL)
    await database.run(
      'DELETE FROM user_oauth_tokens WHERE user_id = ? AND provider = ?',
      [stateData.userId, 'reddit']
    );
    
    const dbResult = await database.run(
      `INSERT INTO user_oauth_tokens 
       (user_id, provider, access_token, refresh_token, expires_at, scope, reddit_username, updated_at)
       VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
      [
        stateData.userId,
        'reddit',
        tokenData.access_token,
        tokenData.refresh_token || null,
        expiresAt.toISOString(),
        tokenData.scope,
        redditUser.name
      ]
    );
    console.log('Database insert result:', dbResult);

    // Redirect to frontend with success - use stored return URL
    const frontendUrl = process.env.FRONTEND_URL || 'https://www.writer-j.com';
    const returnUrl = stateData.returnUrl || '/dashboard';
    
    // Build redirect URL properly
    const separator = returnUrl.includes('?') ? '&' : '?';
    const fullReturnUrl = `${frontendUrl}${returnUrl}${separator}reddit_auth=success&username=${encodeURIComponent(redditUser.name)}`;
    
    console.log('Reddit OAuth success - redirecting to:', fullReturnUrl);
    res.redirect(fullReturnUrl);

  } catch (error) {
    console.error('Reddit OAuth callback error:', error);
    console.error('Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      url: error.config?.url,
      method: error.config?.method
    });
    
    let errorMessage = 'Unknown OAuth error';
    if (error.response?.data?.error) {
      errorMessage = `${error.response.data.error}: ${error.response.data.error_description || 'No description'}`;
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error.message) {
      errorMessage = error.message;
    }
    
    const frontendUrl = process.env.FRONTEND_URL || 'https://www.writer-j.com';
    
    // Try to get return URL from state if available, otherwise redirect to dashboard
    let returnUrl = '/dashboard';
    if (req.query.state) {
      try {
        const stateData = JSON.parse(Buffer.from(req.query.state, 'base64').toString());
        returnUrl = stateData.returnUrl || '/dashboard';
      } catch (err) {
        console.error('Could not decode state for error redirect:', err);
      }
    }
    
    const separator = returnUrl.includes('?') ? '&' : '?';
    const fullReturnUrl = `${frontendUrl}${returnUrl}${separator}reddit_auth=error&message=${encodeURIComponent(errorMessage)}`;
    res.redirect(fullReturnUrl);
  }
});

// GET /api/reddit-oauth/status - Check Reddit OAuth status
router.get('/status', authenticateToken, async (req, res) => {
  try {
    const token = await database.get(
      'SELECT reddit_username, expires_at, scope FROM user_oauth_tokens WHERE user_id = ? AND provider = ?',
      [req.user.id, 'reddit']
    );

    if (!token) {
      return res.json({
        connected: false,
        username: null
      });
    }

    // Check if token is expired
    const isExpired = new Date(token.expires_at) <= new Date();

    res.json({
      connected: !isExpired,
      username: token.reddit_username,
      scope: token.scope,
      expiresAt: token.expires_at,
      expired: isExpired
    });

  } catch (error) {
    console.error('Reddit OAuth status error:', error);
    res.status(500).json({
      error: 'Failed to check Reddit OAuth status',
      message: error.message
    });
  }
});

// DELETE /api/reddit-oauth/disconnect - Disconnect Reddit OAuth
router.delete('/disconnect', authenticateToken, async (req, res) => {
  try {
    await database.run(
      'DELETE FROM user_oauth_tokens WHERE user_id = ? AND provider = ?',
      [req.user.id, 'reddit']
    );

    res.json({
      success: true,
      message: 'Reddit account disconnected successfully'
    });

  } catch (error) {
    console.error('Reddit OAuth disconnect error:', error);
    res.status(500).json({
      error: 'Failed to disconnect Reddit account',
      message: error.message
    });
  }
});

// Helper function to get valid access token for user
const getUserRedditToken = async (userId) => {
  const tokenData = await database.get(
    'SELECT access_token, refresh_token, expires_at FROM user_oauth_tokens WHERE user_id = ? AND provider = ?',
    [userId, 'reddit']
  );

  if (!tokenData) {
    throw new Error('No Reddit authorization found. Please connect your Reddit account.');
  }

  // Check if token is expired
  if (new Date(tokenData.expires_at) <= new Date()) {
    // TODO: Implement refresh token logic if using permanent tokens
    throw new Error('Reddit authorization expired. Please reconnect your Reddit account.');
  }

  return tokenData.access_token;
};

module.exports = {
  router,
  getUserRedditToken
};