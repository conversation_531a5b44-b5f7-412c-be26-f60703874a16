const express = require('express');
const router = express.Router();
const aiServiceManager = require('../services/aiServiceManager');

// POST /api/generate/article
router.post('/article', async (req, res) => {
  try {
    const {
      topics,
      sources,
      productInfo,
      tonality,
      length,
      format,
      authorName,
      authorBio,
      targetAudience,
      articleGoal,
      primaryKeywords,
      secondaryKeywords
    } = req.body;

    // Validate required fields
    if (!Array.isArray(topics) || topics.length === 0 || topics.length > 5) {
      return res.status(400).json({
        error: 'Topics is required and must be an array containing 1 to 5 topic strings'
      });
    }

    // Validate each topic is a non-empty string
    for (let i = 0; i < topics.length; i++) {
      if (!topics[i] || typeof topics[i] !== 'string' || topics[i].trim().length === 0) {
        return res.status(400).json({
          error: `Topic at index ${i} must be a non-empty string`
        });
      }
    }

    // Validate optional fields with defaults
    const articleData = {
      topics: topics.map(t => t.trim()), // Array of 1-5 topic strings
      sources: Array.isArray(sources) ? sources : [],
      productInfo: productInfo || {},
      tonality: tonality || 'informative',
      length: length || 'medium',
      format: format || 'markdown',
      authorName: authorName || '',
      authorBio: authorBio || '',
      targetAudience: targetAudience || '',
      articleGoal: articleGoal || '',
      primaryKeywords: Array.isArray(primaryKeywords) ? primaryKeywords : [],
      secondaryKeywords: Array.isArray(secondaryKeywords) ? secondaryKeywords : []
    };

    // Validate tonality
    const validTonalities = ['informative', 'persuasive', 'casual', 'formal', 'technical'];
    if (!validTonalities.includes(articleData.tonality)) {
      articleData.tonality = 'informative';
    }

    // Validate length
    const validLengths = ['snippet', 'short_post', 'medium_article', 'long_guide', 'pillar_module'];
    if (!validLengths.includes(articleData.length)) {
      articleData.length = 'medium_article';
    }

    // Validate format
    const validFormats = ['markdown', 'plain'];
    if (!validFormats.includes(articleData.format)) {
      articleData.format = 'markdown';
    }

    // Generate article
    const generatedArticle = await aiServiceManager.generateArticle(articleData);

    res.json({
      article: generatedArticle,
      metadata: {
        topics: articleData.topics,
        topicsCount: articleData.topics.length,
        tonality: articleData.tonality,
        length: articleData.length,
        format: articleData.format,
        sourcesUsed: articleData.sources.length,
        hasProductInfo: !!(articleData.productInfo && articleData.productInfo.name),
        hasAuthorInfo: !!(articleData.authorName || articleData.authorBio),
        primaryKeywordsCount: articleData.primaryKeywords.length,
        secondaryKeywordsCount: articleData.secondaryKeywords.length,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error in /api/generate/article:', error);
    res.status(500).json({
      error: 'Failed to generate article',
      message: error.message
    });
  }
});

module.exports = router;
