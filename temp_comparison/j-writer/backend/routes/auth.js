const express = require('express');
const { 
  hashPassword, 
  comparePassword, 
  generateToken, 
  generateSecureToken,
  sendVerificationEmail,
  sendPasswordResetEmail,
  isValidEmail,
  isValidPassword
} = require('../utils/auth');
const { authenticateToken } = require('../middleware/auth');
const database = require('../config/database');

const router = express.Router();

// POST /api/auth/register
router.post('/register', async (req, res) => {
  try {
    const { email, password, confirmPassword, fullName } = req.body;

    // Validation
    if (!email || !password || !confirmPassword) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Email, password, and confirm password are required'
      });
    }

    if (!isValidEmail(email)) {
      return res.status(400).json({
        error: 'Invalid email',
        message: 'Please provide a valid email address'
      });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({
        error: 'Password mismatch',
        message: 'Passwords do not match'
      });
    }

    if (!isValidPassword(password)) {
      return res.status(400).json({
        error: 'Weak password',
        message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
      });
    }

    // Check if user already exists
    const existingUser = await database.get(
      'SELECT id FROM users WHERE email = ?',
      [email.toLowerCase()]
    );

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'An account with this email address already exists'
      });
    }

    // Hash password and generate verification token
    const passwordHash = await hashPassword(password);
    const verificationToken = generateSecureToken();

    // Create user
    const result = await database.run(
      `INSERT INTO users (email, password_hash, full_name, verification_token) 
       VALUES (?, ?, ?, ?)`,
      [email.toLowerCase(), passwordHash, fullName || '', verificationToken]
    );

    // Send verification email (if email is configured)
    try {
      await sendVerificationEmail(email, verificationToken);
    } catch (emailError) {
      console.warn('Failed to send verification email:', emailError.message);
      // Continue with registration even if email fails
    }

    // Generate JWT token
    const token = generateToken({ userId: result.id, email: email.toLowerCase() });

    res.status(201).json({
      message: 'User registered successfully',
      token,
      user: {
        id: result.id,
        email: email.toLowerCase(),
        fullName: fullName || '',
        planType: 'V1_DEFAULT_ACCESS',
        emailVerified: false,
        role: 'user'
      },
      emailSent: true // Indicates verification email was attempted
    });

  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      error: 'Registration failed',
      message: 'An error occurred during registration'
    });
  }
});

// POST /api/auth/login
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validation
    if (!email || !password) {
      return res.status(400).json({
        error: 'Missing credentials',
        message: 'Email and password are required'
      });
    }

    // Find user
    const user = await database.get(
      'SELECT id, email, password_hash, full_name, plan_type, email_verified, role FROM users WHERE email = ?',
      [email.toLowerCase()]
    );

    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Invalid email or password'
      });
    }

    // Verify password
    const isValidPassword = await comparePassword(password, user.password_hash);
    if (!isValidPassword) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Invalid email or password'
      });
    }

    // Generate JWT token
    const token = generateToken({ userId: user.id, email: user.email });

    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        planType: user.plan_type,
        emailVerified: user.email_verified,
        role: user.role || 'user'
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      message: 'An error occurred during login'
    });
  }
});

// POST /api/auth/verify-email
router.post('/verify-email', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        error: 'Missing token',
        message: 'Verification token is required'
      });
    }

    // Find user with verification token
    const user = await database.get(
      'SELECT id, email FROM users WHERE verification_token = ?',
      [token]
    );

    if (!user) {
      return res.status(400).json({
        error: 'Invalid token',
        message: 'Invalid or expired verification token'
      });
    }

    // Update user as verified
    await database.run(
      'UPDATE users SET email_verified = TRUE, verification_token = NULL WHERE id = ?',
      [user.id]
    );

    res.json({
      message: 'Email verified successfully'
    });

  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      error: 'Verification failed',
      message: 'An error occurred during email verification'
    });
  }
});

// POST /api/auth/forgot-password
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({
        error: 'Missing email',
        message: 'Email address is required'
      });
    }

    // Find user
    const user = await database.get(
      'SELECT id, email FROM users WHERE email = ?',
      [email.toLowerCase()]
    );

    // Always return success to prevent email enumeration
    if (!user) {
      return res.json({
        message: 'If an account with that email exists, a password reset link has been sent'
      });
    }

    // Generate reset token (expires in 1 hour)
    const resetToken = generateSecureToken();
    const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

    // Save reset token
    await database.run(
      'UPDATE users SET reset_token = ?, reset_token_expires = ? WHERE id = ?',
      [resetToken, expiresAt.toISOString(), user.id]
    );

    // Send reset email
    try {
      await sendPasswordResetEmail(user.email, resetToken);
    } catch (emailError) {
      console.warn('Failed to send password reset email:', emailError.message);
    }

    res.json({
      message: 'If an account with that email exists, a password reset link has been sent'
    });

  } catch (error) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      error: 'Request failed',
      message: 'An error occurred while processing your request'
    });
  }
});

// POST /api/auth/reset-password
router.post('/reset-password', async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    // Validation
    if (!token || !password || !confirmPassword) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Token, password, and confirm password are required'
      });
    }

    if (password !== confirmPassword) {
      return res.status(400).json({
        error: 'Password mismatch',
        message: 'Passwords do not match'
      });
    }

    if (!isValidPassword(password)) {
      return res.status(400).json({
        error: 'Weak password',
        message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
      });
    }

    // Find user with valid reset token
    const user = await database.get(
      'SELECT id FROM users WHERE reset_token = ? AND reset_token_expires > ?',
      [token, new Date().toISOString()]
    );

    if (!user) {
      return res.status(400).json({
        error: 'Invalid token',
        message: 'Invalid or expired reset token'
      });
    }

    // Hash new password
    const passwordHash = await hashPassword(password);

    // Update password and clear reset token
    await database.run(
      'UPDATE users SET password_hash = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
      [passwordHash, user.id]
    );

    res.json({
      message: 'Password reset successfully'
    });

  } catch (error) {
    console.error('Password reset error:', error);
    res.status(500).json({
      error: 'Reset failed',
      message: 'An error occurred while resetting your password'
    });
  }
});

// GET /api/auth/me
router.get('/me', authenticateToken, async (req, res) => {
  try {
    // Get user details with task count
    const user = await database.get(
      'SELECT id, email, full_name, plan_type, email_verified, role, created_at FROM users WHERE id = ?',
      [req.user.id]
    );

    const taskCount = await database.get(
      'SELECT COUNT(*) as count FROM tasks WHERE user_id = ?',
      [req.user.id]
    );

    res.json({
      user: {
        id: user.id,
        email: user.email,
        fullName: user.full_name,
        planType: user.plan_type,
        emailVerified: user.email_verified,
        role: user.role || 'user',
        createdAt: user.created_at,
        taskCount: taskCount.count
      }
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      error: 'Failed to get user information',
      message: 'An error occurred while retrieving user information'
    });
  }
});

module.exports = router;
