import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import AdminRoute from './components/AdminRoute';
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import Dashboard from './components/Dashboard';
import ArticleGenerator from './components/ArticleGenerator';
import TaskList from './components/tasks/TaskList';
import StartArticle from './components/tasks/StartArticle';
import TaskEditor from './components/tasks/TaskEditor';
import Homepage from './components/pages/Homepage';
import Features from './components/pages/Features';
import Pricing from './components/pages/Pricing';
import AdminDashboard from './components/admin/AdminDashboard';

import AdminTest from './components/AdminTest';
import AdminDashboardSimple from './components/AdminDashboardSimple';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          {/* Public routes */}
          <Route path="/home" element={<Homepage />} />
          <Route path="/features" element={<Features />} />
          <Route path="/pricing" element={<Pricing />} />
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />

          {/* Protected routes */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Dashboard />
            </ProtectedRoute>
          } />

          <Route path="/generator" element={
            <ProtectedRoute>
              <ArticleGenerator />
            </ProtectedRoute>
          } />

          <Route path="/tasks" element={
            <ProtectedRoute>
              <TaskList />
            </ProtectedRoute>
          } />

          <Route path="/tasks/new" element={
            <ProtectedRoute>
              <StartArticle />
            </ProtectedRoute>
          } />

          <Route path="/tasks/:taskId/edit" element={
            <ProtectedRoute>
              <TaskEditor />
            </ProtectedRoute>
          } />

          <Route path="/tasks/:taskId" element={
            <ProtectedRoute>
              <TaskEditor />
            </ProtectedRoute>
          } />

          <Route path="/admin" element={
            <AdminRoute>
              <AdminDashboard />
            </AdminRoute>
          } />

          <Route path="/admin-test" element={
            <ProtectedRoute>
              <AdminTest />
            </ProtectedRoute>
          } />

          <Route path="/admin-simple" element={
            <ProtectedRoute>
              <AdminDashboardSimple />
            </ProtectedRoute>
          } />

          {/* Default redirect */}
          <Route path="/" element={<Navigate to="/home" replace />} />

          {/* Catch all route */}
          <Route path="*" element={<Navigate to="/home" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
