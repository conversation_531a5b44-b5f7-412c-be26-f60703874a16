import React, { useState, useCallback, useMemo, useRef } from 'react';
import { ChevronDownIcon, ChevronUpIcon, QuestionMarkCircleIcon, TagIcon, CloudIcon } from '@heroicons/react/24/outline';

const KeywordResearchPanel = ({
  researchData,
  onKeywordSelect,
  onTopicSelect,
  onAddToSelections,
  onRemoveSelection,
  selections = [],
  primaryKeywordCount = 0,
  secondaryKeywordCount = 0,
  primaryKeywordLimit = 3,
  secondaryKeywordLimit = 12
}) => {
  const [expandedSections, setExpandedSections] = useState({
    autocompleteSuggestions: true,
    relatedKeywords: true,
    wordCloud: true,
    peopleAlsoAsk: true
  });

  // Simple memoized set for selected items lookup
  const selectedItemsSet = useMemo(() => {
    const set = new Set();
    selections.forEach(selection => {
      set.add(`${selection.value}-${selection.type}`);
    });
    return set;
  }, [selections]);

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  if (!researchData) return null;

  const { autocompleteSuggestions, relatedKeywords, peopleAlsoAsk, wordCloudData, knowledgeGraph } = researchData;

  // Simple selection check using Set
  const isSelected = (value, type) => {
    return selectedItemsSet.has(`${value}-${type}`);
  };

  // Helper functions for limits
  const isPrimaryKeywordType = (type) => {
    return type === 'autocomplete' || type === 'related' || type === 'custom';
  };

  const isSecondaryKeywordType = (type) => {
    return type === 'keyTerm' || type === 'paa';
  };

  const isLimitReached = (type) => {
    if (isPrimaryKeywordType(type)) {
      return primaryKeywordCount >= primaryKeywordLimit;
    } else if (isSecondaryKeywordType(type)) {
      return secondaryKeywordCount >= secondaryKeywordLimit;
    }
    return false;
  };

  const isNearLimit = (type) => {
    if (isPrimaryKeywordType(type)) {
      return primaryKeywordCount >= primaryKeywordLimit - 1;
    } else if (isSecondaryKeywordType(type)) {
      return secondaryKeywordCount >= secondaryKeywordLimit - 2;
    }
    return false;
  };

  // 防抖处理器 - 使用useRef避免快速点击导致的状态竞争
  const pendingActionsRef = useRef(new Set());
  
  const handleItemClick = useCallback((value, type, additionalData = {}) => {
    const actionKey = `${value}-${type}`;
    
    // 如果正在处理相同的操作，直接返回
    if (pendingActionsRef.current.has(actionKey)) {
      return;
    }
    
    // 标记为正在处理
    pendingActionsRef.current.add(actionKey);
    
    const currentlySelected = isSelected(value, type);
    
    if (currentlySelected) {
      // 移除选择
      const selectionToRemove = selections.find(selection =>
        selection.value === value && selection.type === type
      );
      
      if (selectionToRemove && onRemoveSelection) {
        onRemoveSelection(selectionToRemove);
      }
    } else {
      // 添加选择
      if (onAddToSelections) {
        onAddToSelections(value, type, additionalData);
      }
    }
    
    // 延迟清除标记，确保状态更新完成
    setTimeout(() => {
      pendingActionsRef.current.delete(actionKey);
    }, 300);
  }, [isSelected, selections, onRemoveSelection, onAddToSelections]);

  // 简化按钮渲染器
  const renderKeywordButton = useCallback((item, index, type, displayValue, additionalData = {}) => {
    const selected = isSelected(displayValue, type);
    const limitReached = isLimitReached(type);
    const disabled = !selected && limitReached;

    return (
      <button
        key={index}
        onClick={() => !disabled && handleItemClick(displayValue, type, additionalData)}
        disabled={disabled}
        className={`text-left rounded-lg border transition-all duration-150 ${
          type === 'paa' ? 'w-full p-4' : 'p-3'
        } ${
          selected
            ? type === 'autocomplete' ? 'bg-blue-100 border-blue-400 text-blue-900 shadow-md' :
              type === 'related' ? 'bg-green-100 border-green-400 text-green-900 shadow-md' :
              type === 'keyTerm' ? 'bg-purple-200 text-purple-900 shadow-md border-2 border-purple-400' :
              'bg-orange-100 border-orange-400 text-orange-900 shadow-md'
            : disabled
            ? 'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed'
            : type === 'autocomplete' ? 'bg-white hover:bg-blue-50 border-blue-200 hover:border-blue-400 text-gray-900' :
              type === 'related' ? 'bg-gray-50 hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-900' :
              type === 'keyTerm' ? 'bg-purple-100 text-purple-800 hover:bg-purple-200' :
              'bg-white hover:bg-orange-50 border-orange-200 hover:border-orange-400 text-gray-900'
        }`}
      >
        {type === 'keyTerm' ? (
          <span
            className="flex items-center"
            style={{
              fontSize: `${Math.max(0.75, Math.min(1.2, (additionalData.count || 10) / 10))}rem`
            }}
          >
            {selected ? '✓' : disabled ? '🚫' : ''} {displayValue}
            <span className="ml-1 text-xs opacity-75">({additionalData.count || 0})</span>
          </span>
        ) : type === 'paa' ? (
          <div className="w-full">
            <h5 className="font-medium text-base flex items-start">
              <span className="mr-2 mt-0.5">{selected ? '✓' : disabled ? '🚫' : '❓'}</span>
              <span className="flex-1 leading-relaxed">{displayValue}</span>
            </h5>
          </div>
        ) : (
          <div className="flex items-center justify-between">
            <span className="text-sm flex items-center">
              {selected ? '✓' : disabled ? '🚫' : type === 'autocomplete' ? '🔍' : '🏷️'} {displayValue}
            </span>
            {additionalData.relevance && (
              <span className={`text-xs px-2 py-1 rounded-full ${
                additionalData.relevance === 'high' ? 'bg-green-100 text-green-800' :
                additionalData.relevance === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {additionalData.relevance}
              </span>
            )}
          </div>
        )}
      </button>
    );
  }, [isSelected, isLimitReached, handleItemClick]);

  return (
    <div className="p-6 space-y-6">

      {/* Knowledge Graph */}
      {knowledgeGraph && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">{knowledgeGraph.title}</h4>
          {knowledgeGraph.type && (
            <p className="text-sm text-blue-700 mb-2">{knowledgeGraph.type}</p>
          )}
          {knowledgeGraph.description && (
            <p className="text-sm text-blue-800">{knowledgeGraph.description}</p>
          )}
        </div>
      )}

      {/* 1. Autocomplete Suggestions - FIRST */}
      {autocompleteSuggestions && autocompleteSuggestions.length > 0 && (
        <div className="border-2 border-blue-200 rounded-lg bg-blue-50">
          <button
            onClick={() => toggleSection('autocompleteSuggestions')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-blue-100"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-blue-900 flex items-center">
                <TagIcon className="w-6 h-6 mr-3 text-blue-600" />
                🔍 Google Autocomplete Suggestions ({autocompleteSuggestions.length})
              </h4>
              <div className="text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded">
                Primary: {primaryKeywordCount}/{primaryKeywordLimit}
              </div>
            </div>
            {expandedSections.autocompleteSuggestions ? (
              <ChevronUpIcon className="w-5 h-5 text-blue-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-blue-500" />
            )}
          </button>

          {expandedSections.autocompleteSuggestions && (
            <div className="px-4 pb-4">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm text-blue-700">
                  💡 Click any suggestion to add it to your selections!
                </p>
                {isLimitReached('autocomplete') && (
                  <p className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    Primary keyword limit reached
                  </p>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {autocompleteSuggestions.map((suggestion, index) => 
                  renderKeywordButton(suggestion, index, 'autocomplete', suggestion)
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 2. Related Keywords - SECOND */}
      {relatedKeywords && relatedKeywords.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <button
            onClick={() => toggleSection('relatedKeywords')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-medium text-gray-900">🏷️ Related Keywords ({relatedKeywords.length})</h4>
              <div className="text-sm text-blue-700 bg-blue-100 px-2 py-1 rounded">
                Primary: {primaryKeywordCount}/{primaryKeywordLimit}
              </div>
            </div>
            {expandedSections.relatedKeywords ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-500" />
            )}
          </button>

          {expandedSections.relatedKeywords && (
            <div className="px-4 pb-4">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm text-gray-600">
                  💡 Click any keyword to add it to your selections!
                </p>
                {isLimitReached('related') && (
                  <p className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    Primary keyword limit reached
                  </p>
                )}
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {relatedKeywords.map((keyword, index) => 
                  renderKeywordButton(keyword, index, 'related', keyword.query, { relevance: keyword.relevance })
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 3. Word Cloud - THIRD */}
      {wordCloudData && wordCloudData.words && wordCloudData.words.length > 0 && (
        <div className="border border-gray-200 rounded-lg">
          <button
            onClick={() => toggleSection('wordCloud')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-medium text-gray-900 flex items-center">
                <CloudIcon className="w-5 h-5 mr-2 text-purple-600" />
                ☁️ Key Terms ({wordCloudData.words.length})
              </h4>
              <div className="text-sm text-gray-700 bg-gray-100 px-2 py-1 rounded">
                Secondary: {secondaryKeywordCount}/{secondaryKeywordLimit}
              </div>
            </div>
            {expandedSections.wordCloud ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-500" />
            )}
          </button>

          {expandedSections.wordCloud && (
            <div className="px-4 pb-4">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm text-gray-600">
                  💡 Click any term to add it to your selections!
                </p>
                {isLimitReached('keyTerm') && (
                  <p className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    Secondary keyword limit reached
                  </p>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {wordCloudData.words.slice(0, 30).map((wordData, index) => 
                  renderKeywordButton(wordData, index, 'keyTerm', wordData.word, { count: wordData.count })
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* 4. People Also Ask - FOURTH */}
      {peopleAlsoAsk && peopleAlsoAsk.length > 0 && (
        <div className="border-2 border-orange-200 rounded-lg bg-orange-50">
          <button
            onClick={() => toggleSection('peopleAlsoAsk')}
            className="w-full flex items-center justify-between p-4 text-left hover:bg-orange-100"
          >
            <div className="flex items-center justify-between w-full">
              <h4 className="font-bold text-lg text-orange-900 flex items-center">
                <QuestionMarkCircleIcon className="w-6 h-6 mr-3 text-orange-600" />
                🔥 Popular Questions People Ask ({peopleAlsoAsk.length})
              </h4>
              <div className="text-sm text-gray-700 bg-gray-100 px-2 py-1 rounded">
                Secondary: {secondaryKeywordCount}/{secondaryKeywordLimit}
              </div>
            </div>
            {expandedSections.peopleAlsoAsk ? (
              <ChevronUpIcon className="w-5 h-5 text-orange-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-orange-500" />
            )}
          </button>

          {expandedSections.peopleAlsoAsk && (
            <div className="px-4 pb-4">
              <div className="flex items-center justify-between mb-3">
                <p className="text-sm text-orange-700">
                  💡 Click any question to add it to your selections!
                </p>
                {isLimitReached('paa') && (
                  <p className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                    Secondary keyword limit reached
                  </p>
                )}
              </div>
              <div className="space-y-2">
                {peopleAlsoAsk.map((item, index) => 
                  renderKeywordButton(item, index, 'paa', item.question)
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default KeywordResearchPanel;