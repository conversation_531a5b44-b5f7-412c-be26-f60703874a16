import React, { useState, useEffect } from 'react';
import { 
  CogIcon, 
  DocumentTextIcon, 
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { buildApiUrl, API_CONFIG } from '../../config/api';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('models');
  const [models, setModels] = useState([]);
  const [prompts, setPrompts] = useState([]);
  const [stats, setStats] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [testingModel, setTestingModel] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [exportingTasks, setExportingTasks] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('token');
      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      };

      const [modelsRes, promptsRes, statsRes] = await Promise.all([
        fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.MODELS), { headers }),
        fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.PROMPTS), { headers }),
        fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.STATS), { headers })
      ]);

      if (!modelsRes.ok || !promptsRes.ok || !statsRes.ok) {
        throw new Error('Failed to load admin data');
      }

      const [modelsData, promptsData, statsData] = await Promise.all([
        modelsRes.json(),
        promptsRes.json(),
        statsRes.json()
      ]);

      setModels(modelsData.models || []);
      setPrompts(promptsData.prompts || []);
      setStats(statsData.stats || {});
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const updateModel = async (modelName, updates) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.UPDATE_MODEL(modelName)), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error('Failed to update model');
      }

      await loadData(); // Reload data
    } catch (err) {
      setError(err.message);
    }
  };

  const updatePrompt = async (id, updates) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.UPDATE_PROMPT(id)), {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        throw new Error('Failed to update prompt');
      }

      setSuccessMessage('Prompt updated successfully and applied immediately!');
      setTimeout(() => setSuccessMessage(null), 5000); // Clear after 5 seconds
      await loadData(); // Reload data
    } catch (err) {
      setError(err.message);
    }
  };

  const testModel = async (modelName) => {
    setTestingModel(modelName);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.TEST_MODEL(modelName)), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      const result = await response.json();

      if (result.success) {
        alert(`✅ ${result.message}`);
      } else {
        alert(`❌ ${result.message}`);
      }
    } catch (err) {
      alert(`❌ Test failed: ${err.message}`);
    } finally {
      setTestingModel(null);
    }
  };

  const exportTasks = async () => {
    setExportingTasks(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(buildApiUrl('/api/admin/tasks/export'), {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('导出失败');
      }

      // Create blob from response
      const blob = await response.blob();
      
      // Get filename from response headers or create default
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'tasks_export.csv';
      if (contentDisposition) {
        const matches = contentDisposition.match(/filename="([^"]+)"/);
        if (matches) {
          filename = matches[1];
        }
      }

      // Create download link and trigger download
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      setSuccessMessage('Tasks数据导出成功！');
      setTimeout(() => setSuccessMessage(null), 5000);
    } catch (err) {
      setError(`导出失败: ${err.message}`);
    } finally {
      setExportingTasks(false);
    }
  };



  const tabs = [
    { id: 'models', name: 'AI Models', icon: CogIcon },
    { id: 'prompts', name: 'Prompts', icon: DocumentTextIcon },
    { id: 'stats', name: 'Statistics', icon: ChartBarIcon }
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
          <p className="mt-2 text-gray-600">Manage AI models, prompts, and system configuration</p>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="mt-1 text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="mb-6 bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-green-800">Success</h3>
                <p className="mt-1 text-sm text-green-700">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  } whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex items-center`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'models' && (
          <ModelsTab 
            models={models} 
            onUpdateModel={updateModel} 
            onTestModel={testModel}
            testingModel={testingModel}
          />
        )}
        
        {activeTab === 'prompts' && (
          <PromptsTab
            prompts={prompts}
            onUpdatePrompt={updatePrompt}
          />
        )}

        {activeTab === 'stats' && (
          <StatsTab 
            stats={stats} 
            onExportTasks={exportTasks}
            exportingTasks={exportingTasks}
          />
        )}
      </div>
    </div>
  );
};

// Models Tab Component
const ModelsTab = ({ models, onUpdateModel, onTestModel, testingModel }) => {
  const [editingModel, setEditingModel] = useState(null);
  const [formData, setFormData] = useState({});

  const startEdit = (model) => {
    setEditingModel(model.model_name);
    setFormData({
      api_key: '',
      model_version: model.model_version || '',
      is_active: model.is_active,
      is_default: model.is_default
    });
  };

  const saveModel = async () => {
    await onUpdateModel(editingModel, formData);
    setEditingModel(null);
    setFormData({});
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">AI Model Configuration</h2>
        <p className="mt-1 text-sm text-gray-600">
          Configure API keys, model versions, and settings for each AI provider.
          API keys are stored securely in the database and can be updated here.
        </p>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Model
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Provider
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Version
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                API Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {models.map((model) => (
              <tr key={model.model_name}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="text-sm font-medium text-gray-900">
                      {model.model_name}
                    </div>
                    {model.is_default && (
                      <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        Default
                      </span>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {model.provider}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {editingModel === model.model_name ? (
                    <input
                      type="text"
                      value={formData.model_version}
                      onChange={(e) => setFormData({...formData, model_version: e.target.value})}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    />
                  ) : (
                    model.model_version || 'Not set'
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {editingModel === model.model_name ? (
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.is_active}
                          onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                          className="mr-2"
                        />
                        Active
                      </label>
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={formData.is_default}
                          onChange={(e) => setFormData({...formData, is_default: e.target.checked})}
                          className="mr-2"
                        />
                        Default
                      </label>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      {model.is_active ? (
                        <CheckCircleIcon className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircleIcon className="h-5 w-5 text-red-500" />
                      )}
                      <span className={`text-sm ${model.is_active ? 'text-green-700' : 'text-red-700'}`}>
                        {model.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {editingModel === model.model_name ? (
                    <input
                      type="password"
                      placeholder="Enter new API key"
                      value={formData.api_key}
                      onChange={(e) => setFormData({...formData, api_key: e.target.value})}
                      className="border border-gray-300 rounded px-2 py-1 text-sm w-full"
                    />
                  ) : (
                    <span className={model.has_api_key ? 'text-green-600' : 'text-red-600'}>
                      {model.has_api_key ? '✓ Configured' : '✗ Missing'}
                    </span>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  {editingModel === model.model_name ? (
                    <>
                      <button
                        onClick={saveModel}
                        className="text-green-600 hover:text-green-900"
                      >
                        Save
                      </button>
                      <button
                        onClick={() => setEditingModel(null)}
                        className="text-gray-600 hover:text-gray-900"
                      >
                        Cancel
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={() => startEdit(model)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => onTestModel(model.model_name)}
                        disabled={testingModel === model.model_name || !model.has_api_key}
                        className="text-purple-600 hover:text-purple-900 disabled:text-gray-400"
                      >
                        {testingModel === model.model_name ? 'Testing...' : 'Test'}
                      </button>
                    </>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

// Prompts Tab Component
const PromptsTab = ({ prompts, onUpdatePrompt }) => {
  const [editingPrompt, setEditingPrompt] = useState(null);
  const [formData, setFormData] = useState({});

  const startEdit = (prompt) => {
    setEditingPrompt(prompt.id);
    setFormData({
      template_name: prompt.template_name,
      prompt_content: prompt.prompt_content,
      is_active: prompt.is_active
    });
  };

  const savePrompt = async () => {
    await onUpdatePrompt(editingPrompt, formData);
    setEditingPrompt(null);
    setFormData({});
  };

  const groupedPrompts = prompts.reduce((acc, prompt) => {
    const key = `${prompt.model_name}-${prompt.template_type}`;
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(prompt);
    return acc;
  }, {});

  const getPromptTypeLabel = (templateType) => {
    switch (templateType) {
      case 'clustered_topics':
        return 'Clustered Topics (Step 1)';
      case 'article_generation':
        return 'Article Generation (Step 7)';
      case 'topic_generation':
        return 'Topic Generation (Legacy)';
      case 'keyword_selection':
        return 'Keyword Selection (Legacy)';
      default:
        return templateType;
    }
  };

  const getPromptTypeDescription = (templateType) => {
    switch (templateType) {
      case 'clustered_topics':
        return 'Used for generating organized topic clusters in Step 1 (Step0KeywordResearch component)';
      case 'article_generation':
        return 'Used for generating complete articles with SEO optimization in Step 7 (Step5Generation component)';
      case 'topic_generation':
        return 'Legacy prompt type - no longer actively used';
      case 'keyword_selection':
        return 'Legacy prompt type - no longer actively used';
      default:
        return 'Custom prompt type';
    }
  };

  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold text-gray-900">Prompt Templates</h2>

      {Object.entries(groupedPrompts).map(([key, promptGroup]) => {
        const [modelName, templateType] = key.split('-');
        return (
          <div key={key} className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">
                {modelName.toUpperCase()} - {getPromptTypeLabel(templateType)}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {getPromptTypeDescription(templateType)}
              </p>
            </div>

            <div className="divide-y divide-gray-200">
              {promptGroup.map((prompt) => (
                <div key={prompt.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h4 className="text-sm font-medium text-gray-900">
                          {editingPrompt === prompt.id ? (
                            <input
                              type="text"
                              value={formData.template_name}
                              onChange={(e) => setFormData({...formData, template_name: e.target.value})}
                              className="border border-gray-300 rounded px-2 py-1 text-sm w-64"
                            />
                          ) : (
                            prompt.template_name
                          )}
                        </h4>
                        {editingPrompt === prompt.id ? (
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={formData.is_active}
                              onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                              className="mr-1"
                            />
                            <span className="text-xs text-gray-600">Active</span>
                          </label>
                        ) : (
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            prompt.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {prompt.is_active ? 'Active' : 'Inactive'}
                          </span>
                        )}
                      </div>

                      <div className="mt-2">
                        {editingPrompt === prompt.id ? (
                          <textarea
                            value={formData.prompt_content}
                            onChange={(e) => setFormData({...formData, prompt_content: e.target.value})}
                            rows={10}
                            className="w-full border border-gray-300 rounded px-3 py-2 text-sm font-mono"
                            placeholder="Enter prompt content..."
                          />
                        ) : (
                          <div className="bg-gray-50 rounded p-3 max-h-40 overflow-y-auto">
                            <pre className="text-xs text-gray-700 whitespace-pre-wrap font-mono">
                              {prompt.prompt_content.substring(0, 500)}
                              {prompt.prompt_content.length > 500 && '...'}
                            </pre>
                          </div>
                        )}
                      </div>

                      <div className="mt-3 text-xs text-gray-500">
                        Last updated: {new Date(prompt.updated_at).toLocaleString()}
                      </div>
                    </div>

                    <div className="ml-4 flex-shrink-0">
                      {editingPrompt === prompt.id ? (
                        <div className="space-x-2">
                          <button
                            onClick={savePrompt}
                            className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-green-600 hover:bg-green-700"
                          >
                            Save
                          </button>
                          <button
                            onClick={() => setEditingPrompt(null)}
                            className="inline-flex items-center px-3 py-1 border border-gray-300 text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                          >
                            Cancel
                          </button>
                        </div>
                      ) : (
                        <button
                          onClick={() => startEdit(prompt)}
                          className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200"
                        >
                          Edit
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        );
      })}

      {prompts.length === 0 && (
        <div className="bg-white p-6 rounded-lg shadow text-center">
          <p className="text-gray-600">No prompt templates found.</p>
        </div>
      )}
    </div>
  );
};

// Stats Tab Component
const StatsTab = ({ stats, onExportTasks, exportingTasks }) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">System Statistics</h2>
        <button
          onClick={onExportTasks}
          disabled={exportingTasks}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
          {exportingTasks ? '导出中...' : '导出Tasks数据'}
        </button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-blue-600">{stats.totalUsers || 0}</div>
          <div className="text-sm text-gray-600">Total Users</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{stats.totalTasks || 0}</div>
          <div className="text-sm text-gray-600">Total Tasks</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-purple-600">{stats.activeModels || 0}</div>
          <div className="text-sm text-gray-600">Active Models</div>
        </div>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">{stats.totalPrompts || 0}</div>
          <div className="text-sm text-gray-600">Prompt Templates</div>
        </div>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <h3 className="text-lg font-medium text-gray-900 mb-4">数据导出</h3>
        <div className="space-y-4">
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="font-medium text-gray-900">Tasks完整数据导出</h4>
            <p className="text-sm text-gray-600 mt-1">
              导出tasks表的完整原始数据，包括所有JSON字段的具体内容，如关键词列表、生成的话题建议、完整文章内容等，用于深度质量评估和内容分析。
            </p>
            <p className="text-xs text-gray-500 mt-2">
              导出格式: CSV | 包含字段: 任务基础信息、完整JSON数据（关键词、话题建议、文章内容等）、用户信息、时间戳
            </p>
            <p className="text-xs text-orange-600 mt-1">
              注意: 导出文件可能较大，包含完整的文章文本和JSON数据
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
