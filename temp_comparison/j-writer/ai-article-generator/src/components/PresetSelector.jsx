import React, { useState, useEffect } from 'react';
import { ChevronDownIcon, BookmarkIcon, PlusIcon } from '@heroicons/react/24/outline';
import { presetService } from '../config/api';

const PresetSelector = ({ 
  presetType, // 'author' or 'product'
  onPresetSelect, 
  onSavePreset,
  currentData,
  className = ""
}) => {
  const [presets, setPresets] = useState([]);
  const [selectedPresetId, setSelectedPresetId] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  // Load presets when component mounts or presetType changes
  useEffect(() => {
    loadPresets();
  }, [presetType]);

  const loadPresets = async () => {
    try {
      setIsLoading(true);
      setError('');
      const fetchedPresets = await presetService.getPresets(presetType);
      setPresets(fetchedPresets);
    } catch (err) {
      console.error('Error loading presets:', err);
      setError('Failed to load presets');
    } finally {
      setIsLoading(false);
    }
  };

  const handlePresetSelect = async (presetId) => {
    if (!presetId) {
      setSelectedPresetId('');
      setIsDropdownOpen(false);
      return;
    }

    try {
      setIsLoading(true);
      const preset = await presetService.getPreset(presetId);
      setSelectedPresetId(presetId);
      setIsDropdownOpen(false);
      onPresetSelect(preset.presetData);
    } catch (err) {
      console.error('Error loading preset:', err);
      setError('Failed to load preset');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSavePreset = () => {
    onSavePreset(currentData);
  };

  // Check if current data has any content to save
  const hasDataToSave = () => {
    if (presetType === 'product') {
      return currentData?.name || currentData?.description || currentData?.link ||
             (currentData?.features && currentData.features.length > 0);
    } else if (presetType === 'author') {
      return currentData?.authorName || currentData?.authorBio ||
             currentData?.targetAudience || currentData?.articleGoal;
    }
    return false;
  };

  // Get a summary of current data for display
  const getDataSummary = () => {
    if (presetType === 'product') {
      const parts = [];
      if (currentData?.name) parts.push(`Name: ${currentData.name}`);
      if (currentData?.description) parts.push(`Description: ${currentData.description.substring(0, 30)}...`);
      if (currentData?.features?.length) parts.push(`${currentData.features.length} feature(s)`);
      return parts.join(', ') || 'No data entered';
    } else if (presetType === 'author') {
      const parts = [];
      if (currentData?.authorName) parts.push(`Author: ${currentData.authorName}`);
      if (currentData?.authorBio) parts.push(`Bio: ${currentData.authorBio.substring(0, 30)}...`);
      if (currentData?.targetAudience) parts.push(`Audience: ${currentData.targetAudience}`);
      return parts.join(', ') || 'No data entered';
    }
    return 'No data entered';
  };

  const selectedPreset = presets.find(p => p.id === selectedPresetId);

  return (
    <div className={`bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <BookmarkIcon className="w-5 h-5 text-indigo-600" />
          <h4 className="text-lg font-medium text-indigo-900">
            {presetType === 'product' ? 'Product' : 'Author'} Profiles
          </h4>
        </div>
        
        {hasDataToSave() && (
          <button
            onClick={handleSavePreset}
            className="flex items-center space-x-1 px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors"
          >
            <PlusIcon className="w-4 h-4" />
            <span>Save as Profile</span>
          </button>
        )}
      </div>

      {error && (
        <div className="mb-3 p-2 bg-red-100 border border-red-300 text-red-700 text-sm rounded-md">
          {error}
        </div>
      )}

      <div className="space-y-3">
        <p className="text-sm text-indigo-700">
          Load from a saved {presetType} profile or save your current information for future use.
        </p>

        {/* Preset Dropdown */}
        <div className="relative">
          <button
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            disabled={isLoading || presets.length === 0}
            className="w-full flex items-center justify-between px-3 py-2 bg-white border border-indigo-300 rounded-md text-left focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
          >
            <span className="text-gray-900">
              {isLoading ? 'Loading...' : 
               selectedPreset ? selectedPreset.presetName : 
               presets.length === 0 ? `No ${presetType} profiles saved` : 
               `Select a ${presetType} profile`}
            </span>
            <ChevronDownIcon className={`w-4 h-4 text-gray-500 transition-transform ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </button>

          {isDropdownOpen && presets.length > 0 && (
            <div className="absolute z-10 w-full mt-1 bg-white border border-indigo-300 rounded-md shadow-lg max-h-60 overflow-auto">
              <button
                onClick={() => handlePresetSelect('')}
                className="w-full px-3 py-2 text-left hover:bg-indigo-50 text-gray-600 border-b border-gray-100"
              >
                Clear selection
              </button>
              {presets.map((preset) => (
                <button
                  key={preset.id}
                  onClick={() => handlePresetSelect(preset.id)}
                  className={`w-full px-3 py-2 text-left hover:bg-indigo-50 ${
                    selectedPresetId === preset.id ? 'bg-indigo-100 text-indigo-900' : 'text-gray-900'
                  }`}
                >
                  <div className="font-medium">{preset.presetName}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    Created {new Date(preset.createdAt).toLocaleDateString()}
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Selected Preset Info */}
        {selectedPreset && (
          <div className="bg-white border border-indigo-200 rounded-md p-3">
            <div className="text-sm">
              <div className="font-medium text-indigo-900 mb-1">
                {selectedPreset.presetName}
              </div>
              <div className="text-indigo-700 text-xs">
                Last updated: {new Date(selectedPreset.updatedAt).toLocaleDateString()}
              </div>
            </div>
          </div>
        )}

        {/* No Presets Message */}
        {!isLoading && presets.length === 0 && (
          <div className="text-center py-4">
            <BookmarkIcon className="w-8 h-8 text-indigo-300 mx-auto mb-2" />
            <p className="text-sm text-indigo-600 mb-2">
              No {presetType} profiles saved yet
            </p>
            <p className="text-xs text-indigo-500">
              Fill out the form below and click "Save as Profile" to create your first preset
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PresetSelector;
