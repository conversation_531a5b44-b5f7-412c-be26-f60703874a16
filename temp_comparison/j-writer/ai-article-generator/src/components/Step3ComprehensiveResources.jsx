import React, { useState } from 'react';
import { PlusIcon, XMarkIcon, LinkIcon, DocumentTextIcon, GlobeAltIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';
import RedditInsightAssistant from './RedditInsightAssistant';

const Step3ComprehensiveResources = ({ data, updateData }) => {
  const [inputValues, setInputValues] = useState({
    url: '',
    text: ''
  });
  const [isExtracting, setIsExtracting] = useState(false);

  // Get comprehensive resources (all types in one array)
  const resources = data.comprehensiveResources || [];
  const redditSources = data.redditSources || [];
  const selectedTopics = data.selectedTopics || [];

  // Add URL resource
  const addUrlResource = async () => {
    const url = inputValues.url.trim();
    if (!url) return;

    setIsExtracting(true);
    try {
      const extractedData = await apiCall(API_CONFIG.ENDPOINTS.SOURCES_EXTRACT, {
        method: 'POST',
        body: JSON.stringify({ url }),
      });

      const newResource = {
        id: `url-${Date.now()}`,
        type: 'url',
        url,
        title: extractedData.title,
        content: extractedData.content,
        description: extractedData.description,
        wordCount: extractedData.wordCount,
        addedAt: new Date().toISOString()
      };

      updateData({
        comprehensiveResources: [...resources, newResource]
      });

      setInputValues(prev => ({ ...prev, url: '' }));
    } catch (error) {
      console.error('Error extracting URL content:', error);
      alert('Failed to extract content from URL. Please check the URL and try again.');
    } finally {
      setIsExtracting(false);
    }
  };

  // Add text block resource
  const addTextResource = () => {
    const text = inputValues.text.trim();
    if (!text) return;

    const wordCount = text.split(/\s+/).length;
    const newResource = {
      id: `text-${Date.now()}`,
      type: 'text',
      title: 'Custom Text Block',
      content: text,
      wordCount: wordCount,
      addedAt: new Date().toISOString()
    };

    updateData({
      comprehensiveResources: [...resources, newResource]
    });

    setInputValues(prev => ({ ...prev, text: '' }));
  };

  // Remove resource
  const removeResource = (resourceId) => {
    const updatedResources = resources.filter(resource => resource.id !== resourceId);
    updateData({
      comprehensiveResources: updatedResources
    });
  };

  // Handle Reddit post use
  const handleRedditPostUse = (postData) => {
    console.log('🎯 handleRedditPostUse called with:', postData);
    
    try {
      const currentRedditSources = redditSources || [];
      
      // Check if this post is already selected
      const existingIndex = currentRedditSources.findIndex(source => 
        source.url === postData.source.url
      );
      
      if (existingIndex !== -1) {
        // Post already selected - remove it (toggle off)
        console.log('🎯 Removing existing Reddit post');
        const updatedSources = currentRedditSources.filter((_, index) => index !== existingIndex);
        
        updateData({
          redditSources: updatedSources
        });
      } else {
        // Check limit (max 3 posts)
        if (currentRedditSources.length >= 3) {
          alert('Maximum 3 Reddit posts can be selected. Please remove one first.');
          return;
        }
        
        // Add new post
        console.log('🎯 Adding Reddit post to redditSources (limit: 3)');
        const updatedSources = [...currentRedditSources, postData.source];
        
        updateData({
          redditSources: updatedSources
        });
      }
    } catch (error) {
      console.error('Error handling Reddit post:', error);
      alert('Failed to manage Reddit post. Please try again.');
    }
  };

  // Get resource icon
  const getResourceIcon = (type) => {
    switch (type) {
      case 'url':
        return <LinkIcon className="w-5 h-5" />;
      case 'text':
        return <DocumentTextIcon className="w-5 h-5" />;
      case 'reddit':
        return (
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v2H5a3 3 0 000 6h1v3a1 1 0 001.804.596l10-2A1 1 0 0018 14V3z" />
          </svg>
        );
      default:
        return <GlobeAltIcon className="w-5 h-5" />;
    }
  };

  // Get resource type label
  const getResourceTypeLabel = (type) => {
    switch (type) {
      case 'url':
        return 'URL Source';
      case 'text':
        return 'Text Block';
      case 'reddit':
        return 'Reddit Post';
      default:
        return 'Resource';
    }
  };

  // Get resource color scheme
  const getResourceColors = (type) => {
    switch (type) {
      case 'url':
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          text: 'text-blue-800',
          icon: 'text-blue-600'
        };
      case 'text':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          text: 'text-green-800',
          icon: 'text-green-600'
        };
      case 'reddit':
        return {
          bg: 'bg-orange-50',
          border: 'border-orange-200',
          text: 'text-orange-800',
          icon: 'text-orange-600'
        };
      default:
        return {
          bg: 'bg-gray-50',
          border: 'border-gray-200',
          text: 'text-gray-800',
          icon: 'text-gray-600'
        };
    }
  };

  const totalResources = resources.length + redditSources.length;

  return (
    <div className="space-y-8">
      {/* Step Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Gather Supporting Resources</h2>
        <p className="text-lg text-gray-600">
          <span className="font-semibold text-blue-600">Step 3 of 7:</span> Add URLs, text content, and Reddit insights to strengthen your article
        </p>
      </div>

      {/* Selected Topics Display */}
      {selectedTopics.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
          <h3 className="text-lg font-semibold text-blue-900 mb-3 flex items-center">
            <span className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm mr-2">✓</span>
            Selected Article Topics
          </h3>
          <div className="grid gap-3">
            {selectedTopics.map((topic, index) => (
              <div key={index} className="bg-white border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-1">
                  {topic.edited || topic.original || topic}
                </h4>
                {(topic.edited && topic.original && topic.edited !== topic.original) && (
                  <p className="text-sm text-gray-600">
                    <span className="italic">Original:</span> {topic.original}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Resource Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-green-600">{resources.length}</div>
          <div className="text-sm text-green-700">Added Resources</div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">{redditSources.length}</div>
          <div className="text-sm text-orange-700">Reddit Insights</div>
        </div>
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{totalResources}</div>
          <div className="text-sm text-blue-700">Total Sources</div>
        </div>
      </div>

      {/* Add Resources Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        {/* Add URL Resource Card */}
        <div className="bg-white border-2 border-blue-200 rounded-xl p-6 hover:border-blue-300 transition-colors">
          <div className="flex items-start space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <LinkIcon className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Add URL Resource</h3>
              <p className="text-sm text-gray-600">
                Extract content from articles, research papers, or websites
              </p>
            </div>
          </div>
          
          <div className="space-y-3">
            <input
              type="url"
              value={inputValues.url}
              onChange={(e) => setInputValues(prev => ({ ...prev, url: e.target.value }))}
              placeholder="https://example.com/article"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              disabled={isExtracting}
            />
            <button
              onClick={addUrlResource}
              disabled={!inputValues.url.trim() || isExtracting}
              className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center space-x-2"
            >
              {isExtracting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Extracting...</span>
                </>
              ) : (
                <>
                  <PlusIcon className="w-4 h-4" />
                  <span>Add URL</span>
                </>
              )}
            </button>
          </div>
        </div>

        {/* Add Text Block Card */}
        <div className="bg-white border-2 border-green-200 rounded-xl p-6 hover:border-green-300 transition-colors">
          <div className="flex items-start space-x-3 mb-4">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <DocumentTextIcon className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">Add Text Content</h3>
              <p className="text-sm text-gray-600">
                Paste or type relevant content, quotes, or research data
              </p>
            </div>
          </div>
          
          <div className="space-y-3">
            <textarea
              value={inputValues.text}
              onChange={(e) => setInputValues(prev => ({ ...prev, text: e.target.value }))}
              placeholder="Paste your content here..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
            />
            <button
              onClick={addTextResource}
              disabled={!inputValues.text.trim()}
              className="w-full px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center justify-center space-x-2"
            >
              <PlusIcon className="w-4 h-4" />
              <span>Add Text</span>
            </button>
          </div>
        </div>
      </div>



      {/* Added Resources Display */}
      {resources.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-xl font-semibold text-gray-900">📚 Your Resources</h3>
            <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-sm font-medium">
              {resources.length}
            </span>
          </div>
          <div className="grid gap-4">
            {resources.map((resource) => {
              const colors = getResourceColors(resource.type);
              return (
                <div key={resource.id} className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className={`w-10 h-10 ${colors.bg} rounded-lg flex items-center justify-center flex-shrink-0`}>
                        <div className={colors.icon}>
                          {getResourceIcon(resource.type)}
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium text-gray-900 truncate">
                            {resource.title}
                          </h4>
                          <span className={`px-2 py-1 ${colors.bg} ${colors.text} text-xs rounded-full font-medium`}>
                            {getResourceTypeLabel(resource.type)}
                          </span>
                        </div>
                        
                        {resource.type === 'url' && (
                          <p className="text-sm text-blue-600 hover:text-blue-800 mb-2 break-all">
                            <a href={resource.url} target="_blank" rel="noopener noreferrer" className="hover:underline">
                              {resource.url}
                            </a>
                          </p>
                        )}
                        
                        {resource.description && (
                          <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                            {resource.description}
                          </p>
                        )}
                        
                        <div className="text-xs text-gray-500 mb-3">
                          {resource.wordCount} words • Added {new Date(resource.addedAt).toLocaleDateString()}
                        </div>
                        
                        {/* Content Preview */}
                        <div className={`${colors.bg} rounded-lg p-3`}>
                          <p className="text-xs text-gray-700 line-clamp-3">
                            {resource.content.substring(0, 200)}
                            {resource.content.length > 200 && '...'}
                          </p>
                        </div>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => removeResource(resource.id)}
                      className="ml-4 w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                      title="Remove resource"
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* No Resources State */}
      {resources.length === 0 && (
        <div className="text-center py-12 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border-2 border-dashed border-gray-300">
          <div className="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 shadow-sm">
            <span className="text-3xl">📚</span>
          </div>
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Ready to Add Resources</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Use the cards above to add URLs, text content, or search Reddit for insights. 
            Resources are optional but help create more comprehensive articles.
          </p>
        </div>
      )}

      {/* Reddit Insights Display */}
      {redditSources.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <h3 className="text-xl font-semibold text-gray-900">
              <svg className="w-6 h-6 inline mr-2 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v2H5a3 3 0 000 6h1v3a1 1 0 001.804.596l10-2A1 1 0 0018 14V3z" />
              </svg>
              Reddit Insights
            </h3>
            <span className="bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-sm font-medium">
              {redditSources.length}
            </span>
          </div>
          <p className="text-gray-600 mb-4">
            Real user discussions and community insights you've collected
          </p>
          <div className="grid gap-4">
            {redditSources.map((source, index) => {
              const colors = getResourceColors('reddit');
              return (
                <div key={index} className="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start space-x-3">
                    <div className={`w-10 h-10 ${colors.bg} rounded-lg flex items-center justify-center flex-shrink-0`}>
                      <div className={colors.icon}>
                        {getResourceIcon('reddit')}
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-2">
                        <h4 className="font-medium text-gray-900">
                          {source.title || 'Reddit Discussion'}
                        </h4>
                        <span className={`px-2 py-1 ${colors.bg} ${colors.text} text-xs rounded-full font-medium`}>
                          Reddit Post
                        </span>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">
                        r/{source.subreddit} • {source.upvotes} upvotes • {source.comments} comments
                      </p>
                      
                      <div className="text-xs text-gray-500 mb-3">
                        Community insights and real user discussions
                      </div>
                      
                      {/* Content Preview */}
                      <div className={`${colors.bg} rounded-lg p-3`}>
                        <p className="text-xs text-gray-700 line-clamp-3">
                          {source.content ? source.content.substring(0, 200) : 'Real user discussions and insights from Reddit community'}
                          {source.content && source.content.length > 200 && '...'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Reddit Search Assistant */}
      <div className="bg-white border border-orange-200 rounded-xl p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
              <path d="M18 3a1 1 0 00-1.196-.98l-10 2A1 1 0 006 5v2H5a3 3 0 000 6h1v3a1 1 0 001.804.596l10-2A1 1 0 0018 14V3z" />
            </svg>
          </div>
          <div>
            <h3 className="font-semibold text-gray-900">Search Reddit for Insights</h3>
            <p className="text-sm text-gray-600">
              Find real user discussions and community insights related to your topics
            </p>
          </div>
        </div>
        
        <RedditInsightAssistant
          onUsePost={handleRedditPostUse}
          currentKeyword={data.keywords && data.keywords.length > 0 ? data.keywords[0] : ''}
          selectedPosts={redditSources}
        />
      </div>

      {/* AI Integration Info */}
      <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-xl p-6">
        <div className="flex items-start space-x-3">
          <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white text-sm font-bold">AI</span>
          </div>
          <div>
            <h3 className="font-medium text-purple-900 mb-2">Intelligent Resource Integration</h3>
            <p className="text-sm text-purple-700">
              Our AI will analyze each resource for relevance, quality, and usefulness. Resources will be automatically integrated
              into appropriate sections of your article, ensuring natural flow. Don't worry about quality - poor or irrelevant 
              resources will be excluded rather than hurt your content.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step3ComprehensiveResources;