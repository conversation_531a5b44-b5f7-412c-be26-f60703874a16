import React, { useState } from 'react';
import { PlusIcon, XMarkIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';
import KeywordResearchPanel from './KeywordResearchPanel';
import SelectionManager from './SelectionManager';

const Step1Ideation = ({ data, updateData, onNext }) => {
  const [keywordInput, setKeywordInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [keywordResearchData, setKeywordResearchData] = useState(null);
  const [isResearching, setIsResearching] = useState(false);
  const [selections, setSelections] = useState([]);
  const [currentResearchKeyword, setCurrentResearchKeyword] = useState('');
  const [hasResearched, setHasResearched] = useState(false);
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);

  const addKeyword = () => {
    if (keywordInput.trim() && !data.keywords.includes(keywordInput.trim())) {
      updateData({
        keywords: [...data.keywords, keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword) => {
    updateData({
      keywords: data.keywords.filter(k => k !== keyword)
    });
  };

  const generateTopicSuggestions = async () => {
    if (data.keywords.length === 0) {
      alert('Please add at least one keyword first.');
      return;
    }

    setIsLoading(true);
    try {
      const suggestions = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_SUGGESTIONS, {
        method: 'POST',
        body: JSON.stringify({ keywords: data.keywords }),
      });

      updateData({ topicSuggestions: suggestions });
    } catch (error) {
      console.error('Error generating suggestions:', error);
      alert('Failed to generate topic suggestions. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const selectTopic = (topic) => {
    updateData({ selectedTopic: topic });
  };

  const handleKeywordSelect = (keyword) => {
    if (keyword.trim() && !data.keywords.includes(keyword.trim())) {
      updateData({
        keywords: [...data.keywords, keyword.trim()]
      });
      setKeywordInput('');
    }
  };

  const performKeywordResearch = async (keyword) => {
    if (!keyword || keyword.trim().length === 0) {
      alert('Please enter a keyword for research.');
      return;
    }

    setIsResearching(true);
    setCurrentResearchKeyword(keyword.trim());
    try {
      const researchData = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_KEYWORD_RESEARCH, {
        method: 'POST',
        body: JSON.stringify({ keyword: keyword.trim() }),
      });

      setKeywordResearchData(researchData);
      setHasResearched(true); // Show the floating box after research
    } catch (error) {
      console.error('Error performing keyword research:', error);
      alert('Failed to perform keyword research. Please try again.');
    } finally {
      setIsResearching(false);
    }
  };

  const handleResearchKeyword = (keyword) => {
    performKeywordResearch(keyword);
  };

  const handleKeywordFromResearch = (keyword) => {
    if (keyword.trim() && !data.keywords.includes(keyword.trim())) {
      updateData({
        keywords: [...data.keywords, keyword.trim()]
      });
    }
  };

  const handleTopicFromResearch = (topic) => {
    updateData({ selectedTopic: topic });
  };

  // Selection management functions
  const addToSelections = (item, type, additionalData = {}) => {
    const newSelection = {
      id: `${type}-${Date.now()}-${Math.random()}`,
      type: type,
      value: item,
      ...additionalData
    };

    // Check if item already exists
    const exists = selections.some(selection =>
      selection.type === type && selection.value === item
    );

    if (!exists) {
      setSelections(prev => [...prev, newSelection]);
    }
  };

  const removeFromSelections = (selectionToRemove) => {
    setSelections(prev => prev.filter(selection => {
      // If both have IDs, use ID comparison
      if (selection.id && selectionToRemove.id) {
        return selection.id !== selectionToRemove.id;
      }
      // Fallback to value and type comparison
      return !(selection.value === selectionToRemove.value && selection.type === selectionToRemove.type);
    }));
  };

  const clearAllSelections = () => {
    setSelections([]);
  };

  const useSelectionsAsKeywords = async (keywordSelections) => {
    const keywords = keywordSelections.map(selection => selection.value);
    const uniqueKeywords = [...new Set([...data.keywords, ...keywords])];

    if (keywords.length === 0) {
      alert('Please select some keywords first.');
      return;
    }

    setIsGeneratingTopics(true);

    try {
      // Generate article topics using database-stored prompt
      // The backend will automatically use the keyword_selection prompt from the database
      const response = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_SUGGESTIONS, {
        method: 'POST',
        body: JSON.stringify({
          keywords: keywords
        }),
      });

      // Parse the response to extract topic suggestions
      let topicSuggestions = [];
      if (response && response.suggestions) {
        topicSuggestions = response.suggestions;
      } else if (response && typeof response === 'string') {
        // Parse numbered list from AI response
        const lines = response.split('\n').filter(line => line.trim());
        topicSuggestions = lines
          .filter(line => /^\d+\./.test(line.trim()))
          .map(line => line.replace(/^\d+\.\s*/, '').trim())
          .filter(topic => topic.length > 0);
      }

      // Update data with both keywords and generated topics
      updateData({
        keywords: uniqueKeywords,
        topicSuggestions: {
          aiGenerated: topicSuggestions,
          generatedFromKeywords: keywords
        }
      });

      // Clear selections after successful generation
      setSelections([]);

    } catch (error) {
      console.error('Error generating article topics:', error);
      alert('Failed to generate article topics. Please try again.');
      // Still update keywords even if topic generation fails
      updateData({ keywords: uniqueKeywords });
    } finally {
      setIsGeneratingTopics(false);
    }
  };

  const addCustomKeyword = (customKeywordData) => {
    const keywordWithId = {
      ...customKeywordData,
      id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    };
    setSelections(prev => [...prev, keywordWithId]);
  };

  return (
    <div className="flex">
      {/* Main Content Area */}
      <div className={`${hasResearched ? 'flex-1 pr-6' : 'w-full'} space-y-8`}>
        {/* Header Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Discover Your Next Article Topic</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Start by entering a keyword to research trending topics, questions people are asking, and content opportunities.
          </p>
        </div>

      {/* Main Research Section */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
        <div className="max-w-2xl mx-auto">
          <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
            🔍 Keyword Research & Topic Discovery
          </h3>

          {/* Primary Input */}
          <div className="flex gap-3 mb-6">
            <input
              type="text"
              value={keywordInput}
              onChange={(e) => setKeywordInput(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && keywordInput.trim()) {
                  addKeyword();
                  performKeywordResearch(keywordInput.trim());
                }
              }}
              placeholder="Enter a keyword to research (e.g., 'productivity', 'coffee', 'marketing')"
              className="flex-1 px-6 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 text-lg"
            />
            <button
              onClick={() => {
                if (keywordInput.trim()) {
                  addKeyword();
                  performKeywordResearch(keywordInput.trim());
                }
              }}
              disabled={!keywordInput.trim() || isResearching}
              className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium text-lg flex items-center"
            >
              {isResearching ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                  Researching...
                </>
              ) : (
                <>
                  <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
                  Research
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Topic Ideas Section */}
      {data.topicSuggestions && Object.keys(data.topicSuggestions).length > 0 && (
        <div className="space-y-8">
          {/* AI Generated Topics */}
          {data.topicSuggestions.aiGenerated && data.topicSuggestions.aiGenerated.length > 0 && (
            <div className="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl p-6 border border-purple-200">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-purple-900">🤖 AI-Generated Article Topics</h3>
                <div className="text-sm text-purple-600 bg-purple-100 px-3 py-1 rounded-full">
                  Based on {data.topicSuggestions.generatedFromKeywords?.length || 0} selected keywords
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {data.topicSuggestions.aiGenerated.map((topic, index) => (
                  <button
                    key={`ai-${index}`}
                    onClick={() => selectTopic(topic)}
                    className={`p-4 rounded-lg border text-left transition-all hover:shadow-md ${
                      data.selectedTopic === topic
                        ? 'border-purple-500 bg-purple-50 shadow-md'
                        : 'border-purple-200 bg-white hover:border-purple-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <div className="w-6 h-6 bg-purple-100 rounded flex items-center justify-center">
                          <span className="text-purple-600 font-bold text-sm">✨</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className={`font-medium text-sm leading-tight ${
                          data.selectedTopic === topic ? 'text-purple-900' : 'text-gray-900'
                        }`}>
                          {topic}
                        </h4>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Original Topic Ideas */}
          {(data.topicSuggestions.trendingAngles || data.topicSuggestions.problemFocused || data.topicSuggestions.comparisons) && (
            <div>
              <h3 className="text-xl font-bold text-gray-900 mb-6">Topic Ideas</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Show trending angles, problem-focused, and comparisons */}
                {[
                  ...(data.topicSuggestions.trendingAngles || []),
                  ...(data.topicSuggestions.problemFocused || []),
                  ...(data.topicSuggestions.comparisons || [])
                ].slice(0, 6).map((topic, index) => (
                  <button
                    key={index}
                    onClick={() => selectTopic(topic)}
                    className={`p-4 rounded-lg border text-left transition-all hover:shadow-md ${
                      data.selectedTopic === topic
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <div className="w-6 h-6 bg-gray-100 rounded flex items-center justify-center">
                          <span className="text-gray-600 font-bold text-sm">#</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className={`font-medium text-sm leading-tight ${
                          data.selectedTopic === topic ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {topic}
                        </h4>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Questions Section */}
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-6">Questions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Show only high-intent questions */}
              {(data.topicSuggestions.highIntentQuestions || []).slice(0, 6).map((question, index) => {
                return (
                  <button
                    key={`q-${index}`}
                    onClick={() => selectTopic(question)}
                    className={`p-4 rounded-lg border text-left transition-all hover:shadow-md ${
                      data.selectedTopic === question
                        ? 'border-blue-500 bg-blue-50 shadow-md'
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0 mt-1">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-blue-600 font-bold text-sm">?</span>
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className={`text-sm leading-tight ${
                          data.selectedTopic === question ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {question}
                        </p>
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      )}

      {/* Keyword Research Results - Prominent Position */}
      {keywordResearchData && (
        <div className="bg-white rounded-xl shadow-lg border border-gray-200">
          <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">
              🎯 Research Results for "{keywordResearchData.keyword}"
            </h3>
            <p className="text-gray-600">
              Discover trending topics, popular questions, and content opportunities based on real search data.
            </p>
          </div>
          <KeywordResearchPanel
            researchData={keywordResearchData}
            onKeywordSelect={handleKeywordFromResearch}
            onTopicSelect={handleTopicFromResearch}
            onAddToSelections={addToSelections}
            onRemoveSelection={removeFromSelections}
            selections={selections}
          />
        </div>
      )}
      </div>

      {/* Right Sidebar - Selection Manager - Only show after research */}
      {hasResearched && (
        <div className="w-80 flex-shrink-0">
          <div className="sticky top-6">
            <SelectionManager
              selections={selections}
              onRemoveSelection={removeFromSelections}
              onClearAll={clearAllSelections}
              onUseAsKeywords={useSelectionsAsKeywords}
              currentKeyword={currentResearchKeyword}
              onAddCustomKeyword={addCustomKeyword}
              isGeneratingTopics={isGeneratingTopics}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Step1Ideation;
