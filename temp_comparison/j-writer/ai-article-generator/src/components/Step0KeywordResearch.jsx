import React, { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { apiCall, API_CONFIG } from '../config/api';
import KeywordResearchPanel from './KeywordResearchPanel';
import SelectionManager from './SelectionManager';

const Step0KeywordResearch = ({ data, updateData, onNext, isGeneratingTopics, setIsGeneratingTopics }) => {
  const [keywordInput, setKeywordInput] = useState('');
  const [isResearching, setIsResearching] = useState(false);
  const [currentResearchKeyword, setCurrentResearchKeyword] = useState('');

  // Use persistent data from main app state
  const keywordResearchData = data.keywordResearchData;
  const selections = data.keywordResearchSelections || [];
  const hasResearched = !!keywordResearchData;
  
  // Get the current research keyword from persistent data or current state
  const effectiveCurrentKeyword = currentResearchKeyword || 
    (keywordResearchData && keywordResearchData.originalKeyword) ||
    (data.keywords && data.keywords.length > 0 ? data.keywords[0] : '');
  
  // Debug: Log keyword availability
  console.log('🔍 Keyword availability:', {
    currentResearchKeyword,
    originalKeyword: keywordResearchData?.originalKeyword,
    dataKeywords: data.keywords,
    effectiveCurrentKeyword
  });
  

  const addKeyword = () => {
    if (keywordInput.trim() && !data.keywords.includes(keywordInput.trim())) {
      updateData({
        keywords: [...data.keywords, keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  const performKeywordResearch = async (keyword) => {
    if (!keyword || keyword.trim().length === 0) {
      alert('Please enter a keyword for research.');
      return;
    }

    setIsResearching(true);
    setCurrentResearchKeyword(keyword.trim());
    try {
      const researchData = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_KEYWORD_RESEARCH, {
        method: 'POST',
        body: JSON.stringify({ keyword: keyword.trim() }),
      });

      // Save research data to persistent state
      updateData({
        keywordResearchData: researchData
      });
    } catch (error) {
      console.error('Error performing keyword research:', error);
      alert('Failed to perform keyword research. Please try again.');
    } finally {
      setIsResearching(false);
    }
  };

  const handleKeywordFromResearch = (keyword) => {
    if (keyword.trim() && !data.keywords.includes(keyword.trim())) {
      updateData({
        keywords: [...data.keywords, keyword.trim()]
      });
    }
  };

  const handleTopicFromResearch = (topic) => {
    updateData({ selectedTopic: topic });
  };

  // Helper functions for keyword limits - exclude Reddit topics
  const getPrimaryKeywordCount = () => {
    return selections.filter(s => 
      (s.type === 'autocomplete' || s.type === 'related' || s.type === 'custom') 
      && s.type !== 'reddit_topic'
    ).length;
  };

  const getSecondaryKeywordCount = () => {
    return selections.filter(s => s.type === 'keyTerm' || s.type === 'paa').length;
  };

  const isPrimaryKeywordType = (type) => {
    return (type === 'autocomplete' || type === 'related' || type === 'custom') && type !== 'reddit_topic';
  };

  const isSecondaryKeywordType = (type) => {
    return type === 'keyTerm' || type === 'paa';
  };

  const isRedditTopicType = (type) => {
    return type === 'reddit_topic';
  };

  // Selection management functions - 使用函数式更新防止状态竞争
  const addToSelections = (item, type, additionalData = {}) => {
    updateData(prevData => {
      const currentSelections = prevData.keywordResearchSelections || [];
      
      // 检查是否已存在
      const exists = currentSelections.some(selection =>
        selection.type === type && selection.value === item
      );

      if (exists) {
        return prevData; // 已存在，不做修改
      }

      // 检查限制 - 排除Reddit话题
      const primaryCount = currentSelections.filter(s => 
        (s.type === 'autocomplete' || s.type === 'related' || s.type === 'custom') 
        && s.type !== 'reddit_topic'
      ).length;
      const secondaryCount = currentSelections.filter(s => 
        s.type === 'keyTerm' || s.type === 'paa'
      ).length;

      // Reddit topics are not subject to limits
      if (type !== 'reddit_topic') {
        if ((type === 'autocomplete' || type === 'related' || type === 'custom') && primaryCount >= 3) {
          console.warn('🎯 Primary keyword limit reached (3/3). Cannot add more custom keywords.');
          alert('Primary keyword limit reached (3/3). Please remove some keywords first.');
          return prevData;
        }
        if ((type === 'keyTerm' || type === 'paa') && secondaryCount >= 12) {
          console.warn('🎯 Secondary keyword limit reached (12/12). Cannot add more keywords.');
          alert('Secondary keyword limit reached (12/12). Please remove some keywords first.');
          return prevData;
        }
      }

      // 生成唯一ID
      const timestamp = Date.now();
      const randomPart = Math.random().toString(36).substring(2, 15);
      const counter = currentSelections.length;
      const id = `${type}-${timestamp}-${counter}-${randomPart}`;

      const newSelection = {
        id,
        type: type,
        value: item,
        ...additionalData
      };

      console.log('🎯 Successfully added selection:', newSelection);
      return {
        ...prevData,
        keywordResearchSelections: [...currentSelections, newSelection]
      };
    });
  };

  const removeFromSelections = (selectionToRemove) => {
    updateData(prevData => {
      const currentSelections = prevData.keywordResearchSelections || [];
      
      const newSelections = currentSelections.filter(selection => {
        // 优先使用ID比较
        if (selection.id && selectionToRemove.id) {
          return selection.id !== selectionToRemove.id;
        }
        // 后备：使用值和类型比较
        return !(selection.value === selectionToRemove.value && selection.type === selectionToRemove.type);
      });

      return {
        ...prevData,
        keywordResearchSelections: newSelections
      };
    });
  };

  const clearAllSelections = () => {
    updateData({
      keywordResearchSelections: []
    });
  };

  // Handle Reddit post selection - allow up to 3 posts

  const useSelectionsAsKeywords = async (keywordSelections) => {
    const keywords = keywordSelections.map(selection => selection.value);
    const uniqueKeywords = [...new Set([...data.keywords, ...keywords])];

    if (keywords.length === 0) {
      alert('Please select some keywords first.');
      return;
    }

    // Set loading state and navigate to next step immediately
    setIsGeneratingTopics(true);

    // Clear any existing topic selections to fix the persistence issue
    updateData({
      selectedTopics: [], // Clear topic selections
      selectedTopic: '', // Clear legacy topic selection
      keywords: uniqueKeywords, // Set keywords immediately
    });

    // Navigate to next step immediately so user sees loading in Step 2
    onNext();

    try {
      // Generate article topics using database-stored clustered prompt
      // The backend will automatically use the clustered_topics prompt from the database
      // Include Reddit sources if available
      const requestBody = {
        keywords: keywords
      };
      
      // Add Reddit sources if available
      if (data.redditSources && data.redditSources.length > 0) {
        console.log(`🔗 Including ${data.redditSources.length} Reddit sources for enhanced topic generation`);
        requestBody.redditSources = data.redditSources;
      }
      
      const response = await apiCall(API_CONFIG.ENDPOINTS.IDEATION_SUGGESTIONS, {
        method: 'POST',
        body: JSON.stringify(requestBody),
      });

      // Parse the response to extract clustered topic suggestions
      let topicSuggestions = [];
      let clusteredTopics = {};

      console.log('🔍 DEBUG: Raw AI Response:', response);

      if (response && response.suggestions) {
        topicSuggestions = response.suggestions;
        console.log('📝 Using structured response with suggestions:', topicSuggestions.length);

        // Check if the response also has clustered data
        if (response.clustered) {
          clusteredTopics = response.clustered;
          console.log('🗂️ Found clustered data in response:', Object.keys(clusteredTopics));
        }
      } else if (response && typeof response === 'string') {
        console.log('📄 Parsing string response for clusters...');

        // Parse clustered Markdown response
        const lines = response.split('\n').filter(line => line.trim());
        let currentCluster = null;

        console.log('📋 Total lines to parse:', lines.length);

        for (const line of lines) {
          const trimmedLine = line.trim();

          // Check for H3 headers (cluster names) - be more flexible
          if (trimmedLine.startsWith('### ') || trimmedLine.startsWith('## ') || trimmedLine.match(/^#{1,3}\s+/)) {
            currentCluster = trimmedLine.replace(/^#{1,3}\s+/, '').trim();
            clusteredTopics[currentCluster] = [];
            console.log('🏷️ Found cluster:', currentCluster);
          }
          // Check for numbered list items
          else if (/^\d+\./.test(trimmedLine) && currentCluster) {
            const topic = trimmedLine.replace(/^\d+\.\s*/, '').trim();
            if (topic.length > 0) {
              clusteredTopics[currentCluster].push(topic);
              topicSuggestions.push(topic); // Also add to flat list for backward compatibility
              console.log(`  ✅ Added topic to ${currentCluster}:`, topic.substring(0, 50) + '...');
            }
          }
          // Also check for bullet points as fallback
          else if (/^[-*]\s+/.test(trimmedLine) && currentCluster) {
            const topic = trimmedLine.replace(/^[-*]\s+/, '').trim();
            if (topic.length > 0) {
              clusteredTopics[currentCluster].push(topic);
              topicSuggestions.push(topic);
              console.log(`  ✅ Added bullet topic to ${currentCluster}:`, topic.substring(0, 50) + '...');
            }
          }
        }

        console.log('🗂️ Final clustered topics:', Object.keys(clusteredTopics));
        console.log('📊 Total topics found:', topicSuggestions.length);

        // If no clusters found, fall back to simple numbered list parsing
        if (Object.keys(clusteredTopics).length === 0) {
          console.log('⚠️ No clusters found, falling back to simple parsing...');
          topicSuggestions = lines
            .filter(line => /^\d+\./.test(line.trim()))
            .map(line => line.replace(/^\d+\.\s*/, '').trim())
            .filter(topic => topic.length > 0);
          console.log('📝 Fallback topics found:', topicSuggestions.length);
        }
      }

      // Update data with generated topics (both clustered and flat)
      // Keep keywordResearchSelections for article generation later
      updateData({
        topicSuggestions: {
          aiGenerated: topicSuggestions,
          clustered: clusteredTopics,
          generatedFromKeywords: keywords,
          hasClusters: Object.keys(clusteredTopics).length > 0
        }
        // Don't clear keywordResearchSelections - we need them for article generation
      });

    } catch (error) {
      console.error('Error generating article topics:', error);

      // Parse error response for better user messaging
      let errorMessage = 'AI service is temporarily unavailable. Please try again later.';
      if (error.message && error.message.includes('AI service unavailable')) {
        errorMessage = 'Unable to connect to AI service. Please check your internet connection and try again.';
      }

      // Update data with error state
      updateData({
        topicSuggestions: {
          error: true,
          errorMessage: errorMessage
        }
        // Keep keywordResearchSelections even on error
      });
    } finally {
      setIsGeneratingTopics(false);
    }
  };



  const addCustomKeyword = (customKeywordData) => {
    const keywordWithId = {
      ...customKeywordData,
      id: `custom-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
    };
    updateData({
      keywordResearchSelections: [...selections, keywordWithId]
    });
  };

  return (
    <div className="flex">
      {/* Main Content Area */}
      <div className={`${hasResearched ? 'flex-1 pr-6' : 'w-full'} space-y-8`}>
        {/* Header Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Discover Content Opportunities</h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            <span className="font-semibold text-blue-600">Step 1 of 7:</span> Begin your article creation journey by researching keywords.
            Enter a topic or keyword to discover trending angles, popular questions, and content opportunities
            that will form the foundation of your article.
          </p>
        </div>

        {/* Main Research Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
              🔍 Keyword Research & Topic Discovery
            </h3>

            {/* Primary Input */}
            <div className="flex gap-3 mb-6">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && keywordInput.trim()) {
                    addKeyword();
                    performKeywordResearch(keywordInput.trim());
                  }
                }}
                placeholder="Enter a keyword to research (e.g., 'productivity', 'coffee', 'marketing')"
                className="flex-1 px-6 py-4 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900 text-lg"
              />
              <button
                onClick={() => {
                  if (keywordInput.trim()) {
                    addKeyword();
                    performKeywordResearch(keywordInput.trim());
                  }
                }}
                disabled={!keywordInput.trim() || isResearching}
                className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium text-lg flex items-center"
              >
                {isResearching ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Researching...
                  </>
                ) : (
                  <>
                    <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
                    Research
                  </>
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Keyword Research Results */}
        {keywordResearchData && (
          <div className="bg-white rounded-xl shadow-lg border border-gray-200">
            <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">
                🎯 Research Results for "{keywordResearchData.keyword}"
              </h3>
              <p className="text-gray-600">
                Discover trending topics, popular questions, and content opportunities based on real search data.
              </p>
            </div>
            <KeywordResearchPanel
              researchData={keywordResearchData}
              onKeywordSelect={handleKeywordFromResearch}
              onTopicSelect={handleTopicFromResearch}
              onAddToSelections={addToSelections}
              onRemoveSelection={removeFromSelections}
              selections={selections}
              primaryKeywordCount={getPrimaryKeywordCount()}
              secondaryKeywordCount={getSecondaryKeywordCount()}
              primaryKeywordLimit={3}
              secondaryKeywordLimit={12}
            />
          </div>
        )}

      </div>

      {/* Right Sidebar - Selection Manager - Only show after research */}
      {hasResearched && (
        <div className="w-80 flex-shrink-0">
          <div className="sticky top-6">
            <SelectionManager
              selections={selections}
              onRemoveSelection={removeFromSelections}
              onClearAll={clearAllSelections}
              onUseAsKeywords={useSelectionsAsKeywords}
              currentKeyword={effectiveCurrentKeyword}
              onAddCustomKeyword={addCustomKeyword}
              isGeneratingTopics={isGeneratingTopics}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Step0KeywordResearch;
