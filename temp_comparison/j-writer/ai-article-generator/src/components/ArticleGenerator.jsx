import React, { useState } from 'react';
import { ChevronRightIcon, ChevronLeftIcon, SparklesIcon } from '@heroicons/react/24/outline';
import StepIndicator from './StepIndicator';
import WriterJLogo from './WriterJLogo';
import Step0KeywordResearch from './Step0KeywordResearch';
import Step1TopicSelection from './Step1TopicSelection';
import Step3ComprehensiveResources from './Step3ComprehensiveResources';
import Step3Product from './Step3Product';
import Step4EEAT from './Step4EEAT';
import Step4Parameters from './Step4Parameters';
import Step5Generation from './Step5Generation';

const STEPS = [
  { id: 1, title: 'Keyword Research', subtitle: 'Discover content opportunities', component: Step0KeywordResearch, icon: '🔍' },
  { id: 2, title: 'Choose Topics', subtitle: 'Define your article focus', component: Step1TopicSelection, icon: '🎯' },
  { id: 3, title: 'Comprehensive Resources', subtitle: 'Add any supporting resources', component: Step3ComprehensiveResources, icon: '📚' },
  { id: 4, title: 'Product Integration', subtitle: 'Optional product mentions', component: Step3Product, icon: '🏷️' },
  { id: 5, title: 'Authority Profile', subtitle: 'Establish your expertise', component: Step4EEAT, icon: '👤' },
  { id: 6, title: 'Style & Format', subtitle: 'Customize writing style', component: Step4Parameters, icon: '⚙️' },
  { id: 7, title: 'Generate Article', subtitle: 'Create your content', component: Step5Generation, icon: '✨' },
];

function ArticleGenerator() {
  const [currentStep, setCurrentStep] = useState(0); // Index 0 = Step 1
  const [isGeneratingTopics, setIsGeneratingTopics] = useState(false);
  const [articleData, setArticleData] = useState({
    keywords: [],
    selectedTopic: '',
    selectedTopics: [], // New: array of selected topics with original and edited text
    topicSuggestions: [],
    keywordResearchData: null, // Store keyword research results temporarily
    keywordResearchSelections: [], // Store selections from keyword research
    sources: [], // Legacy: keep for backward compatibility
    topicSources: {}, // New: sources organized by topic { topicId: [sources] }
    comprehensiveResources: [], // New: unified resource management (URLs, text blocks)
    redditSources: [], // Reddit insights from Step 1
    productInfo: {
      name: '',
      link: '',
      description: '',
      features: []
    },
    eeatProfile: {
      authorName: '',
      authorBio: '',
      authorCredentials: '',
      experienceYears: '',
      relevantExperience: ''
    },
    outputParameters: {
      tonality: 'informative',
      length: 'medium_article',
      format: 'markdown',
      includeOutline: false,
      includeSources: true,
      includeCallToAction: false,
      callToActionText: '',
      targetAudience: '',
      writingStyle: 'Informative',
      seoFocus: true
    },
    generatedArticle: ''
  });

  const updateArticleData = (updates) => {
    // 支持函数式更新和对象更新
    if (typeof updates === 'function') {
      setArticleData(updates);
    } else {
      setArticleData(prev => ({
        ...prev,
        ...updates
      }));
    }
  };

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (stepIndex) => {
    setCurrentStep(stepIndex);
  };

  const CurrentStepComponent = STEPS[currentStep].component;

  const canProceedToNext = () => {
    switch (currentStep) {
      case 0:
        return true; // Keyword research is optional, can proceed anytime
      case 1:
        return articleData.selectedTopics && articleData.selectedTopics.length > 0;
      case 2:
        return true; // Sources are optional
      case 3:
        return true; // Product info is optional
      case 4:
        return true; // Parameters have defaults
      case 5:
        return true; // Final step
      default:
        return false;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Modern Header - Full Width */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50 shadow-lg shadow-blue-500/5">
        <div className="w-full px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <WriterJLogo className="w-12 h-12" showText={false} />
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-indigo-800 bg-clip-text text-transparent">
                  Writer J
                </h1>
                <p className="text-sm text-gray-600 font-medium">AI Article Generator</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-2 px-4 py-2 bg-white/60 rounded-full">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-700">AI Ready</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content - Full Width */}
      <main className="w-full py-8">
        <div className="flex min-h-screen">
          {/* Left Sidebar - Step Indicator */}
          <div className="w-64 flex-shrink-0 px-4">
            <div className="sticky top-32">
              <StepIndicator
                steps={STEPS}
                currentStep={currentStep}
                onStepClick={goToStep}
              />
            </div>
          </div>

          {/* Main Content Area - Full Width */}
          <div className="flex-1 px-6">
            {/* Premium Content Container */}
            <div className="bg-white/70 backdrop-blur-sm rounded-3xl shadow-2xl shadow-blue-500/10 border border-white/30 overflow-hidden">
              {/* Elegant Header */}
              <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 px-8 py-6 border-b border-white/30">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900 mb-1">
                      {STEPS[currentStep].title}
                    </h2>
                    <p className="text-gray-600 font-medium">
                      {STEPS[currentStep].subtitle}
                    </p>
                  </div>
                  <div className="text-4xl opacity-80">
                    {STEPS[currentStep].icon}
                  </div>
                </div>
              </div>

              {/* Premium Step Content */}
              <div className="relative p-8">
                <CurrentStepComponent
                  data={articleData}
                  updateData={updateArticleData}
                  onNext={nextStep}
                  onPrev={prevStep}
                  isFirstStep={currentStep === 0}
                  isLastStep={currentStep === STEPS.length - 1}
                  isGeneratingTopics={isGeneratingTopics}
                  setIsGeneratingTopics={setIsGeneratingTopics}
                />
              </div>

              {/* Premium Navigation */}
              <div className="bg-gradient-to-r from-gray-50 via-blue-50 to-indigo-50 px-8 py-6 border-t border-white/30">
                <div className="flex justify-between items-center">
                  <button
                    onClick={prevStep}
                    disabled={currentStep === 0}
                    className="flex items-center space-x-2 px-6 py-3 bg-white/80 text-gray-700 rounded-xl font-medium hover:bg-white hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 border border-gray-200/50"
                  >
                    <ChevronLeftIcon className="w-4 h-4" />
                    <span>Previous</span>
                  </button>

                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600 font-medium">
                      Step {currentStep + 1} of {STEPS.length}
                    </span>
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${((currentStep + 1) / STEPS.length) * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <button
                    onClick={nextStep}
                    disabled={currentStep === STEPS.length - 1 || !canProceedToNext()}
                    className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-xl font-medium hover:from-blue-700 hover:to-indigo-700 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg"
                  >
                    <span>Next</span>
                    <ChevronRightIcon className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

export default ArticleGenerator;
