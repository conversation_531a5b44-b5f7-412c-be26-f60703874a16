import React, { useState } from 'react';
import { 
  XMarkIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  TagIcon,
  QuestionMarkCircleIcon,
  CloudIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';

const FloatingSelectionPanel = ({ 
  selectedItems = {
    autocomplete: [],
    relatedKeywords: [],
    keyTerms: [],
    peopleAlsoAsk: []
  },
  onRemoveItem,
  onClearAll,
  onUseForContent
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  // Calculate total items
  const totalItems = Object.values(selectedItems).reduce((total, items) => total + items.length, 0);

  // Get all items with their types
  const getAllItems = () => {
    const allItems = [];
    
    selectedItems.autocomplete?.forEach(item => 
      allItems.push({ type: 'autocomplete', content: item, icon: MagnifyingGlassIcon, color: 'blue' })
    );
    
    selectedItems.relatedKeywords?.forEach(item => 
      allItems.push({ type: 'relatedKeywords', content: item, icon: TagIcon, color: 'gray' })
    );
    
    selectedItems.keyTerms?.forEach(item => 
      allItems.push({ type: 'keyTerms', content: item, icon: CloudIcon, color: 'purple' })
    );
    
    selectedItems.peopleAlsoAsk?.forEach(item => 
      allItems.push({ type: 'peopleAlsoAsk', content: item, icon: QuestionMarkCircleIcon, color: 'orange' })
    );
    
    return allItems;
  };

  const getFilteredItems = () => {
    if (activeTab === 'all') return getAllItems();
    return getAllItems().filter(item => item.type === activeTab);
  };

  const getTabCount = (type) => {
    if (type === 'all') return totalItems;
    return selectedItems[type]?.length || 0;
  };

  const getColorClasses = (color) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200'
    };
    return colors[color] || colors.gray;
  };

  if (totalItems === 0) return null;

  return (
    <div className={`fixed left-4 top-1/2 transform -translate-y-1/2 z-50 transition-all duration-300 ${
      isExpanded ? 'w-80' : 'w-12'
    }`}>
      <div className="bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {isExpanded && (
              <div className="flex items-center space-x-2">
                <DocumentTextIcon className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">Selected Items</h3>
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {totalItems}
                </span>
              </div>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-1 hover:bg-blue-100 rounded transition-colors"
            >
              {isExpanded ? (
                <ChevronLeftIcon className="w-5 h-5 text-gray-600" />
              ) : (
                <ChevronRightIcon className="w-5 h-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        {/* Content */}
        {isExpanded && (
          <div className="max-h-96 flex flex-col">
            {/* Tabs */}
            <div className="flex border-b border-gray-200 bg-gray-50">
              <button
                onClick={() => setActiveTab('all')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  activeTab === 'all' 
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                All ({getTabCount('all')})
              </button>
              <button
                onClick={() => setActiveTab('autocomplete')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  activeTab === 'autocomplete' 
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Auto ({getTabCount('autocomplete')})
              </button>
              <button
                onClick={() => setActiveTab('relatedKeywords')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  activeTab === 'relatedKeywords' 
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Related ({getTabCount('relatedKeywords')})
              </button>
              <button
                onClick={() => setActiveTab('keyTerms')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  activeTab === 'keyTerms' 
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Terms ({getTabCount('keyTerms')})
              </button>
              <button
                onClick={() => setActiveTab('peopleAlsoAsk')}
                className={`flex-1 px-3 py-2 text-xs font-medium transition-colors ${
                  activeTab === 'peopleAlsoAsk' 
                    ? 'bg-white text-blue-600 border-b-2 border-blue-600' 
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                PAA ({getTabCount('peopleAlsoAsk')})
              </button>
            </div>

            {/* Items List */}
            <div className="flex-1 overflow-y-auto p-3 space-y-2 max-h-64">
              {getFilteredItems().map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div
                    key={`${item.type}-${index}`}
                    className={`flex items-center justify-between p-2 rounded border text-sm ${getColorClasses(item.color)}`}
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <IconComponent className="w-4 h-4 flex-shrink-0" />
                      <span className="truncate">{item.content}</span>
                    </div>
                    <button
                      onClick={() => onRemoveItem && onRemoveItem(item.type, item.content)}
                      className="ml-2 p-1 hover:bg-red-100 rounded transition-colors flex-shrink-0"
                    >
                      <XMarkIcon className="w-3 h-3 text-red-500" />
                    </button>
                  </div>
                );
              })}
              
              {getFilteredItems().length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p className="text-sm">No items selected</p>
                  <p className="text-xs mt-1">Click items from research results to add them here</p>
                </div>
              )}
            </div>

            {/* Actions */}
            {totalItems > 0 && (
              <div className="border-t border-gray-200 p-3 space-y-2">
                <button
                  onClick={() => onUseForContent && onUseForContent(selectedItems)}
                  className="w-full px-3 py-2 bg-blue-600 text-white rounded text-sm font-medium hover:bg-blue-700 transition-colors"
                >
                  Use for Content Creation
                </button>
                <button
                  onClick={() => onClearAll && onClearAll()}
                  className="w-full px-3 py-2 bg-gray-100 text-gray-700 rounded text-sm font-medium hover:bg-gray-200 transition-colors flex items-center justify-center space-x-1"
                >
                  <TrashIcon className="w-4 h-4" />
                  <span>Clear All</span>
                </button>
              </div>
            )}
          </div>
        )}

        {/* Collapsed state indicator */}
        {!isExpanded && totalItems > 0 && (
          <div className="p-3 text-center">
            <div className="w-6 h-6 bg-blue-100 text-blue-800 rounded-full flex items-center justify-center text-xs font-bold mx-auto">
              {totalItems}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingSelectionPanel;
