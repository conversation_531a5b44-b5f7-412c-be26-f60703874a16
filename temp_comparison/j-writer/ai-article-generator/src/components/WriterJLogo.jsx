import React from 'react';

const WriterJLogo = ({ className = "w-12 h-12", showText = false, textClassName = "text-2xl font-bold text-gray-900" }) => {
  return (
    <div className="flex items-center space-x-3">
      <div className={`${className} flex items-center justify-center`}>
        <img
          src="/writej.png"
          alt="Writer J Logo"
          className={`${className} object-contain`}
        />
      </div>

      {showText && (
        <span className={textClassName}>Writer J</span>
      )}
    </div>
  );
};

export default WriterJLogo;
