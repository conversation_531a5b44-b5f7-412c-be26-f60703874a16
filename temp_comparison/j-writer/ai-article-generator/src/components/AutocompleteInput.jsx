import React, { useRef, useEffect } from 'react';
import { useAutocomplete } from '../hooks/useAutocomplete';

const AutocompleteInput = ({ 
  value, 
  onChange, 
  onSelect, 
  placeholder = "Enter keywords...",
  className = "",
  disabled = false 
}) => {
  const inputRef = useRef(null);
  const suggestionsRef = useRef(null);
  
  const {
    suggestions,
    isLoading,
    showSuggestions,
    selectedIndex,
    debouncedFetchSuggestions,
    hideSuggestions,
    selectSuggestion,
    handleKeyDown
  } = useAutocomplete();

  // Handle input change
  const handleInputChange = (e) => {
    const newValue = e.target.value;
    onChange(newValue);
    debouncedFetchSuggestions(newValue);
  };

  // Handle key down events
  const handleInputKeyDown = (e) => {
    const handled = handleKeyDown(e, value, (suggestion) => {
      onChange(suggestion);
      if (onSelect) {
        onSelect(suggestion);
      }
    });

    // If not handled by autocomplete, let the parent handle it
    if (!handled && e.key === 'Enter' && onSelect) {
      onSelect(value);
    }
  };

  // Handle suggestion click
  const handleSuggestionClick = (suggestion) => {
    const selected = selectSuggestion(suggestion);
    onChange(selected);
    if (onSelect) {
      onSelect(selected);
    }
    inputRef.current?.focus();
  };

  // Handle input blur with delay to allow suggestion clicks
  const handleInputBlur = () => {
    setTimeout(() => {
      hideSuggestions();
    }, 150);
  };

  // Handle input focus
  const handleInputFocus = () => {
    if (value && value.trim().length >= 2 && suggestions.length > 0) {
      // Show suggestions if we have them
      debouncedFetchSuggestions(value);
    }
  };

  // Scroll selected suggestion into view
  useEffect(() => {
    if (selectedIndex >= 0 && suggestionsRef.current) {
      const selectedElement = suggestionsRef.current.children[selectedIndex];
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  return (
    <div className="relative">
      <input
        ref={inputRef}
        type="text"
        value={value}
        onChange={handleInputChange}
        onKeyDown={handleInputKeyDown}
        onBlur={handleInputBlur}
        onFocus={handleInputFocus}
        placeholder={placeholder}
        disabled={disabled}
        className={`${className} ${isLoading ? 'pr-8' : ''}`}
        autoComplete="off"
      />
      
      {/* Loading indicator */}
      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </div>
      )}

      {/* Suggestions dropdown */}
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto">
          <ul ref={suggestionsRef} className="py-1">
            {suggestions.map((suggestion, index) => (
              <li
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className={`px-4 py-2 cursor-pointer text-sm ${
                  index === selectedIndex
                    ? 'bg-blue-100 text-blue-900'
                    : 'text-gray-900 hover:bg-gray-100'
                }`}
              >
                <div className="flex items-center">
                  <svg 
                    className="w-4 h-4 mr-3 text-gray-400" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
                    />
                  </svg>
                  <span className="truncate">{suggestion}</span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default AutocompleteInput;
