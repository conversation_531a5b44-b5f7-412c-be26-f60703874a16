import React from 'react';

const Step4Parameters = ({ data, updateData }) => {
  const updateParameters = (field, value) => {
    updateData({
      outputParameters: {
        ...data.outputParameters,
        [field]: value
      }
    });
  };

  const tonalityOptions = [
    { value: 'informative', label: 'Informative', description: 'Educational and fact-based' },
    { value: 'persuasive', label: 'Persuasive', description: 'Convincing and compelling' },
    { value: 'casual', label: 'Casual', description: 'Friendly and conversational' },
    { value: 'formal', label: 'Formal', description: 'Professional and structured' },
    { value: 'technical', label: 'Technical', description: 'Detailed and expert-level' },
  ];

const lengthOptions = [
  {
    value: 'snippet',
    label: 'Snippet (~500 words)', // New shorter option
    description: 'Very short, focused piece, ideal for quick updates, single ideas, or FAQ answers'
  },
  {
    value: 'short_post', // Corresponds to your previous 'short'
    label: 'Short Post (~800 words)',
    description: 'Concise content, good for explaining a specific concept or a typical blog post (usually suitable for 1-2 topics)'
  },
  {
    value: 'medium_article', // Corresponds to your previous 'medium'
    label: 'Medium Article (~1500 words)',
    description: 'Balanced depth and readability, suitable for an in-depth exploration of a topic (usually suitable for 2-4 topics)'
  },
  {
    value: 'long_guide', // Corresponds to your previous 'long'
    label: 'Long-Form Guide (~2500 words)',
    description: 'Comprehensive and detailed coverage of a single, focused topic (monitor single-generation quality for optimal results; usually suitable for 3-5 closely related topics)'
  },
  {
    value: 'pillar_module', // New strategic option
    label: 'Pillar Page Module (~700 words)',
    description: 'A focused chapter/section designed for building a larger pillar page (recommended for the "modular assembly" strategy, typically for 1 sub-topic)'
  }
];

  const formatOptions = [
    { value: 'markdown', label: 'Markdown', description: 'With headings and formatting' },
    { value: 'plain', label: 'Plain Text', description: 'Simple text without formatting' },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Customize Your Article Style</h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          <span className="font-semibold text-blue-600">Step 6 of 7:</span> Configure how your article should be written.
          Choose the tone, length, and format that best suits your audience and goals.
          These settings will guide the AI to create content that matches your specific requirements.
        </p>
      </div>

      {/* Tonality Selection */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Tonality
        </label>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {tonalityOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                data.outputParameters?.tonality === option.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 bg-white hover:bg-gray-50'
              }`}
            >
              <input
                type="radio"
                name="tonality"
                value={option.value}
                checked={data.outputParameters?.tonality === option.value}
                onChange={(e) => updateParameters('tonality', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className="block text-sm font-medium text-gray-900">
                    {option.label}
                  </span>
                  <span className="block text-sm text-gray-500">
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Article Length */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Article Length
        </label>
        <div className="space-y-3">
          {lengthOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                data.outputParameters?.length === option.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 bg-white hover:bg-gray-50'
              }`}
            >
              <input
                type="radio"
                name="length"
                value={option.value}
                checked={data.outputParameters?.length === option.value}
                onChange={(e) => updateParameters('length', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className="block text-sm font-medium text-gray-900">
                    {option.label}
                  </span>
                  <span className="block text-sm text-gray-500">
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Response Format */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Response Format
        </label>
        <div className="space-y-3">
          {formatOptions.map((option) => (
            <label
              key={option.value}
              className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                data.outputParameters?.format === option.value
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 bg-white hover:bg-gray-50'
              }`}
            >
              <input
                type="radio"
                name="format"
                value={option.value}
                checked={data.outputParameters?.format === option.value}
                onChange={(e) => updateParameters('format', e.target.value)}
                className="sr-only"
              />
              <div className="flex flex-1">
                <div className="flex flex-col">
                  <span className="block text-sm font-medium text-gray-900">
                    {option.label}
                  </span>
                  <span className="block text-sm text-gray-500">
                    {option.description}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>



      {/* Parameters Summary */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="text-md font-medium text-gray-800 mb-3">📋 Style Configuration Summary</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <span className="font-medium text-gray-700">Tone:</span>
            <span className="ml-2 text-gray-600 capitalize">{data.outputParameters?.tonality || 'informative'}</span>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <span className="font-medium text-gray-700">Length:</span>
            <span className="ml-2 text-gray-600 capitalize">{data.outputParameters?.length || 'medium_article'}</span>
          </div>
          <div className="bg-white rounded-lg p-3 border border-gray-200">
            <span className="font-medium text-gray-700">Format:</span>
            <span className="ml-2 text-gray-600 capitalize">{data.outputParameters?.format || 'markdown'}</span>
          </div>
        </div>
      </div>

      {/* Ready for Next Step */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <p className="text-sm text-green-700">
          <strong>Style configured!</strong> Your article will be written in a <strong>{data.outputParameters?.tonality || 'informative'}</strong> tone,
          approximately <strong>{(() => {
            const length = data.outputParameters?.length || 'medium_article';
            switch(length) {
              case 'snippet': return '500';
              case 'short_post': return '800';
              case 'medium_article': return '1500';
              case 'long_guide': return '2500';
              case 'pillar_module': return '700';
              default: return '1500';
            }
          })()} words</strong>,
          formatted as <strong>{data.outputParameters?.format || 'markdown'}</strong>.
        </p>
      </div>
    </div>
  );
};

export default Step4Parameters;
