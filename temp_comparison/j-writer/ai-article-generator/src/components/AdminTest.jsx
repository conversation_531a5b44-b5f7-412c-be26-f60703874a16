import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { buildApiUrl, API_CONFIG } from '../config/api';

const AdminTest = () => {
  const { user, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const results = {};

    try {
      // Test 1: Check authentication
      results.auth = {
        isAuthenticated,
        user: user ? { ...user } : null,
        hasRole: !!user?.role,
        isAdmin: user?.role === 'admin'
      };

      // Test 2: Test API connection
      try {
        const response = await fetch(buildApiUrl('/health'));
        results.apiConnection = {
          success: response.ok,
          status: response.status,
          url: buildApiUrl('/health')
        };
      } catch (error) {
        results.apiConnection = {
          success: false,
          error: error.message,
          url: buildApiUrl('/health')
        };
      }

      // Test 3: Test admin endpoint access
      if (isAuthenticated) {
        try {
          const token = localStorage.getItem('token');
          const response = await fetch(buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.STATS), {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          });
          
          results.adminAccess = {
            success: response.ok,
            status: response.status,
            url: buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.STATS)
          };

          if (response.ok) {
            const data = await response.json();
            results.adminAccess.data = data;
          } else {
            const errorData = await response.text();
            results.adminAccess.error = errorData;
          }
        } catch (error) {
          results.adminAccess = {
            success: false,
            error: error.message,
            url: buildApiUrl(API_CONFIG.ENDPOINTS.ADMIN.STATS)
          };
        }
      }

      setTestResults(results);
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      runTests();
    }
  }, [isAuthenticated]);

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white shadow-lg rounded-lg">
      <h2 className="text-2xl font-bold mb-6">Admin Access Test</h2>
      
      <button
        onClick={runTests}
        disabled={loading}
        className="mb-6 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
      >
        {loading ? 'Running Tests...' : 'Run Tests'}
      </button>

      <div className="space-y-6">
        {/* Authentication Test */}
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">🔐 Authentication Test</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Authenticated:</strong> 
              <span className={`ml-2 ${testResults.auth?.isAuthenticated ? 'text-green-600' : 'text-red-600'}`}>
                {testResults.auth?.isAuthenticated ? '✅ Yes' : '❌ No'}
              </span>
            </div>
            <div>
              <strong>Has Role:</strong> 
              <span className={`ml-2 ${testResults.auth?.hasRole ? 'text-green-600' : 'text-red-600'}`}>
                {testResults.auth?.hasRole ? '✅ Yes' : '❌ No'}
              </span>
            </div>
            <div>
              <strong>User Role:</strong> 
              <span className="ml-2 font-mono">
                {testResults.auth?.user?.role || 'undefined'}
              </span>
            </div>
            <div>
              <strong>Is Admin:</strong> 
              <span className={`ml-2 ${testResults.auth?.isAdmin ? 'text-green-600' : 'text-red-600'}`}>
                {testResults.auth?.isAdmin ? '✅ Yes' : '❌ No'}
              </span>
            </div>
          </div>
          
          {testResults.auth?.user && (
            <div className="mt-4">
              <strong>User Object:</strong>
              <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(testResults.auth.user, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* API Connection Test */}
        <div className="border rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">🌐 API Connection Test</h3>
          {testResults.apiConnection && (
            <div className="space-y-2 text-sm">
              <div>
                <strong>Status:</strong> 
                <span className={`ml-2 ${testResults.apiConnection.success ? 'text-green-600' : 'text-red-600'}`}>
                  {testResults.apiConnection.success ? '✅ Connected' : '❌ Failed'}
                </span>
              </div>
              <div>
                <strong>URL:</strong> 
                <span className="ml-2 font-mono text-blue-600">
                  {testResults.apiConnection.url}
                </span>
              </div>
              <div>
                <strong>HTTP Status:</strong> 
                <span className="ml-2 font-mono">
                  {testResults.apiConnection.status}
                </span>
              </div>
              {testResults.apiConnection.error && (
                <div>
                  <strong>Error:</strong> 
                  <span className="ml-2 text-red-600">
                    {testResults.apiConnection.error}
                  </span>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Admin Access Test */}
        {isAuthenticated && (
          <div className="border rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3">👑 Admin Access Test</h3>
            {testResults.adminAccess && (
              <div className="space-y-2 text-sm">
                <div>
                  <strong>Status:</strong> 
                  <span className={`ml-2 ${testResults.adminAccess.success ? 'text-green-600' : 'text-red-600'}`}>
                    {testResults.adminAccess.success ? '✅ Access Granted' : '❌ Access Denied'}
                  </span>
                </div>
                <div>
                  <strong>URL:</strong> 
                  <span className="ml-2 font-mono text-blue-600">
                    {testResults.adminAccess.url}
                  </span>
                </div>
                <div>
                  <strong>HTTP Status:</strong> 
                  <span className="ml-2 font-mono">
                    {testResults.adminAccess.status}
                  </span>
                </div>
                {testResults.adminAccess.error && (
                  <div>
                    <strong>Error:</strong> 
                    <span className="ml-2 text-red-600 font-mono text-xs">
                      {testResults.adminAccess.error}
                    </span>
                  </div>
                )}
                {testResults.adminAccess.data && (
                  <div>
                    <strong>Response Data:</strong>
                    <pre className="mt-2 p-3 bg-gray-100 rounded text-xs overflow-auto">
                      {JSON.stringify(testResults.adminAccess.data, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {!isAuthenticated && (
        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800">
            Please log in with admin credentials to test admin access:
          </p>
          <div className="mt-2 font-mono text-sm">
            <div>Email: <EMAIL></div>
            <div>Password: Admin123!</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminTest;
