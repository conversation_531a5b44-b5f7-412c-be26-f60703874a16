# Writer J - Environment Configuration Example
# Copy this file to .env and update with your actual values

# Database Configuration
# For Production (PostgreSQL)
DATABASE_URL=postgresql://username:password@localhost:5432/writer_j

# For Development (SQLite) - uncomment the line below instead
# DATABASE_URL=sqlite:./database.db

# JWT Secret (generate a secure random string)
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random

# AI Service API Keys
# Google Gemini API (Primary)
GOOGLE_API_KEY=your-google-gemini-api-key

# DeepSeek API (Alternative)
DEEPSEEK_API_KEY=your-deepseek-api-key

# OpenAI API (Optional)
OPENAI_API_KEY=your-openai-api-key

# Search API
SERPER_API_KEY=your-serper-search-api-key

# Reddit OAuth (Optional - for enhanced Reddit features)
REDDIT_CLIENT_ID=your-reddit-client-id
REDDIT_CLIENT_SECRET=your-reddit-client-secret
REDDIT_REDIRECT_URI=http://localhost:3001/api/reddit-oauth/callback

# Server Configuration
PORT=3001
NODE_ENV=development

# CORS Configuration (Frontend URL)
FRONTEND_URL=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=another-secret-for-sessions