# Contributing to <PERSON> <PERSON>

Thank you for your interest in contributing to <PERSON> J! This document provides guidelines and information for contributors.

## 🤝 Ways to Contribute

### 🐛 Reporting Bugs
- Use the GitHub issue tracker
- Provide detailed reproduction steps
- Include environment information (OS, Node.js version, etc.)
- Add screenshots or error logs when applicable

### 💡 Suggesting Features
- Check existing issues to avoid duplicates
- Provide clear use cases and benefits
- Consider implementation complexity
- Be open to discussion and feedback

### 📝 Documentation
- Fix typos and grammar
- Add missing documentation
- Improve code comments
- Update outdated information

### 🔧 Code Contributions
- Fix bugs and implement features
- Improve performance
- Add tests
- Refactor for better maintainability

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Git
- PostgreSQL (for full development)
- AI API keys for testing

### Development Setup

1. **Fork and Clone**
```bash
git clone https://github.com/YOUR-USERNAME/j-writer.git
cd j-writer
```

2. **Backend Setup**
```bash
cd backend
npm install
cp .env.example .env
# Configure your .env file
npm run init-db
npm run dev
```

3. **Frontend Setup**
```bash
cd ../ai-article-generator
npm install
cp .env.example .env.local
# Configure your .env.local file
npm run dev
```

4. **Verify Installation**
- Frontend: http://localhost:5173
- Backend: http://localhost:3001
- Create a test account and run through the workflow

## 📋 Development Guidelines

### Code Style
- Use ESLint and Prettier configurations
- Follow existing naming conventions
- Write descriptive commit messages
- Add comments for complex logic

### Commit Messages
Use conventional commit format:
```
type(scope): description

feat(auth): add OAuth login support
fix(api): resolve user session timeout
docs(readme): update installation instructions
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

### Branch Naming
- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring

### Pull Request Process

1. **Before Starting**
   - Check existing issues and PRs
   - Create an issue for discussion if needed
   - Fork the repository

2. **Development**
   - Create a feature branch
   - Write clean, documented code
   - Add tests for new functionality
   - Ensure all tests pass

3. **Submission**
   - Push to your fork
   - Create a pull request
   - Fill out the PR template
   - Be responsive to feedback

### Testing
- Write unit tests for new functions
- Test UI components with user interactions
- Verify API endpoints work correctly
- Test across different browsers (if UI changes)

## 🏗️ Architecture Overview

### Frontend Structure
```
ai-article-generator/src/
├── components/           # React components
│   ├── tasks/           # Task management
│   ├── auth/            # Authentication
│   └── admin/           # Admin features
├── contexts/            # React contexts
├── config/              # Configuration
└── styles/              # CSS styles
```

### Backend Structure
```
backend/
├── routes/              # Express routes
├── services/            # Business logic
├── middleware/          # Express middleware
├── config/              # Configuration
└── migrations/          # Database migrations
```

### Key Components

#### Frontend
- **TaskEditor**: Main workflow component
- **StepX Components**: Individual workflow steps
- **AuthContext**: User authentication state
- **API Configuration**: Backend communication

#### Backend
- **aiServiceManager**: AI service routing and management
- **Authentication**: JWT-based user auth
- **Database**: PostgreSQL with SQLite fallback
- **API Routes**: RESTful endpoints

## 🧪 Testing Guidelines

### Frontend Testing
```bash
cd ai-article-generator
npm test
```

### Backend Testing
```bash
cd backend
npm test
```

### Manual Testing Checklist
- [ ] User registration and login
- [ ] All 7 workflow steps
- [ ] Article generation with different AI services
- [ ] Task saving and loading
- [ ] Admin functionality (if applicable)

## 📦 Adding New Features

### New AI Service Integration

1. **Create Service File**
```javascript
// backend/services/newAIService.js
class NewAIService {
  async generateArticle(data) {
    // Implementation
  }
  
  async generateTopics(keywords) {
    // Implementation
  }
  
  async keywordResearch(keyword) {
    // Implementation
  }
}
```

2. **Register Service**
```javascript
// backend/services/aiServiceManager.js
import NewAIService from './newAIService.js';

// Add to service map
const services = {
  // existing services...
  'newai': new NewAIService()
};
```

3. **Update Configuration**
```javascript
// Add environment variables
// Update admin configuration
// Add service selection UI
```

### New Workflow Step

1. **Create Component**
```jsx
// ai-article-generator/src/components/StepXNewFeature.jsx
const StepXNewFeature = ({ data, updateData, onNext, onPrev }) => {
  // Implementation
};
```

2. **Register in TaskEditor**
```javascript
// Add to STEPS array in TaskEditor.jsx
const STEPS = [
  // existing steps...
  { 
    id: X, 
    title: 'New Feature', 
    subtitle: 'Description', 
    component: StepXNewFeature, 
    icon: '🆕' 
  }
];
```

3. **Update Data Flow**
```javascript
// Update database schema if needed
// Update API endpoints
// Update data validation
```

## 🚦 Release Process

### Version Numbering
- Major: Breaking changes (v2.0.0)
- Minor: New features (v1.1.0)
- Patch: Bug fixes (v1.0.1)

### Release Checklist
- [ ] Update version numbers
- [ ] Update CHANGELOG.md
- [ ] Test all functionality
- [ ] Update documentation
- [ ] Create release notes
- [ ] Tag release in Git

## 🆘 Getting Help

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Email**: [contact information if available]

### Common Issues
- **Database Connection**: Check PostgreSQL service and credentials
- **AI API Errors**: Verify API keys and rate limits
- **Build Failures**: Clear node_modules and reinstall dependencies

## 📄 Code of Conduct

### Our Standards
- Be respectful and inclusive
- Provide constructive feedback
- Focus on the code, not the person
- Help others learn and grow

### Unacceptable Behavior
- Harassment or discrimination
- Trolling or insulting comments
- Spam or off-topic discussions
- Sharing others' private information

## 🙏 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- GitHub contributor graphs

Thank you for contributing to Writer J! 🚀