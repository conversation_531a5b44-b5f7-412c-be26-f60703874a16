{"version": 2, "buildCommand": "cd ai-article-generator && rm -rf node_modules package-lock.json && npm install && npm run build", "outputDirectory": "ai-article-generator/dist", "installCommand": "cd ai-article-generator && rm -rf node_modules package-lock.json && npm install", "framework": null, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}