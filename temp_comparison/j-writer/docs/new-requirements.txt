Product Requirements Document Addendum: Timely Context Integration
Version: 1.3 (Continuing from PRD v1.2)
Date: June 1, 2025
Author: [Your Name/Team]
Status: Draft
Feature Focus: This document details requirements for the new "Timely Context Integration" feature, which will be Step 3 in the article generation workflow.

1. Feature Overview
Feature Name: Timely Context Integration
Placement in Workflow: Step 3 (Following "Step 2: Article Topic Ideation" and preceding "Step 4: Source Integration")

Goal: To enable users to enrich their chosen article topic with fresh, relevant information from recent news, search trends, and active social discussions. This feature aims to enhance the timeliness, relevance, and potential engagement of the generated articles by providing the AI with current contextual information.

Brief Description: After a user finalizes their article topic and core keywords, this step will automatically fetch and display recent news articles, Google Trends insights, and popular Reddit discussions related to those keywords. The user can then review and select the most pertinent items. These selected timely items will be passed as additional context to the AI for the article generation phase (Step 7).

2. User Stories
As a content creator, I want the system to automatically find recent news and trending topics related to my chosen article subject, so that I can make my content more current and engaging without extensive manual research.

As an SEO specialist, I want to incorporate timely information into articles, so that I can improve content freshness signals and potentially boost search rankings for relevant trends.

As a user, I want to be able to review a list of fetched timely items and select only the ones that best fit my article's specific angle, so that I have control over the contextual information provided to the AI.

As a user, I want the AI to intelligently use the selected timely context, so that it enhances the article naturally (e.g., as an introduction hook, supporting example, or to frame a discussion).

3. Functional Requirements
3.1. Inputs to Step 3
1.  The primary input for this step will be the `Selected Article Topic` (string) and the associated `Core Keywords` (array of strings) finalized by the user in "Step 2: Article Topic Ideation."

3.2. Data Retrieval (Automated Backend Process)
1.  Upon the user entering Step 3, the backend system shall automatically initiate parallel API calls to retrieve timely information based on the `Core Keywords`.
2.  **News Retrieval:**
    * The system shall query one or more configured News APIs (e.g., Google News API, NewsAPI.org, or similar).
    * The query shall use the `Core Keywords`.
    * Results should be filtered for articles published within a configurable recent timeframe (default: last 30 days, configurable by admin).
    * The system shall retrieve a configurable maximum number of news items (default: top 5-10 relevant articles).
    * For each news item, the system must fetch: `Article Title`, `Source Name` (e.g., "Reuters"), `Publication Date`, a `Brief Snippet/Summary`, and the `Source URL`.
3.  **Google Trends Retrieval (V1 Focus: Rising Queries):**
    * The system shall query a Google Trends API or a reliable library wrapper (e.g., `google-trends-api` for Node.js, or similar for other backend languages).
    * The query shall use the primary `Core Keyword`.
    * The system shall attempt to retrieve "Rising" related search queries.
    * For each rising query, the system must fetch: `Query Text` and its `Percentage Increase` (if available).
    * The system shall retrieve a configurable maximum number of rising queries (default: top 5).
    * *Note: Full trend graph visualization is out of scope for V1; focus is on textual data.*
4.  **Reddit Discussion Retrieval:**
    * The system shall query the Reddit API.
    * The query shall use the `Core Keywords` to search for posts across relevant, pre-defined (or dynamically identified) subreddits.
    * Results should be filtered for posts created/highly active within a configurable recent timeframe (default: last 30 days).
    * The system shall sort results by relevance or "hotness."
    * The system shall retrieve a configurable maximum number of Reddit posts (default: top 5-10 relevant posts).
    * For each Reddit post, the system must fetch: `Post Title`, `Subreddit Name` (e.g., r/technology), `Score/Upvotes`, `Number of Comments`, and the `Post URL`.
5.  **Error Handling:** If any API call fails or returns no relevant data, the system should gracefully handle the error (e.g., display "No recent news found" for that section) without halting the entire step.
6.  **Data Deduplication (Basic):** The system should make a best effort to avoid displaying highly duplicative items (e.g., the same news story syndicated across multiple sources, if identifiable).

3.3. User Interface (UI) - Presentation & Selection
1.  The UI for Step 3 shall clearly display the fetched timely information in distinct, labeled, and potentially collapsible sections:
    * "Recent News"
    * "Trending Topics (from Google Trends)"
    * "Reddit Buzz"
2.  Each retrieved item within these sections must be presented concisely and clearly.
    * **News Item Display:** `[Checkbox] Source Name: Article Title (Publication Date) - Snippet... [Link to original article]`
    * **Google Trend Display:** `[Checkbox] Rising Query: Query Text (+X% increase)`
    * **Reddit Post Display:** `[Checkbox] r/SubredditName: Post Title (Upvotes: Y, Comments: Z) [Link to Reddit post]`
3.  Each item must have a `Checkbox` allowing the user to select it for inclusion as context.
4.  A "Select All" / "Deselect All" option should be available for each section (News, Trends, Reddit).
5.  The UI should indicate if no results were found for a particular section.
6.  The UI must provide clear "Next" (to proceed to Step 4) and "Back" (to return to Step 2) navigation buttons.

3.4. Outputs from Step 3
1.  The primary output of this step will be a structured list of `Selected Timely Context Items`.
2.  Each item in this list should contain:
    * `type`: (e.g., "news", "google_trend", "reddit_post")
    * `title`: (The title of the news article, trend query, or Reddit post)
    * `source_name`: (e.g., "Reuters", "Google Trends", "r/technology")
    * `content_snippet_or_data`: (News snippet, trend percentage, or Reddit post title/key info)
    * `url`: (The direct URL to the source item)
    * `date_retrieved_or_published`: (Relevant date)
3.  This list of selected items will be saved as part of the overall "Article Task" data and passed to "Step 7: AI Article Generation" as additional context.
4.  If the user selects no items, an empty list will be passed.

4. UI/UX Considerations
Performance: The data retrieval process should be optimized to minimize loading time for the user when they enter this step. Consider asynchronous loading of data for each section if necessary.

Clarity: Information should be presented in an easily scannable and understandable format. Avoid overwhelming the user with too much text per item initially; provide links for more details.

Responsiveness: The layout must be responsive and usable across different screen sizes.

User Control: Users should feel in control of the selection process. The impact of their selections on the final article should be implicitly understood (i.e., these items will help make the article more current).

Feedback: Provide clear visual feedback when items are selected/deselected.

5. Technical Considerations & Dependencies
API Integrations:

Reliable News API (e.g., NewsAPI.org, GNews API, or equivalent). Requires API key management and handling of usage limits/costs.

Google Trends access (via official API if available, or through well-maintained unofficial libraries. Be mindful of Google's ToS and potential instability of unofficial methods).

Reddit API (requires app registration, OAuth or app-only authentication, and adherence to API usage guidelines).

Backend Logic:

Orchestration of multiple API calls.

Parsing and normalizing data from different API responses.

Error handling and retry mechanisms for API calls.

Caching strategies for API responses (especially for trends or news that don't change minute-by-minute) to manage API rate limits and improve performance.

Security: Secure storage and handling of API keys.

Scalability: Design API interactions to be scalable if user volume increases.

6. Out of Scope for V1 (Future Enhancements)
Advanced filtering within fetched results (e.g., filter news by specific source, filter Reddit by specific subreddits not predefined).

User-configurable date ranges for data retrieval.

Sentiment analysis on Reddit posts or news items.

Visualizations for Google Trends data (e.g., mini line graphs).

User ability to add custom timely URLs not automatically fetched.

AI-powered summarization of selected timely items before sending to the main generation AI.

This document outlines the core requirements for the "Timely Context Integration" feature. It should serve as a foundation for design, development, and testing. Further details and acceptance criteria will be defined during sprint planning.